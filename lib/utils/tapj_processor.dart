import 'dart:io';

import 'package:turing_art/core/tapj/n8_tapj_file_manager.dart';
import 'package:turing_art/core/tapj/n8_tapj_models.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/utils/pg_log.dart';

/// Tapj文件处理器工具类
/// 提供可复用的tapj文件处理功能，用于文件选择器、拖拽等场景
class TapjProcessor {
  /// 验证tapj文件格式（检查魔数）
  static bool validateTapjFile(String tapjFilePath) {
    try {
      final file = File(tapjFilePath);
      if (!file.existsSync()) {
        PGLog.w('tapj文件不存在: $tapjFilePath');
        return false;
      }

      // 读取文件头部验证魔数
      final bytes = file.readAsBytesSync();
      if (bytes.length < 4) {
        PGLog.w('tapj文件太小，无法验证魔数: $tapjFilePath');
        return false;
      }

      // 验证魔数是否为 "TAPJ"
      final magicBytes = bytes.sublist(0, 4);
      final magicString = String.fromCharCodes(magicBytes);
      if (magicString != N8TapjFileManager.tapjMagicString) {
        PGLog.w('无效的tapj文件格式，魔数不匹配: $tapjFilePath, 实际魔数: $magicString');
        return false;
      }

      PGLog.d('tapj文件魔数验证通过: $tapjFilePath');
      return true;
    } catch (e) {
      PGLog.e('验证tapj文件时出错: $tapjFilePath, 错误: $e');
      return false;
    }
  }

  /// 读取tapj文件内容
  static Future<N8TapjReadResult?> readTapjFile(String tapjFilePath) async {
    try {
      if (!validateTapjFile(tapjFilePath)) {
        return null;
      }

      return await N8TapjFileManager.readN8TapjFile(tapjFilePath);
    } catch (e) {
      PGLog.e('读取tapj文件失败: $tapjFilePath, 错误: $e');
      return null;
    }
  }

  /// 将tapj文件处理为图片文件结果
  /// 适用于文件选择器场景
  static Future<DealImageFilesResult?> processTapjForImagePicker(
      String tapjFilePath) async {
    try {
      // 创建完整的导入项目数据
      final tapjImportResult = await createImportProjectData(
        tapjFilePath,
        newProject: true,
        autoNavigate: true,
      );

      if (tapjImportResult == null) {
        PGLog.w('无法创建tapj导入数据: $tapjFilePath');
        return null;
      }

      // 提取有效的图片文件路径
      final validFiles = <File>[];
      for (final fileInfo in tapjImportResult.importData.fileList) {
        final file = File(fileInfo.originalPath);
        if (await file.exists()) {
          validFiles.add(file);
          PGLog.d(
              '找到有效图片文件: ${fileInfo.originalPath}, 选中状态: ${fileInfo.isSelected}');
        } else {
          PGLog.w('图片文件不存在: ${fileInfo.originalPath}');
        }
      }

      if (validFiles.isEmpty) {
        PGLog.w('tapj文件中没有找到有效的图片文件');
        return null;
      }

      final projectName =
          tapjImportResult.importData.projectName?.isNotEmpty == true
              ? tapjImportResult.importData.projectName!
              : _getProjectNameFromTapjPath(tapjFilePath);

      PGLog.d('从tapj文件处理得到 ${validFiles.length} 个有效图片文件，项目名称: $projectName');

      // 返回包含tapj导入数据的结果
      return DealImageFilesResult.fromTapj(
          validFiles, projectName, tapjImportResult);
    } catch (e) {
      PGLog.e('处理tapj文件失败: $tapjFilePath, 错误: $e');
      return null;
    }
  }

  /// 创建项目导入数据
  /// 将tapj文件转换为ImportProjectData，用于完整的项目导入流程
  static Future<TapjImportResult?> createImportProjectData(
    String tapjFilePath, {
    bool newProject = true,
    bool autoNavigate = true,
  }) async {
    try {
      final result = await readTapjFile(tapjFilePath);
      if (result == null) {
        PGLog.w('无法读取tapj文件: $tapjFilePath');
        return null;
      }

      // 将N8ExportFileInfo转换为FileItem，并合并描述文件信息
      final fileList = mergeFileInfoWithDescription(
        result.projectData.fileList,
        result.descriptionFiles,
      );

      final importData = ImportProjectData(
        projectId: result.projectData.rawWorkspaceId,
        projectName: result.projectData.workspaceName,
        fileList: fileList,
        autoNavigate: autoNavigate,
        newProject: newProject,
      );

      PGLog.d(
          '从tapj文件创建ImportProjectData: 项目=${importData.projectName}, 文件数=${importData.fileList.length}');

      // 如果有描述文件信息，记录日志
      if (result.descriptionFiles != null) {
        PGLog.d('合并了描述文件信息，包含 ${result.descriptionFiles!.length} 个文件描述');
      }

      return TapjImportResult(
        importData: importData,
        historyFiles: result.historyFiles,
        descriptionFiles: result.descriptionFiles,
      );
    } catch (e) {
      PGLog.e('创建ImportProjectData失败: $tapjFilePath, 错误: $e');
      return null;
    }
  }

  /// 合并tapj文件信息和描述文件信息
  ///
  /// [tapjFileList] tapj文件中的文件列表
  /// [descriptionFiles] 描述文件中的文件列表（可能为null）
  /// 返回合并后的FileItem列表
  ///
  /// 注意：以tapj文件为准，描述文件仅提供isSelected信息
  static List<FileItem> mergeFileInfoWithDescription(
    List<N8ExportFileInfo> tapjFileList,
    List<N8SelectedFileInfo>? descriptionFiles,
  ) {
    // 如果没有描述文件，直接转换tapj文件信息
    if (descriptionFiles == null || descriptionFiles.isEmpty) {
      return tapjFileList.map((fileInfo) {
        return FileItem(
          originalPath: fileInfo.originalPath,
          historyId: fileInfo.historyId != 'org' ? fileInfo.historyId : null,
          isSelected: fileInfo.isSelected,
        );
      }).toList();
    }

    // 创建描述文件的映射表，以originalPath为key
    final Map<String, N8SelectedFileInfo> descriptionMap = {};
    for (final desc in descriptionFiles) {
      descriptionMap[desc.originalPath] = desc;
    }

    // 以tapj文件为准，合并描述文件的isSelected信息
    final List<FileItem> mergedList = [];

    for (final tapjFile in tapjFileList) {
      final description = descriptionMap[tapjFile.originalPath];

      // 优先使用描述文件中的isSelected状态，如果描述文件中没有对应文件则使用tapj文件中的状态
      final isSelected = description?.isSelected ?? tapjFile.isSelected;

      mergedList.add(FileItem(
        originalPath: tapjFile.originalPath,
        historyId: tapjFile.historyId != 'org' ? tapjFile.historyId : null,
        isSelected: isSelected,
      ));
    }

    PGLog.d(
        '文件信息合并完成: tapj文件${tapjFileList.length}个, 描述文件${descriptionFiles.length}个, 最终输出${mergedList.length}个');

    return mergedList;
  }

  /// 从tapj文件路径提取项目名称
  static String _getProjectNameFromTapjPath(String tapjFilePath) {
    final fileName = tapjFilePath.split(RegExp(r'[/\\]')).last;
    return fileName.replaceAll(RegExp(r'\.[^.]*$'), ''); // 移除扩展名
  }

  /// 检查文件是否为tapj格式
  static bool isTapjFile(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    return N8TapjFileManager.supportedTapjExtensions.contains(extension);
  }

  /// 获取tapj文件的项目信息（不读取完整内容）
  static Future<String?> getTapjProjectName(String tapjFilePath) async {
    try {
      final result = await readTapjFile(tapjFilePath);
      if (result == null) {
        return null;
      }

      return result.projectData.workspaceName.isNotEmpty
          ? result.projectData.workspaceName
          : _getProjectNameFromTapjPath(tapjFilePath);
    } catch (e) {
      PGLog.e('获取tapj项目名称失败: $tapjFilePath, 错误: $e');
      return _getProjectNameFromTapjPath(tapjFilePath);
    }
  }
}

/// Tapj导入结果类
/// 包含ImportProjectData和历史文件数据
class TapjImportResult {
  final ImportProjectData importData;
  final Map<String, List<int>> historyFiles;
  final List<N8SelectedFileInfo>? descriptionFiles;

  TapjImportResult({
    required this.importData,
    required this.historyFiles,
    this.descriptionFiles,
  });
}
