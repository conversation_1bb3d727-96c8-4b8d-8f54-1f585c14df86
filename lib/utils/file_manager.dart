import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import 'app_constants.dart';
import 'pg_log.dart';

enum FolderType {
  projects,
  workspace,
  input,
  output,
  presetOutput,
  temp,
  log,
  updateDownload,
  db,
  aigc,
  imageCache,

  // 照片内部文件夹
  data,
  export,
  history,
  resource,
  thumb,
  interactiveMask
}

class FileManager {
  // 单例实例
  static final FileManager _instance = FileManager._internal();

  // 工厂构造函数
  factory FileManager() => _instance;

  // 私有构造函数
  FileManager._internal();

  // 私有字段
  late final Directory _rootDir;
  late final Directory _dbDir;
  late final Directory _imageCacheDir;
  Directory? _userRootDir;
  Directory? _updateDownloadDir;
  Directory? _currentLogDir;
  bool _initialized = false;
  Future<void>? _initFuture;

  /// 初始化
  Future<void> _initialize() async {
    if (_initialized) {
      return;
    }

    // 如果已经在初始化中，返回同一个 Future
    if (_initFuture != null) {
      await _initFuture;
      return;
    }

    // 创建新的初始化 Future
    _initFuture = _doInitialize();
    try {
      await _initFuture;
    } finally {
      _initFuture = null;
    }
  }

  /// 执行实际的初始化
  Future<void> _doInitialize() async {
    if (_initialized) {
      return;
    }

    try {
      final appDir = await _getAppDirectory();

      // 初始化基础目录
      _rootDir = await _ensureDirectory(
          path.join(appDir.path, FolderType.projects.name));
      _dbDir =
          await _ensureDirectory(path.join(appDir.path, FolderType.db.name));
      _imageCacheDir = await _ensureDirectory(
          path.join(appDir.path, FolderType.imageCache.name));

      _initialized = true;
    } catch (e) {
      throw Exception('Failed to initialize FileManager: $e');
    }
  }

  /// 获取应用目录
  Future<Directory> _getAppDirectory() async {
    if (AppConstants.isDesktop) {
      return await getApplicationSupportDirectory();
    }
    return await getApplicationDocumentsDirectory();
  }

  /// 确保目录存在
  Future<Directory> _ensureDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (!directory.existsSync()) {
      await directory.create(recursive: true);
    }
    return directory;
  }

  /// 确保初始化完成
  Future<void> ensureInitialized() async {
    if (!_initialized) {
      await _initialize();
    }
  }

  Future<Directory> get appDir async {
    if (AppConstants.isDesktop) {
      return await getApplicationSupportDirectory();
    } else {
      return await getApplicationDocumentsDirectory();
    }
  }

  /// 用户根目录
  Directory? get userRootDir => _userRootDir;

  Directory get rootDir => _rootDir;

  /// 用户更新下载目录
  Directory? get updateDownloadDir => _updateDownloadDir;

  Directory get dbDir => _dbDir;

  String get dbPath =>
      Directory(path.join(_dbDir.path, "turing_art.sqlite")).path;

  /// 获取图片缓存目录
  Directory get imageCacheDir => _imageCacheDir;

  /// 创建用户根目录
  Future<Directory> createUserRootDir(String userId) async {
    await ensureInitialized();
    final userDir = Directory(path.join(_rootDir.path, userId));
    if (!userDir.existsSync()) {
      userDir.createSync(recursive: true);
    }
    _userRootDir = userDir;
    _generateTestPresetJson();
    return userDir;
  }

  /// 创建更新下载的目录
  Future<Directory> createUpdateDownloadDir() async {
    try {
      final appDir = await this.appDir;
      final updateDownloadPath =
          path.join(appDir.path, FolderType.updateDownload.name);
      // 确保目录存在
      await Directory(updateDownloadPath).create(recursive: true);
      return Directory(updateDownloadPath);
    } catch (e) {
      final userRootDir = getUserRootDir();
      if (userRootDir == null) {
        throw Exception('User root directory not found');
      }
      // 如果获取失败，使用一个备用路径
      return Directory(
          path.join(userRootDir.path, FolderType.updateDownload.name));
    }
  }

  /// 获取图片缓存目录
  Future<Directory> getImageCacheDirectory() async {
    if (!_initialized) {
      await _initialize();
    }
    return _imageCacheDir;
  }

  /// 获取用户根目录
  Directory? getUserRootDir() {
    return _userRootDir;
  }

  /// 登出
  void logout() {
    _userRootDir = null;
  }

  /// 创建项目文件夹
  Future<Directory> createProjectDirectory(String projectId) async {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }

    final projectDir = Directory(path.join(userRootDir.path, projectId));
    if (!projectDir.existsSync()) {
      projectDir.createSync(recursive: true);
      // 创建子文件夹
      await Future.wait([
        Directory(path.join(projectDir.path, FolderType.workspace.name))
            .create(),
        Directory(path.join(projectDir.path, FolderType.input.name)).create(),
        Directory(path.join(projectDir.path, FolderType.output.name)).create(),
        Directory(path.join(projectDir.path, FolderType.temp.name)).create(),
      ]);
    }
    return projectDir;
  }

  /// 创建项目文件夹
  Future<Directory> createPhotoDirectory(
      String projectId, String photoId) async {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }

    final photoDir = Directory(path.join(userRootDir.path, projectId, photoId));
    if (!photoDir.existsSync()) {
      photoDir.createSync(recursive: true);
      // 创建子文件夹
      await Future.wait([
        Directory(path.join(photoDir.path, FolderType.data.name)).create(),
        Directory(path.join(photoDir.path, FolderType.export.name)).create(),
        Directory(path.join(photoDir.path, FolderType.history.name)).create(),
        Directory(path.join(photoDir.path, FolderType.resource.name)).create(),
        Directory(path.join(photoDir.path, FolderType.thumb.name)).create(),
      ]);
    }
    return photoDir;
  }

  /// 删除项目文件夹
  Future<void> deleteProjectDirectory(String projectId) async {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    final projectDir = Directory(path.join(userRootDir.path, projectId));
    if (projectDir.existsSync()) {
      projectDir.deleteSync(recursive: true);
    }
  }

  /// 获取项目文件夹
  Directory getProjectDirectory(String projectId) {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    return Directory(path.join(userRootDir.path, projectId));
  }

  /// 获取工作区文件夹
  Directory getWorkspaceDirectory(String projectId) {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    return Directory(
        path.join(userRootDir.path, projectId, FolderType.workspace.name));
  }

  /// 获取输入文件夹
  Directory getInputDirectory(String projectId) {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    return Directory(
        path.join(userRootDir.path, projectId, FolderType.input.name));
  }

  /// 获取导出文件夹
  Directory getExportDirectory(String projectId) {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    return Directory(
        path.join(userRootDir.path, projectId, FolderType.output.name));
  }

  /// 获取预设输出文件夹
  Future<Directory> getPresetOutputDirectory() async {
    try {
      final appDir = await this.appDir;
      final presetOutputPath =
          path.join(appDir.path, FolderType.presetOutput.name);
      // 确保目录存在
      await Directory(presetOutputPath).create(recursive: true);
      return Directory(presetOutputPath);
    } catch (e) {
      final userRootDir = getUserRootDir();
      if (userRootDir == null) {
        throw Exception('User root directory not found');
      }
      // 如果获取失败，使用一个备用路径
      return Directory(
          path.join(userRootDir.path, FolderType.presetOutput.name));
    }
  }

  /// 获取当前日志目录
  Future<Directory> _getCurrentLogDirectory() async {
    if (_currentLogDir != null) {
      return _currentLogDir!;
    }

    final appDir = await this.appDir;
    // 创建日志文件夹
    final logDir = Directory(path.join(appDir.path, FolderType.log.name));
    if (!logDir.existsSync()) {
      logDir.createSync(recursive: true);
    }

    // 生成当前启动的日志文件夹名（年月日时分秒）
    final now = DateTime.now();
    final folderName =
        '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}${now.second.toString().padLeft(2, '0')}';

    final currentLogDir = Directory(path.join(logDir.path, folderName));
    if (!currentLogDir.existsSync()) {
      currentLogDir.createSync(recursive: true);
    }

    _currentLogDir = currentLogDir;
    return currentLogDir;
  }

  /// 获取 Unity 日志文件路径
  Future<String> getUnityLogFilePath() async {
    final logDir = await _getCurrentLogDirectory();
    final unityLogFile = File(path.join(logDir.path, 'unity.log'));

    // 确保文件存在
    if (!unityLogFile.existsSync()) {
      unityLogFile.createSync(recursive: true);
    }

    return unityLogFile.path;
  }

  /// 获取 Flutter 日志文件路径
  Future<String> getFlutterLogFilePath() async {
    final logDir = await _getCurrentLogDirectory();
    final flutterLogFile = File(path.join(logDir.path, 'flutter.log'));

    // 确保文件存在
    if (!flutterLogFile.existsSync()) {
      flutterLogFile.createSync(recursive: true);
    }

    return flutterLogFile.path;
  }

  /// 获取aigc文件夹
  Directory getAigcDirectory({required bool isPresets}) {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    final folderName = isPresets ? "aigcPresets" : "aigcSamples";
    final aigcDir = Directory(
        path.join(userRootDir.path, FolderType.aigc.name, folderName));
    if (!aigcDir.existsSync()) {
      aigcDir.createSync(recursive: true);
    }
    return aigcDir;
  }

  /// 获取aigc抠图临时文件夹
  Directory getAigcMattingDirectory({required String projectId}) {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    final aigcDir = Directory(path.join(
        userRootDir.path, FolderType.aigc.name, "matting", projectId));
    if (!aigcDir.existsSync()) {
      aigcDir.createSync(recursive: true);
    }
    return aigcDir;
  }

  /// 获取临时文件夹
  Directory getTempDirectory(String projectId, String fileId) {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    final tempDir = Directory(
        path.join(userRootDir.path, projectId, fileId, FolderType.temp.name));
    if (!tempDir.existsSync()) {
      tempDir.createSync(recursive: true);
    }
    return tempDir;
  }

  /// 获取临时蒙版文件夹
  Directory getTempInteractiveMaskDirectory(String projectId, String fileId) {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    final uuid = const Uuid().v4();
    final tempDir = Directory(path.join(
      userRootDir.path,
      projectId,
      fileId,
      FolderType.interactiveMask.name,
      uuid,
    ));
    if (!tempDir.existsSync()) {
      tempDir.createSync(recursive: true);
    }
    return tempDir;
  }

  /// 复制文件到项目工作区
  Future<File> copyFileToWorkspace(String projectId, File sourceFile) async {
    final workspaceDir = getWorkspaceDirectory(projectId);
    final fileName = path.basename(sourceFile.path);
    final targetFile = File(path.join(workspaceDir.path, fileName));

    return await sourceFile.copy(targetFile.path);
  }

  /// 复制文件到项目输入文件夹
  Future<File> copyFileToInput(String projectId, File sourceFile) async {
    final inputDir = getInputDirectory(projectId);
    final fileName = path.basename(sourceFile.path);
    final targetFile = File(path.join(inputDir.path, fileName));

    return await sourceFile.copy(targetFile.path);
  }

  /// 清理临时文件夹
  Future<void> cleanTempDirectory(String projectId) async {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    final tempDir =
        Directory(path.join(userRootDir.path, projectId, FolderType.temp.name));
    if (tempDir.existsSync()) {
      tempDir.deleteSync(recursive: true);
    }
  }

  /// 获取项目文件大小（字节）
  Future<int> getProjectSize(String projectId) async {
    final projectDir = getProjectDirectory(projectId);
    if (!projectDir.existsSync()) {
      return 0;
    }

    int size = 0;
    await for (final entity in projectDir.list(recursive: true)) {
      if (entity is File) {
        size += await entity.length();
      }
    }
    return size;
  }

  /// 检查项目是否存在
  Future<bool> projectExists(String projectId) async {
    final projectDir = getProjectDirectory(projectId);
    return projectDir.existsSync();
  }

  /// 获取所有项目ID列表
  Future<List<String>> getAllProjectIds() async {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }
    if (!userRootDir.existsSync()) {
      return [];
    }

    final List<String> projectIds = [];
    await for (final entity in userRootDir.list()) {
      if (entity is Directory) {
        projectIds.add(path.basename(entity.path));
      }
    }
    return projectIds;
  }

  /// 导出文件
  Future<File> exportFile(
      String projectId, File sourceFile, String fileName) async {
    final exportDir = getExportDirectory(projectId);
    final targetFile = File(path.join(exportDir.path, fileName));

    return await sourceFile.copy(targetFile.path);
  }

  /// 获取相对路径（相对于 documents 目录）
  String getRelativePath(String fullPath) {
    // 如果有沙盒路径，才返回相对路径
    if (AppConstants.isContainSandox) {
      final userRootDir = getUserRootDir();
      if (userRootDir == null) {
        throw Exception('User root directory not found');
      }
      return path.relative(fullPath, from: userRootDir.parent.path);
    } else {
      return fullPath;
    }
  }

  /// 获取完整路径
  String getFullPath(String relativePath) {
    // 如果有沙盒路径，才返回完整路径
    if (AppConstants.isContainSandox) {
      final userRootDir = getUserRootDir();
      if (userRootDir == null) {
        throw Exception('User root directory not found');
      }
      return path.join(userRootDir.parent.path, relativePath);
    } else {
      return relativePath;
    }
  }

  /// 复制文件到项目输入文件夹并返回相对路径
  Future<String> copyFileToInputAndGetRelativePath(
      String projectId, File sourceFile) async {
    final inputDir = getInputDirectory(projectId);
    final fileName = path.basename(sourceFile.path);
    final targetFile = File(path.join(inputDir.path, fileName));

    await sourceFile.copy(targetFile.path);
    return getRelativePath(targetFile.path);
  }

  /// 复制文件到工作区并返回相对路径
  Future<String> copyFileToWorkspaceAndGetRelativePath(
      String projectId, File sourceFile) async {
    final workspaceDir = getWorkspaceDirectory(projectId);
    final fileName = path.basename(sourceFile.path);
    final targetFile = File(path.join(workspaceDir.path, fileName));

    await sourceFile.copy(targetFile.path);
    return getRelativePath(targetFile.path);
  }

  /// 检查文件是否存在（使用相对路径）
  Future<bool> fileExists(String relativePath) async {
    final fullPath = getFullPath(relativePath);
    return File(fullPath).existsSync();
  }

  /// 读取文件（使用相对路径）
  Future<File> getFile(String relativePath) async {
    final fullPath = getFullPath(relativePath);
    return File(fullPath);
  }

  /// 生成测试用的 /presets/list.json 文件
  Future<void> _generateTestPresetJson() async {
    final userRootDir = getUserRootDir();
    if (userRootDir == null) {
      throw Exception('User root directory not found');
    }

    final presetJsonPath = '${userRootDir.path}/presets/list.json';
    final presetJsonFile = File(presetJsonPath);

    // 确保目录存在
    await presetJsonFile.parent.create(recursive: true);

    // 写入测试数据
    const jsonData =
        '[{"title":"新预设 175240","createUser":"默认用户","fileID":"ea960568-50c1-44c6-b8cc-e5a0d0d607c9","createTime":"2025-01-17 17:52","updateTime":"2025-01-17 17:52:43","presetType":128,"presetCategory":"未分类"},{"title":"新预设 172806","createUser":"默认用户","fileID":"b4e1cabf-59df-4c0a-aca0-09db2ff6e5f3","createTime":"2025-01-17 17:28","updateTime":"2025-01-17 17:28:08","presetType":128,"presetCategory":"未分类"},{"title":"2222","createUser":"默认用户","fileID":"3d660799-79db-4f98-8657-8e5339e303fa","createTime":"2025-01-17 15:43","updateTime":"2025-01-17 15:43:23","presetType":128,"presetCategory":"未分类"},{"title":"5555","createUser":"默认用户","fileID":"78060fb7-f581-4c3a-a98d-461b3602ad3f","createTime":"2025-01-17 17:27","updateTime":"2025-01-17 17:27:53","presetType":128,"presetCategory":"未分类"},{"title":"AAAA","createUser":"默认用户","fileID":"08c9b9a5-9015-4ea7-98a9-a015c122a071","createTime":"2025-01-17 14:42","updateTime":"2025-01-17 14:42:50","presetType":128,"presetCategory":"未分类"},{"title":"人像2","createUser":"默认用户","fileID":"8b16c09f-5487-48da-baff-95ebe3241ede","createTime":"2025-01-17 14:25","updateTime":"2025-01-17 14:25:37","presetType":128,"presetCategory":"未分类"}]';

    await presetJsonFile.writeAsString(jsonData);
  }

  /// 创建目录（根据文件路径创建目录）
  /// [filePath] 文件路径，会从中提取目录路径并创建
  Future<void> createDirectory(String filePath) async {
    try {
      final file = File(filePath);
      final directory = file.parent;

      if (!directory.existsSync()) {
        await directory.create(recursive: true);
        PGLog.d('创建目录成功: ${directory.path}');
      }
    } catch (e) {
      PGLog.e('创建目录失败: $filePath, 错误: $e');
      rethrow;
    }
  }

  /// 删除指定路径的文件夹内容
  /// [path] 文件夹路径
  /// [keepDirectory] 是否保留文件夹本身，默认为true
  Future<bool> deleteDir(
      {required String path, bool keepDirectory = true}) async {
    try {
      final directory = Directory(path);

      // 检查目录是否存在
      if (!directory.existsSync()) {
        PGLog.d('目录不存在: $path');
        return true; // 目录不存在视为删除成功
      }

      // 列出目录中的所有文件和子目录
      final List<FileSystemEntity> entities =
          await directory.list(recursive: true).toList();

      // 先删除文件，再删除目录（从内到外）
      for (final entity in entities.reversed) {
        try {
          await entity.delete(recursive: false);
          PGLog.d('已删除: ${entity.path}');
        } catch (e) {
          PGLog.e('删除失败: ${entity.path}, 错误: $e');
          // 继续删除其他文件
        }
      }

      // 如果不保留目录本身，则删除目录
      if (!keepDirectory) {
        await directory.delete(recursive: false);
        PGLog.d('已删除目录: $path');
      } else {
        // 确保目录存在
        if (!directory.existsSync()) {
          directory.createSync(recursive: true);
          PGLog.d('重新创建目录: $path');
        }
      }

      return true;
    } catch (e) {
      PGLog.e('删除目录内容失败: $path, 错误: $e');
      return false;
    }
  }
}
