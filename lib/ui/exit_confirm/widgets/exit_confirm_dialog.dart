import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pg_desktop_multi_window/desktop_multi_window.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/windows_process_terminator.dart';

class ExitConfirmDialog extends StatefulWidget {
  const ExitConfirmDialog({super.key});

  @override
  State<ExitConfirmDialog> createState() => _ExitConfirmDialogState();

  static void show() async {
    if (PGDialog.isDialogVisible(DialogTags.exitConfirm)) {
      PGLog.d('ExitConfirmDialog show, but dialog already exist, return');
      return;
    }
    return PGDialog.showCustomDialogOnUnity(
      width: 345,
      height: 378,
      tag: DialogTags.exitConfirm,
      child: const ExitConfirmDialog(),
    );
  }
}

class _ExitConfirmDialogState extends State<ExitConfirmDialog> {
  bool _hoverdMini = false;
  bool _hoverdQuit = false;
  bool _hoverdCancel = false;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 345,
      height: 378,
      child: Container(
        padding: const EdgeInsets.only(left: 16, right: 16),
        decoration: const BoxDecoration(
          color: Color(0xFF121415),
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        child: Column(
          children: [
            const SizedBox(height: 30),
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.asset(
                'assets/icons/app_icon.png',
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '确定退出图灵精修吗？',
              style: TextStyle(
                fontSize: 16,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.semiBold,
                color: const Color(0xFFE1E2E5),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            PlatformMouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (event) => setState(() => _hoverdMini = true),
              onExit: (event) => setState(() => _hoverdMini = false),
              child: _buildButton('最小化', () async {
                // windows 窗口最小化
                PGDialog.dismiss(tag: DialogTags.exitConfirm);
                var windowId = await DesktopMultiWindow.getActiveWindowId();
                WindowController.fromWindowId(windowId ?? -1).minimizeWindow();
              },
                  _hoverdMini
                      ? const Color(0xFF28292C)
                      : const Color(0xFF1B1C1F)),
            ),
            const SizedBox(height: 12),
            PlatformMouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (event) => setState(() => _hoverdQuit = true),
              onExit: (event) => setState(() => _hoverdQuit = false),
              child: _buildButton('退出', () {
                // windows 窗口关闭
                PGDialog.dismiss(tag: DialogTags.exitConfirm);

                // 强制终止应用程序
                if (Platform.isWindows) {
                  // 在Windows上使用原生方法终止进程
                  try {
                    // 1. 先关闭所有子窗口
                    DesktopMultiWindow.getAllSubWindowIds().then((windowIds) {
                      for (final id in windowIds) {
                        WindowController.fromWindowId(id).close();
                      }

                      // 2. 使用Windows原生方法终止进程
                      bool terminated =
                          WindowsProcessTerminator.terminateCurrentProcess();

                      // 3. 如果原生方法失败，尝试其他方法
                      if (!terminated) {
                        SystemNavigator.pop(animated: true);

                        // 最后尝试强制退出
                        Future.delayed(const Duration(milliseconds: 500), () {
                          exit(0);
                        });
                      }
                    });
                  } catch (e) {
                    PGLog.d('退出应用时出错: $e');
                    // 出错时使用强制退出
                    exit(0);
                  }
                } else {
                  // 其他平台使用标准退出
                  SystemNavigator.pop();
                }
              },
                  _hoverdQuit
                      ? const Color(0xFF28292C)
                      : const Color(0xFF1B1C1F)),
            ),
            const SizedBox(height: 30),
            PlatformMouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (event) => setState(() => _hoverdCancel = true),
              onExit: (event) => setState(() => _hoverdCancel = false),
              child: _buildButton('取消', () {
                PGDialog.dismiss(tag: DialogTags.exitConfirm);
              },
                  _hoverdCancel
                      ? const Color(0xFF28292C)
                      : const Color(0xFF1B1C1F)),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildButton(String title, VoidCallback onTap, Color backgroundColor) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 265,
        height: 44,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontFamily: Fonts.defaultFontFamily,
              fontWeight: Fonts.medium,
              color: const Color(0xFFE1E2E5),
            ),
          ),
        ),
      ),
    );
  }
}
