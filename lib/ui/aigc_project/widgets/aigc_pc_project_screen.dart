import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:pg_turing_collect_event/collect/pay_action_log.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/account_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/new_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/reward_repository.dart';
import 'package:turing_art/datalayer/repository/wechat_gift_repository.dart';
import 'package:turing_art/ops/repository/ops_custom_table_repository.dart';
import 'package:turing_art/providers/account_rights_state_provider.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/workspace_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/routing/routes.dart';
import 'package:turing_art/ui/aigc_project/view_model/aigc_project_view_model.dart';
import 'package:turing_art/ui/common/debounce_click_widget/debounce_click_widget.dart';
import 'package:turing_art/ui/common/drag_event_handler/widget/route_aware_drag_handler_widget.dart';
import 'package:turing_art/ui/common/project_grid_view/project_grid_item.dart';
import 'package:turing_art/ui/common/project_grid_view/project_grid_view.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/ui/desktop/title_bar_widget.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/ui/dialog/delete_project_dialog.dart';
import 'package:turing_art/ui/dialog/project_rename_pc_dialog.dart';
import 'package:turing_art/ui/dialog/universal_dialog.dart';
import 'package:turing_art/ui/profile/use_case/auth_usecase_provider.dart';
import 'package:turing_art/ui/profile/view_model/profile_dialog_view_model.dart';
import 'package:turing_art/ui/project_home/services/profile_view_click_service.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_empty_state.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_profile_view.dart';
import 'package:turing_art/ui/ui_status/process_files_ui_status.dart';
import 'package:turing_art/ui/ui_status/select_project_error_type.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/use_case/project/project_usecase_provider.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC项目页面
class AigcPcProjectScreen extends StatefulWidget {
  const AigcPcProjectScreen({
    super.key,
    this.title = 'AigcPcProjectScreen',
  });

  final String title;

  @override
  State<AigcPcProjectScreen> createState() => _AigcPcProjectScreenState();
}

class _AigcPcProjectScreenState extends State<AigcPcProjectScreen> {
  late AigcProjectViewModel _viewModel;

  // 为AIGC页面创建独立的GlobalKey，避免与主页面冲突
  final GlobalKey _aigcUserCardKey = GlobalKey();
  final GlobalKey _aigcProfileViewKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) {
            _viewModel = AigcProjectViewModel(
              GoRouterNavigatorService(context),
              context.read<ProjectRepository>(),
              context.read<ProjectUseCaseProvider>(),
              context.read<UnityUseCaseProvider>(),
              context.read<WorkspaceUseCaseProvider>(),
              context.read<ProjectStateProvider>(),
              context.read<UnityController>(),
              context.read<CurrentUserRepository>(),
              context.read<MediaRepository>(),
            );
            return _viewModel;
          },
        ),
        ChangeNotifierProvider(
          create: (context) => ProfileDialogViewModel(
            context.read<CurrentUserRepository>(),
            context.read<AccountRepository>(),
            context.read<WechatGiftRepository>(),
            context.read<OpsCustomTableRepository>(),
            context.read<AccountRightsStateProvider>(),
            context.read<RewardRepository>(),
            context.read<NewUserRepository>(),
            context.read<AuthUseCaseProvider>(),
          ),
        ),
      ],
      child: Scaffold(
        backgroundColor: const Color(0xFF0D0D0D),
        body: TitleBarWidget(
          backgroundColor: const Color(0xFF0D0D0D),
          child: RouteAwareDragHandlerWidget(
            canProcessFiles: () => true, // AIGC页面总是可以处理文件
            showDragForbiddenOverlay: true,
            onDragDone: (result) async {
              // 处理拖拽完成，result是DealImageFilesResult类型
              if (result.validFiles.isNotEmpty) {
                PGLog.d('AIGC项目页面 - 拖拽文件: ${result.validFiles.length} 个有效文件');

                try {
                  PGDialog.showLoading();
                  // 直接使用result.validFiles创建项目，而不是调用handleDragDone
                  final createResult = await _viewModel.createProject(
                    result.validFiles,
                    projectName: result.projectName,
                  );
                  await PGDialog.dismiss();

                  if (createResult is ErrorStatus &&
                      createResult.errorType ==
                          ProcessFilesErrorType.diskSpace) {
                    PGDialog.showToast(createResult.message);
                  }

                  PGLog.d(
                      'AIGC项目页面 - 处理拖拽文件成功: ${result.validFiles.length} 个有效文件');
                } catch (e) {
                  await PGDialog.dismiss();
                  PGLog.e('AIGC项目页面 - 处理拖拽文件失败: $e');
                  PGDialog.showToast('处理文件失败，请重试');
                }
              }
            },
            child: Row(
              children: [
                // 左侧个人中心
                SizedBox(
                  width: 318,
                  child: Consumer<ProfileDialogViewModel>(
                    builder: (context, profileViewModel, child) {
                      return _buildProfileView();
                    },
                  ),
                ),
                // 右侧主内容区域
                Expanded(
                  child: Container(
                    color: const Color(0xFF0D0D0D),
                    child: Column(
                      children: [
                        const SizedBox(height: 14),
                        // 头部区域
                        _buildHeader(context),
                        // 内容区域
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(
                              top: 12,
                              left: 24,
                              right: 24,
                              bottom: 12,
                            ),
                            child: Consumer<AigcProjectViewModel>(
                              builder: (context, viewModel, child) {
                                return _buildContent(viewModel);
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProfileView() {
    return ProjectHomePcProfileView(
      key: _aigcProfileViewKey,
      userCardKey: _aigcUserCardKey,
      // 点击用户账号展示个人中心功能列表弹窗
      onUserAccountClick: () => ProfileViewClickService.handleUserAccountClick(
        context: context,
        profileViewKey: _aigcProfileViewKey,
        userCardKey: _aigcUserCardKey,
      ),
      // 点击完善账号信息展示微信福利弹窗
      onCompleteAccountInfoClick: () =>
          ProfileViewClickService.handleShowWechatGiftDialog(
        context: context,
      ),
      // 点击购买套餐展示购买套餐弹窗
      onBuyPackageClick: () => ProfileViewClickService.handleBuyPackageClick(
        sourceType: SourceType.home_page,
      ),
      // 点击全部项目
      onAllProjectsClick: () => {},
      // 点击快捷键指南展示快捷键指南弹窗
      onGuideClick: () => ProfileViewClickService.handleGuideClick(),
      // 点击快捷键展示快捷键弹窗
      onShortKeyClick: () => ProfileViewClickService.handleShortKeyClick(),
      // 点击客服展示客服弹窗
      onCustomerServiceClick: () =>
          ProfileViewClickService.handleCustomerServiceClick(),
      // 点击检查更新展示检查更新弹窗
      onCheckUpdateClick: () => ProfileViewClickService.handleCheckUpdateClick(
        context: context,
      ),
      // 点击版本介绍展示版本介绍弹窗
      onVersionIntroduceClick: () =>
          ProfileViewClickService.handleVersionIntroduceClick(
        context: context,
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent(AigcProjectViewModel viewModel) {
    if (!viewModel.hasProjects) {
      return Center(
        child: ProjectHomeEmptyState(
          onSelectFiles: _handleSelectFiles,
        ),
      );
    }

    return ProjectGridView<ProjectUIModel>(
      items: viewModel.projectUIModels,
      itemWidth: 250,
      itemHeight: 250,
      itemBuilder: (context, uiModel, index) {
        final project = viewModel.projects[index]; // 获取对应的ProjectInfo用于操作
        return ProjectGridItem(
          key: ValueKey(project.uuid),
          uiModel: uiModel,
          projectId: project.uuid,
          onTap: () async {
            PGDialog.showLoading();
            final guard = await _viewModel.trySelectedProject(index);
            await PGDialog.dismiss();
            if (guard == SelectProjectErrorType.diskSpace) {
              UniversalDialog.show(
                title: '磁盘空间不足',
                content: '您的C盘空间不足！建议清理磁盘后继续使用\n点击确认后可进入项目，有风险导致项目损坏！',
                onConfirm: () {
                  if (mounted) {
                    viewModel.selectProject(project.uuid);
                  }
                  UniversalDialog.hide();
                },
                confirmText: '确认风险并进入',
                cancelText: '取消进入',
              );
              return;
            }
            _viewModel.selectProject(project.uuid);
          },
          onRename: () => _handleRename(project),
          onDelete: () => _handleDelete(project),
        );
      },
    );
  }

  /// 构建头部区域
  Widget _buildHeader(BuildContext context) {
    return Container(
      height: 56,
      width: double.infinity,
      padding: const EdgeInsets.only(left: 24, right: 24, top: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 返回按钮
          PlatformMouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                Navigator.of(context).pop();
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/icons/page_back.png',
                    width: 24,
                    height: 24,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'AI场景增强',
                    style: TextStyle(
                      fontFamily: Fonts.defaultFontFamily,
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                      height: 1.0,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // 功能按钮区域
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 主题预设管理
              _buildActionButton(
                icon: 'assets/icons/ai_project_preset_icon.png',
                backgroundColor: const Color(0xFF1F1F1F),
                borderColor: Colors.transparent,
                text: '主题预设管理',
                width: 120,
                onTap: () => context.push(Routes.aigcPresets),
              ),
              const SizedBox(width: 8),
              // 打样中心
              _buildActionButton(
                icon: 'assets/icons/ai_project_sample_icon.png',
                backgroundColor: const Color(0xFF1F1F1F),
                borderColor: Colors.transparent,
                text: '打样中心',
                width: 96,
                onTap: () => context.push(Routes.aigcSample),
              ),
              const SizedBox(width: 8),
              // 分隔线
              Container(
                width: 1,
                height: 16,
                decoration: const BoxDecoration(
                  border: Border(
                    left: BorderSide(
                      color: Color(0x1AFFFFFF),
                      width: 1,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // 创建项目
              _buildActionButton(
                icon: 'assets/icons/aigc_presets_create_icon.png',
                backgroundColor: const Color(0xFFF72561),
                borderColor: const Color(0xFFF72561),
                text: '创建项目',
                width: 96,
                onTap: _handleSelectFiles,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建功能按钮
  Widget _buildActionButton({
    required String icon,
    required Color backgroundColor,
    required Color borderColor,
    required VoidCallback onTap,
    String? text,
    double? width,
    Color? iconColor,
  }) {
    return PlatformMouseRegion(
      cursor: SystemMouseCursors.click,
      child: DebounceClickWidget(
        onTap: onTap,
        child: Container(
          width: width,
          height: 32,
          padding: const EdgeInsets.only(
            top: 4,
            right: 7,
            bottom: 4,
            left: 7,
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color: borderColor,
              width: 1,
            ),
            color: backgroundColor,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                icon,
                width: 24,
                height: 24,
                color: iconColor,
              ),
              if (text != null) ...[
                const SizedBox(width: 4),
                Text(
                  text,
                  style: TextStyle(
                    fontFamily: Fonts.defaultFontFamily,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                    height: 1.0,
                    color: Colors.white,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 处理选择文件
  void _handleSelectFiles() async {
    PGLog.d('AIGC项目页面 - 选择文件');

    try {
      // 使用图片选择服务从文件系统选择图片
      PGDialog.showLoading();
      final result = await _viewModel.pickImagesFromFiles();
      if (result != null && result.validFiles.isNotEmpty) {
        final createResult = await _viewModel.createProject(
          result.validFiles,
          projectName: result.projectName,
        );
        if (createResult is ErrorStatus &&
            createResult.errorType == ProcessFilesErrorType.diskSpace) {
          await PGDialog.dismiss();
          PGDialog.showToast(createResult.message);
        }
      }
      await PGDialog.dismiss();
      if (result != null && result.validFiles.isNotEmpty) {
        PGLog.d('AIGC项目页面 - 选择文件成功: ${result.validFiles.length} 个有效文件');
      } else {
        PGLog.d('AIGC项目页面 - 没有选择文件或没有有效文件');
      }
    } catch (e) {
      await PGDialog.dismiss();
      PGLog.e('AIGC项目页面 - 选择文件失败: $e');
      PGDialog.showToast('选择文件失败，请重试');
    }
  }

  /// 处理项目点击
  void _handleItemClick(ProjectInfo project) {
    _viewModel.selectProject(project.uuid);
  }

  /// 处理重命名
  void _handleRename(ProjectInfo project) {
    PGLog.d('重命名项目: ${project.name}');

    // 显示重命名对话框
    ProjectRenamePCDialog.show(
      currentName: project.name,
      onConfirm: (newName) async {
        try {
          PGLog.d('AIGC项目页面 - 重命名项目: ${project.uuid} -> $newName');
          await _viewModel.renameProject(project.uuid, newName);
          PGDialog.showToast('重命名成功');
        } catch (e) {
          PGLog.e('AIGC项目页面 - 重命名项目失败: $e');
          PGDialog.showToast('重命名失败，请重试');
        }
      },
    );
  }

  /// 处理删除
  void _handleDelete(ProjectInfo project) {
    PGLog.d('删除项目: ${project.name}');

    // 显示删除确认对话框
    DeleteProjectDialog.show(
      onConfirm: () async {
        try {
          DeleteProjectDialog.hide();
          PGLog.d('AIGC项目页面 - 确认删除项目: ${project.uuid}');
          PGDialog.showLoading();
          await _viewModel.deleteProject(project.uuid);
          await PGDialog.dismiss();
          PGDialog.showToast('删除项目成功');
        } catch (e) {
          await PGDialog.dismiss();
          PGLog.e('AIGC项目页面 - 删除项目失败: $e');
          PGDialog.showToast('删除项目失败，请重试');
        }
      },
    );
  }
}
