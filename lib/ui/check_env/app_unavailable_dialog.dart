import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pg_desktop_multi_window/desktop_multi_window.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../../config/env_config.dart';
import '../../utils/url_launcher_util.dart';
import '../../utils/windows_process_terminator.dart';
import '../project_home/services/profile_view_click_service.dart';
import 'app_mismatch_reason.dart';

// Windows 平台
class AppUnavailableDialog extends StatelessWidget {
  const AppUnavailableDialog({super.key, required this.checkResult});

  final CheckAppNotOK checkResult;

  final configSuggestionUrl = 'https://help.turing.art/6977519m0';

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 345,
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Color(0xFF121415),
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      child: Column(
        children: [
          createTitleWidget(),
          const SizedBox(height: 24),
          createDescWidget(),
          const SizedBox(height: 24),
          createButtonWidget(
              text: getButtonTip(checkResult),
              textColor: const Color(0xFFFFFFFF),
              backgroundColorBuilder: ({required bool isHovered}) {
                return isHovered
                    ? const Color(0xFFF73069)
                    : const Color(0xFFF72561);
              },
              onTap: () {
                if (checkResult is PoorPerformance) {
                  UrlLauncherUtil.openInSystemBrowser(configSuggestionUrl);
                } else {
                  UrlLauncherUtil.openInSystemBrowser(getDownloadUrl());
                }
              }),
          const SizedBox(height: 12),
          createButtonWidget(
              text: '咨询客服',
              textColor: const Color(0xFFE1E2E5),
              backgroundColorBuilder: ({required bool isHovered}) {
                return isHovered
                    ? const Color(0xFF323335)
                    : const Color(0xFF1B1C1F);
              },
              onTap: () {
                ProfileViewClickService.handleCustomerServiceClick();
              }),
          const SizedBox(height: 12),
          createButtonWidget(
              text: '退出软件',
              textColor: const Color(0xFFE1E2E5),
              backgroundColorBuilder: ({required bool isHovered}) {
                return isHovered
                    ? const Color(0xFF323335)
                    : const Color(0xFF1B1C1F);
              },
              onTap: () {
                exitApp();
              }),
        ],
      ),
    );
  }

  Text createDescWidget() {
    return Text(
      getDesc(checkResult),
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 14,
        fontFamily: Fonts.defaultFontFamily,
        fontWeight: Fonts.regular,
        color: const Color(0x80FFFFFF),
      ),
    );
  }

  Text createTitleWidget() {
    return Text(
      getTitle(checkResult),
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 16,
        fontFamily: Fonts.defaultFontFamily,
        fontWeight: Fonts.semiBold,
        color: const Color(0xFFE1E2E5),
      ),
    );
  }

  Widget createButtonWidget({
    Key? key,
    required String text,
    required Color textColor,
    required Color Function({required bool isHovered}) backgroundColorBuilder,
    required VoidCallback onTap,
  }) {
    return HoverWidget(
      key: key,
      onTap: onTap,
      builder: ({required bool isHovered}) {
        return Container(
          width: 297,
          height: 44,
          decoration: BoxDecoration(
            color: backgroundColorBuilder(isHovered: isHovered),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Center(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontFamily: Fonts.defaultFontFamily,
                fontWeight: Fonts.medium,
                color: textColor,
              ),
            ),
          ),
        );
      },
    );
  }

  String getDownloadUrl() {
    String downloadUrl;
    if (EnvConfig.environment.isProd) {
      downloadUrl = 'https://turing.art/download?os=';
    } else {
      downloadUrl = 'https://dev.tuling.art/download?os=';
    }

    final String osParam;
    final systemName = Platform.operatingSystemVersion.toLowerCase();
    if (systemName.contains('windows 10')) {
      osParam = 'win10';
    } else if (systemName.contains('windows 11')) {
      osParam = 'win11';
    } else {
      osParam = 'win7';
    }
    downloadUrl = downloadUrl + osParam;
    return downloadUrl;
  }

  String getTitle(CheckAppNotOK checkResult) {
    if (checkResult is PoorPerformance) {
      return '⚠️设备不满足稳定运行条件，无法继续使用';
    }
    return '⚠️系统不匹配，无法继续使用';
  }

  String getDesc(CheckAppNotOK checkResult) {
    if (checkResult is PoorPerformance) {
      if (checkResult.ram == null || checkResult.vram == null) {
        return '您的设备内存低于软件运行最低 8G 要求；设备显存低于软件运行所需的 2G 要求';
      } else {
        final ramGb = (checkResult.ram! / 1024.0).toStringAsFixed(1);
        final vramGb = (checkResult.vram! / 1024.0).toStringAsFixed(1);
        return '您的设备内存 ${ramGb}G，低于软件运行最低 8G 要求；设备显存 ${vramGb}G，低于软件运行所需的 2G 要求';
      }
    } else if (checkResult is NeedDownloadWin7App) {
      return '您的系统是 Windows 7，当前软件需在 Windows 10 才能正常运行。点击“下载 Windows 7 版本”以下载此设备可运行的软件。';
    } else if (checkResult is NeedDownloadWin10App) {
      return '您的系统是 Windows 10/11，当前软件需在 Windows 7 才能正常运行。点击“下载 Windows 10/11 版本”以下载此设备可运行的软件。';
    }
    return '';
  }

  String getButtonTip(CheckAppNotOK checkResult) {
    if (checkResult is PoorPerformance) {
      return '了解配置建议';
    } else if (checkResult is NeedDownloadWin7App) {
      return '下载最新 Windows 7 版本';
    } else if (checkResult is NeedDownloadWin10App) {
      return '下载最新 Windows 10 版本';
    }
    return '';
  }

  Future<void> exitApp() async {
    if (Platform.isWindows) {
      // 在Windows上使用原生方法终止进程
      try {
        // 1. 先关闭所有子窗口
        DesktopMultiWindow.getAllSubWindowIds().then((windowIds) {
          for (final id in windowIds) {
            WindowController.fromWindowId(id).close();
          }

          // 2. 使用Windows原生方法终止进程
          bool terminated = WindowsProcessTerminator.terminateCurrentProcess();

          // 3. 如果原生方法失败，尝试其他方法
          if (!terminated) {
            SystemNavigator.pop(animated: true);

            // 最后尝试强制退出
            Future.delayed(const Duration(milliseconds: 500), () {
              exit(0);
            });
          }
        });
      } catch (e) {
        PGLog.d('退出应用时出错: $e');
        // 出错时使用强制退出
        exit(0);
      }
    } else {
      // 其他平台使用标准退出
      SystemNavigator.pop();
    }
  }
}

class HoverWidget extends StatefulWidget {
  final Widget Function({required bool isHovered}) builder;
  final VoidCallback? onTap;

  const HoverWidget({
    super.key,
    required this.builder,
    this.onTap,
  });

  @override
  State<HoverWidget> createState() => _HoverWidgetState();
}

class _HoverWidgetState extends State<HoverWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) {
        setState(() {
          _isHovered = true;
        });
      },
      onExit: (_) {
        setState(() {
          _isHovered = false;
        });
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: widget.builder(isHovered: _isHovered),
      ),
    );
  }
}
