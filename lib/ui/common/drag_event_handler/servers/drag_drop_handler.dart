import 'dart:io';

import 'package:cross_file/cross_file.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/core/tapj/n8_tapj_file_manager.dart';
import 'package:turing_art/datalayer/domain/enums/time_display_format.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/utils/date_time_util.dart';
import 'package:turing_art/utils/file_name_utils.dart';
import 'package:turing_art/utils/permission_util.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/tapj_processor.dart';

import 'drag_drop_interface.dart';

/// 拖放文件信息类（仅限当前文件内部使用）
/// 存储拖放过程中收集的文件信息
class DragFilesInfo {
  /// 第一个文件夹的名称（可能用作项目名）
  String? firstDirName;

  /// 第一个有效文件的名称（可能用作项目名）
  String? firstFileName;

  /// 确定的项目名称
  String? projectName;

  /// 是否是顶层调用标记
  bool isTopLevel = true;
}

/// 拖放名称处理器（仅限当前文件内部使用）
/// 负责处理拖放文件的项目名称生成
class DragNameProcessor {
  /// 获取默认项目名称（基于当前日期）
  static String getDefaultProjectName() {
    return DateTimeUtil.getCurrentDateStr(format: DateFormat.standardNoHour);
  }

  /// 确定最终的项目名称
  /// 优先级：1. 文件名 2. 文件夹名 3. 当前日期
  static void determineProjectName(DragFilesInfo info) {
    if (!info.isTopLevel) {
      return;
    }

    if (info.firstFileName != null && info.firstFileName!.isNotEmpty) {
      info.projectName = info.firstFileName;
    } else if (info.firstDirName != null && info.firstDirName!.isNotEmpty) {
      info.projectName = info.firstDirName;
    } else {
      info.projectName = getDefaultProjectName();
    }
  }
}

/// 桌面端拖放处理器
class DesktopDragDropHandler implements DragDropInterface {
  @override
  Future<DealImageFilesResult?> processDroppedFiles(List<XFile> files) async {
    try {
      PGLog.d('DesktopDragDropHandler - processDroppedFiles');
      if (Platform.isMacOS) {
        final permissionGranted =
            await PermissionUtil.requestPermission(PermissionType.file);
        if (!permissionGranted) {
          PGLog.d('用户未授予文件权限');
          return null;
        }
      }

      // 检查是否有tapj文件，优先处理
      for (final xFile in files) {
        final file = File(xFile.path);
        if (TapjProcessor.isTapjFile(file.path)) {
          PGLog.d('在拖拽文件中发现tapj文件: ${file.path}');
          return await TapjProcessor.processTapjForImagePicker(file.path);
        }
      }

      List<File> validFiles = [];

      // 递归处理文件和文件夹
      var fileInfo = DragFilesInfo();
      _processItems(files, validFiles, fileInfo);

      // 确定项目名称
      DragNameProcessor.determineProjectName(fileInfo);

      // 返回处理结果
      return validFiles.isNotEmpty
          ? DealImageFilesResult.fromImages(
              validFiles, fileInfo.projectName ?? '')
          : null;
    } catch (e) {
      PGLog.e('处理拖放文件时出错: $e');
      return null;
    }
  }

  /// 递归处理拖放的文件和文件夹
  ///
  /// [items] 要处理的XFile项目列表
  /// [validFiles] 收集有效的图片文件
  /// [fileInfo] 文件信息收集对象
  void _processItems(
    List<XFile> items,
    List<File> validFiles,
    DragFilesInfo fileInfo,
  ) {
    for (var xFile in items) {
      final fileSystemEntity = File(xFile.path);
      final fileStat = fileSystemEntity.statSync();

      if (fileStat.type == FileSystemEntityType.directory) {
        // 处理文件夹
        _processDirectory(xFile, validFiles, fileInfo);
      } else if (fileStat.type == FileSystemEntityType.file) {
        // 处理文件
        _processFile(xFile, validFiles, fileInfo);
      }
    }
  }

  /// 处理单个文件夹
  void _processDirectory(
      XFile xFile, List<File> validFiles, DragFilesInfo fileInfo) {
    // 只在顶层调用中记录第一个文件夹名称
    if (fileInfo.isTopLevel && fileInfo.firstDirName == null) {
      fileInfo.firstDirName = xFile.path.split(Platform.pathSeparator).last;
    }

    // 递归处理文件夹中的内容
    final dir = Directory(xFile.path);
    final entities = dir.listSync();

    // 将目录中的文件转换为XFile列表
    final subItems = entities
        .map((entity) => XFile(entity.path,
            name: entity.path.split(Platform.pathSeparator).last))
        .toList();

    // 保存原始的顶层状态
    final bool wasTopLevel = fileInfo.isTopLevel;

    // 设置为非顶层状态进行递归处理
    fileInfo.isTopLevel = false;

    // 递归处理子项目，直接使用现有的fileInfo对象
    _processItems(
      subItems,
      validFiles,
      fileInfo,
    );

    // 恢复原始的顶层状态，继续处理下一个同级别文件夹
    fileInfo.isTopLevel = wasTopLevel;
  }

  /// 处理单个文件
  void _processFile(
      XFile xFile, List<File> validFiles, DragFilesInfo fileInfo) {
    // 检查文件扩展名是否为支持的图片格式或tapj格式
    final extension = xFile.name.split('.').last.toLowerCase();
    if (ImageConstants.supportedExtensions.contains(extension) ||
        N8TapjFileManager.supportedTapjExtensions.contains(extension)) {
      validFiles.add(File(xFile.path));

      // 只在顶层调用中记录第一个有效文件的名称（不含扩展名）
      if (fileInfo.isTopLevel && fileInfo.firstFileName == null) {
        fileInfo.firstFileName =
            FileNameUtils.extractNameWithoutExtension(xFile.name);
      }
    }
  }
}

/// 移动端拖放处理器（暂时空实现）
class MobileDragDropHandler implements DragDropInterface {
  @override
  Future<DealImageFilesResult?> processDroppedFiles(List<XFile> files) async {
    // 移动端暂不支持拖放
    return null;
  }
}
