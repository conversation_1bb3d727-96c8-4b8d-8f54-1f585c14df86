import 'package:pg_turing_collect_event/collect/project_info.dart'
    show ProjectAction;
import 'package:pg_turing_collect_event/model.dart' show recordProjectInfo;
import 'package:turing_art/core/tapj/history_file_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/workspace_provider.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handler_chain.dart';
import 'package:turing_art/ui/ui_status/process_files_ui_status.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/use_case/project/project_usecase_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 1. 检查磁盘空间处理器
class DiskSpaceCheckHandler extends BaseProjectCreateSelectHandler {
  DiskSpaceCheckHandler(this._workspaceUseCase);

  final WorkspaceUseCaseProvider _workspaceUseCase;

  @override
  String get name => 'DiskSpaceCheckHandler';

  @override
  Future<HandlerResult> handle(
      ProjectCreateSelectHandlerContext context) async {
    PGLog.d('ProjectHandler - $name - check disk space');

    if (!await _workspaceUseCase.generateWorkspaceDiskSpaceGuard
        .invoke(files: context.files)) {
      PGLog.e('ProjectHandler - $name - disk space not enough');
      // 设置错误类型
      context.errorType = ProcessFilesErrorType.diskSpace;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.diskSpace);
    }

    PGLog.d('ProjectHandler - $name - disk space check passed');
    return handleNext(context);
  }
}

/// 2. 创建工程处理器
class CreateProjectHandler extends BaseProjectCreateSelectHandler {
  CreateProjectHandler(this._projectUseCase);

  final ProjectUseCaseProvider _projectUseCase;

  @override
  String get name => 'CreateProjectHandler';

  @override
  Future<HandlerResult> handle(
      ProjectCreateSelectHandlerContext context) async {
    PGLog.d('ProjectHandler - $name - start');

    // 如果是覆盖模式，跳过创建项目，直接使用现有projectId
    if (context.isOverwriteMode) {
      if (context.projectId == null || context.projectId!.isEmpty) {
        PGLog.e('ProjectHandler - $name - 覆盖模式下项目ID不能为空');
        context.errorType = ProcessFilesErrorType.projectIdMissing;
        return HandlerResult.breakWithError(
            errorType: ProcessFilesErrorType.projectIdMissing);
      }

      // 在覆盖模式下，使用现有项目ID作为目标项目ID
      context.createdProjectId = context.projectId;
      PGLog.d('ProjectHandler - $name - 覆盖模式：使用现有项目ID: ${context.projectId}');
      return handleNext(context);
    }

    // 正常模式：创建新项目
    final ProjectInfo? project = await _projectUseCase.generateProject.invoke(
      [],
      context.projectId,
      context.projectName,
      ProjectType.edit,
    );

    if (project == null) {
      PGLog.e('ProjectHandler - $name - failed to create project');
      // 设置错误类型
      context.errorType = ProcessFilesErrorType.generateProject;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.generateProject);
    }

    // 将创建的项目ID存入上下文
    context.createdProjectId = project.uuid;

    // 如果设置了项目创建成功回调，则调用它
    if (context.onProjectCreated != null) {
      try {
        context.onProjectCreated!(project.uuid);
        PGLog.d(
            'ProjectHandler - $name - onProjectCreated callback executed successfully');
      } catch (e) {
        PGLog.e('ProjectHandler - $name - onProjectCreated callback error: $e');
        // 回调错误不影响项目创建流程，继续执行
      }
    }

    PGLog.d('ProjectHandler - $name - success');
    return handleNext(context);
  }
}

/// 2. 创建AIGC工程处理器
class CreateAigcProjectHandler extends BaseProjectCreateSelectHandler {
  CreateAigcProjectHandler(this._projectUseCase);

  final ProjectUseCaseProvider _projectUseCase;

  @override
  String get name => 'CreateProjectHandler';

  @override
  Future<HandlerResult> handle(
      ProjectCreateSelectHandlerContext context) async {
    PGLog.d('ProjectHandler - $name - start');

    final ProjectInfo? project = await _projectUseCase.generateProject.invoke(
      context.files,
      context.projectId,
      context.projectName,
      ProjectType.aiGen,
    );

    if (project == null) {
      PGLog.e('ProjectHandler - $name - failed to create project');
      // 设置错误类型
      context.errorType = ProcessFilesErrorType.generateProject;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.generateProject);
    }

    // 将创建的项目ID存入上下文
    context.createdProjectId = project.uuid;

    // 如果设置了项目创建成功回调，则调用它
    if (context.onProjectCreated != null) {
      try {
        context.onProjectCreated!(project.uuid);
        PGLog.d(
            'ProjectHandler - $name - onProjectCreated callback executed successfully');
      } catch (e) {
        PGLog.e('ProjectHandler - $name - onProjectCreated callback error: $e');
        // 回调错误不影响项目创建流程，继续执行
      }
    }

    PGLog.d('ProjectHandler - $name - success');
    return handleNext(context);
  }
}

/// 3. 上报工程信息处理器
class ReportProjectInfoHandler extends BaseProjectCreateSelectHandler {
  ReportProjectInfoHandler(this._currentUserRepository);

  final CurrentUserRepository _currentUserRepository;

  @override
  String get name => 'ReportProjectInfoHandler';

  @override
  Future<HandlerResult> handle(
      ProjectCreateSelectHandlerContext context) async {
    PGLog.d('ProjectHandler - $name - start');

    // 确定要使用的项目ID
    // 优先使用createdProjectId（新建项目场景），如果为null则使用projectId（选择现有项目场景）
    final String? targetProjectId =
        context.createdProjectId ?? context.projectId;

    if (targetProjectId == null) {
      PGLog.e('ProjectHandler - $name - no project id available');
      context.errorType = ProcessFilesErrorType.projectIdMissing;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.projectIdMissing);
    }

    // 使用 Future.microtask 异步执行上报
    Future.microtask(() {
      final updateTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      final userId = _currentUserRepository.user?.effectiveId ?? "";
      try {
        recordProjectInfo(
          userId: userId,
          projectId: targetProjectId,
          projectAction: ProjectAction.create,
          updatetime: updateTime,
          projectName: context.projectName ?? "",
          photoNum: context.files.length.toString(),
        );
        PGLog.d(
            'ProjectHandler - $name - success: reported project info for project: $targetProjectId');
      } catch (e) {
        PGLog.e('ProjectHandler - $name - error reporting project info: $e');
      }
    });

    return handleNext(context);
  }
}

/// 5. 设置工作区处理器
class SetupWorkspaceHandler extends BaseProjectCreateSelectHandler {
  SetupWorkspaceHandler(
    this._unityController,
    this._unityUseCase,
  );

  final UnityController _unityController;
  final UnityUseCaseProvider _unityUseCase;

  @override
  String get name => 'SetupWorkspaceHandler';

  @override
  Future<HandlerResult> handle(
      ProjectCreateSelectHandlerContext context) async {
    PGLog.d('ProjectHandler - $name - start');

    // 确定要使用的项目ID
    // 优先使用createdProjectId（新建项目场景），如果为null则使用projectId（选择现有项目场景）
    final String? targetProjectId =
        context.createdProjectId ?? context.projectId;

    if (targetProjectId == null) {
      PGLog.e('ProjectHandler - $name - no project id available');
      context.errorType = ProcessFilesErrorType.projectIdMissing;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.projectIdMissing);
    }

    try {
      // 发送导入工作区信息到Unity
      final importMessage =
          await _unityUseCase.importWorkspaces.invoke(targetProjectId);
      if (importMessage != null) {
        final result = await _unityController.sendMessage(importMessage);
        PGLog.d('ProjectHandler - $name - workspace import result: $result');
      } else {
        PGLog.w('ProjectHandler - $name - import message is null');
      }

      // 设置导出配置 - 原始代码这里没有使用await，我们保持不阻塞
      final exportConfigMessage =
          await _unityUseCase.setupExportConfig.invoke(targetProjectId);
      if (exportConfigMessage != null) {
        // 不使用await，允许异步执行
        _unityController.sendMessage(exportConfigMessage);
        PGLog.d('ProjectHandler - $name - export config message sent');
      } else {
        PGLog.w('ProjectHandler - $name - export config message is null');
      }

      PGLog.d('ProjectHandler - $name - success');
    } catch (e) {
      PGLog.e('ProjectHandler - $name - error setting up workspace: $e');
      context.errorType = ProcessFilesErrorType.setupWorkspace;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.setupWorkspace);
    }

    return handleNext(context);
  }
}

/// 6. 导入图片处理器
class ImportImagesHandler extends BaseProjectCreateSelectHandler {
  ImportImagesHandler(
    this._unityController,
    this._unityUseCase,
    this._historyFileManager,
    this._projectRepository,
  );

  final UnityController _unityController;
  final UnityUseCaseProvider _unityUseCase;
  final HistoryFileManager _historyFileManager;
  final ProjectRepository _projectRepository;

  @override
  String get name => 'ImportImagesHandler';

  // 发送从历史记录导入预设信息到Unity
  Future<void> _sendImportPresetFromHistoryToUnity(
    String projectId,
    Map<String, String> fileIdToHistoryIdMap,
  ) async {
    try {
      if (fileIdToHistoryIdMap.isEmpty) {
        PGLog.d('没有需要导入的历史预设数据');
        return;
      }

      PGLog.d('准备发送ImportPresetFromHistory消息，映射数据: $fileIdToHistoryIdMap');

      final message = await _unityUseCase.importPresetFromHistory
          .invoke(projectId, fileIdToHistoryIdMap);

      if (message != null) {
        final result = await _unityController.sendMessage(message);
        PGLog.d('发送ImportPresetFromHistory消息结果: $result');
      }
    } catch (e) {
      PGLog.e('发送ImportPresetFromHistory消息到Unity失败: $e');
    }
  }

  /// 根据项目ID和原图路径从数据库查询文件ID
  Future<String?> _getFileIdFromDatabase(
      String projectId, String orgPath) async {
    try {
      // 获取项目的所有文件
      final files = await _projectRepository.getProjectFiles(projectId);

      // 根据原图路径查找对应的文件
      for (final file in files) {
        if (file.orgPath == orgPath) {
          return file.fileId;
        }
      }

      // 如果没找到，记录警告
      PGLog.w('未找到匹配的文件: projectId=$projectId, orgPath=$orgPath');
      return null;
    } catch (e) {
      PGLog.e('查询文件ID失败: projectId=$projectId, orgPath=$orgPath, 错误: $e');
      return null;
    }
  }

  /// 根据fileList中的isSelected信息设置N8选版状态
  Future<void> _setN8SelectStatus(
      String projectId, List<FileItem> fileList) async {
    try {
      final Map<String, bool> fileIdToSelectMap = {};

      // 遍历fileList，构建fileId到isSelected的映射
      for (final fileItem in fileList) {
        final fileId =
            await _getFileIdFromDatabase(projectId, fileItem.originalPath);
        if (fileId != null && fileId.isNotEmpty) {
          fileIdToSelectMap[fileId] = fileItem.isSelected;
          PGLog.d(
              '设置N8选版状态: fileId=$fileId, isSelected=${fileItem.isSelected}');
        }
      }

      if (fileIdToSelectMap.isNotEmpty) {
        // 生成设置N8选版结果的消息
        final message = await _unityUseCase.setN8Select
            .invoke(projectId, fileIdToSelectMap);
        if (message != null) {
          final result = await _unityController.sendMessage(message);
          PGLog.d(
              '发送SetN8Select消息结果: $result, 设置了${fileIdToSelectMap.length}个文件的选版状态');
        } else {
          PGLog.w('生成SetN8Select消息失败');
        }
      } else {
        PGLog.d('没有需要设置选版状态的文件');
      }
    } catch (e) {
      PGLog.e('设置N8选版状态失败: $e');
    }
  }

  @override
  Future<HandlerResult> handle(
      ProjectCreateSelectHandlerContext context) async {
    PGLog.d('ProjectHandler - $name - start');

    // 确定要使用的项目ID
    // 优先使用createdProjectId（新建项目场景），如果为null则使用projectId（选择现有项目场景）
    final String? targetProjectId =
        context.createdProjectId ?? context.projectId;

    if (targetProjectId == null) {
      PGLog.e('ProjectHandler - $name - no project id available');
      context.errorType = ProcessFilesErrorType.projectIdMissing;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.projectIdMissing);
    }

    try {
      // 在覆盖模式下，只处理选版状态，不需要导入图片
      if (context.isOverwriteMode) {
        PGLog.d('ProjectHandler - $name - 覆盖模式：仅处理选版状态');

        // 根据fileList中的isSelected信息，设置N8选版状态
        if (context.fileList != null && context.fileList!.isNotEmpty) {
          PGLog.d('开始设置N8选版状态');
          await _setN8SelectStatus(targetProjectId, context.fileList!);

          // 调用setN8SelectFilterOn打开过滤开关
          PGLog.d('开始设置N8选版过滤器开关');
          await _setN8SelectFilterOn(targetProjectId, true);
        }

        PGLog.d('ProjectHandler - $name - 覆盖模式处理完成');
        return handleNext(context);
      }

      // 正常模式：导入图片
      // 将文件列表包装为DealImageFilesResult
      final dealImageFilesResult = DealImageFilesResult(context.files, "");
      PGLog.d(
          'ProjectHandler - $name - importing ${context.files.length} images to project: $targetProjectId');

      // 发送导入图片信息到Unity - 原始代码这里没有使用await，我们保持不阻塞
      final message = await _unityUseCase.importImagesToUnity.invoke(
        dealImageFilesResult,
        targetProjectId,
      );

      if (message != null) {
        // 不使用await，允许异步执行
        await _unityController.sendMessage(message).then((result) {
          PGLog.d('ProjectHandler - $name - import images result: $result');
        });
      } else {
        PGLog.w('ProjectHandler - $name - import images message is null');
      }

      // 在_sendImportImagesToUnity执行完成后写入历史文件
      if (context.historyFiles != null &&
          context.historyFiles!.isNotEmpty &&
          context.fileList != null) {
        PGLog.d('开始写入历史文件数据');
        var t = DateTime.now().millisecondsSinceEpoch;
        final fileIdToHistoryIdMap =
            await _historyFileManager.writeHistoryFilesToDisk(
                context.historyFiles!, context.fileList!, targetProjectId);
        var timeConsuming = DateTime.now().millisecondsSinceEpoch - t;
        PGLog.d('历史文件写入完成，耗时: $timeConsuming ms');
        // 写入完成后，需要发送消息通知Unity将历史记录放入预设
        await _sendImportPresetFromHistoryToUnity(
            targetProjectId, fileIdToHistoryIdMap);
      }

      // 根据fileList中的isSelected信息，设置N8选版状态
      if (context.fileList != null && context.fileList!.isNotEmpty) {
        PGLog.d('开始设置N8选版状态');
        await _setN8SelectStatus(targetProjectId, context.fileList!);
      }

      PGLog.d('ProjectHandler - $name - success');
    } catch (e) {
      PGLog.e('ProjectHandler - $name - error importing images: $e');
      context.errorType = ProcessFilesErrorType.importImages;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.importImages);
    }

    return handleNext(context);
  }

  /// 设置N8选版过滤器开关
  Future<void> _setN8SelectFilterOn(String projectId, bool isOn) async {
    try {
      final message =
          await _unityUseCase.setN8SelectFilterOn.invoke(projectId, isOn);
      if (message != null) {
        final result = await _unityController.sendMessage(message);
        PGLog.d(
            'ImportImagesHandler: 设置N8选版过滤器开关成功，项目ID: $projectId, 状态: $isOn, 结果: $result');
      } else {
        PGLog.w('ImportImagesHandler: 生成SetN8SelectFilterOn消息失败');
      }
    } catch (e) {
      PGLog.e('ImportImagesHandler: 设置N8选版过滤器开关失败: $e');
      rethrow;
    }
  }
}

/// 7. 导航到编辑页面处理器
class SwitchWorkspaceHandler extends BaseProjectCreateSelectHandler {
  SwitchWorkspaceHandler(
    this._projectStateProvider,
    this._unityController,
    this._unityUseCase,
  );

  final ProjectStateProvider _projectStateProvider;
  final UnityController _unityController;
  final UnityUseCaseProvider _unityUseCase;

  @override
  String get name => 'SwitchWorkspaceHandler';

  @override
  Future<HandlerResult> handle(
      ProjectCreateSelectHandlerContext context) async {
    PGLog.d('ProjectHandler - $name - start');

    // 确定要使用的项目ID
    // 优先使用createdProjectId（新建项目场景），如果为null则使用projectId（选择现有项目场景）
    final String? targetProjectId =
        context.createdProjectId ?? context.projectId;

    if (targetProjectId == null) {
      PGLog.e('ProjectHandler - $name - no project id available');
      context.errorType = ProcessFilesErrorType.projectIdMissing;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.projectIdMissing);
    }

    try {
      PGLog.d(
          'ProjectHandler - $name - navigating to edit page for project: $targetProjectId');

      // 检查是否已经在编辑状态
      if (_projectStateProvider.isEditing) {
        PGLog.d(
            'ProjectHandler - $name - already in editing state, skipping navigation');
        return handleNext(context);
      }

      // 发送切换工作区信息到Unity
      final message = await _unityUseCase.switchWorkspace.invoke(
        targetProjectId,
        isMigrated: context.isMigrated ?? false,
      );
      if (message != null) {
        await _unityController.sendMessage(message);
        PGLog.d('ProjectHandler - $name - workspace switched successfully');
      } else {
        PGLog.w('ProjectHandler - $name - switch workspace message is null');
      }

      // 设置当前项目ID
      _projectStateProvider.editProjectId(targetProjectId);

      PGLog.d('ProjectHandler - $name - success');
    } catch (e) {
      PGLog.e('ProjectHandler - $name - error navigating to edit page: $e');
      context.errorType = ProcessFilesErrorType.navigation;
      return HandlerResult.breakWithError(
          errorType: ProcessFilesErrorType.navigation);
    }

    return HandlerResult.breakWithSuccess();
  }
}
