import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_manager.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_list_export_request.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_project_model.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/providers/aigc_sample_export_polling_provider.dart';
import 'package:turing_art/providers/aigc_sample_list_polling_provider.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 加载状态枚举，用于替代简单的布尔标志
enum LoadState {
  initial, // 初始状态，还未开始加载
  loading, // 正在加载中
  loaded, // 已加载完成
  error, // 加载错误
}

class AigcSampleViewModel extends ChangeNotifier {
  // 预设仓库
  final AigcSampleRepository _repository;

  // 轮询Provider
  final AigcSampleListPollingProvider _pollingProvider;

  // 导出轮询Provider
  final AigcSampleExportPollingProvider _exportPollingProvider;

  // 导出管理器
  final AigcMySampleExportManager _exportManager;

  // 静态变量保存历史已完成样本ID，避免页面销毁导致状态丢失
  static final Set<String> _historicalCompletedSampleIds = <String>{};
  // 静态变量保存是否已经加载过样本数据
  static bool _hadLoadSamples = false;

  // 当前选中的tab
  int _selectedTab = 0;
  int get selectedTab => _selectedTab;

  // Stream订阅
  StreamSubscription<String>? _dataChangeSubscription;
  StreamSubscription<RetryExportRefreshEvent>? _retryRefreshSubscription;

  AigcSampleViewModel({
    required AigcSampleRepository repository,
    required AigcSampleListPollingProvider pollingProvider,
    required AigcSampleExportPollingProvider exportPollingProvider,
    required AigcMySampleExportManager exportManager,
  })  : _repository = repository,
        _pollingProvider = pollingProvider,
        _exportPollingProvider = exportPollingProvider,
        _exportManager = exportManager {
    _initializeDataStream();
    _initializeRetryRefreshStream();
    loadLocalProjectList();
  }

  List<AigcSampleModel> _aigcSampleList = [];
  List<AigcSampleModel> get aigcSampleList => _aigcSampleList;

  // 已完成数量
  final Map<String, List<AigcSampleModel>> _completedListMap = {};

  // 正在处理数量
  final Map<String, List<AigcSampleModel>> _processingListMap = {};

  // 已完成的当前选中的项目名称
  int _selectedProjectIndex = -1;
  int get selectedProjectIndex => _selectedProjectIndex;

  AigcSampleModel? _currentSample;
  AigcSampleModel? get currentSample => _currentSample;

  // 当前选中的项目id
  String _projectId = '';
  String get projectId => _projectId;

  // 是否有新的已完成项目
  List<AigcSampleModel> _newCompletedSampleList = [];
  List<AigcSampleModel> get newCompletedSampleList => _newCompletedSampleList;

  // 正在处理项目
  List<AigcSampleModel> _runningSampleList = [];
  List<AigcSampleModel> get runningSampleList => _runningSampleList;

  // 本地项目列表
  List<AigcSampleProjectModel> _localProjectList = [];
  List<AigcSampleProjectModel> get localProjectList => _localProjectList;

  // 加载状态，用于判断是否是第一次加载、正在加载、加载完成、加载失败
  LoadState _loadState = LoadState.initial;
  LoadState get loadState => _loadState;

  // 便捷的状态检查方法
  bool get isInitial => _loadState == LoadState.initial;
  bool get isDataLoading => _loadState == LoadState.loading;
  bool get isLoaded => _loadState == LoadState.loaded;
  bool get hasError => _loadState == LoadState.error;

  // 是否已经完成过首次初始化（专门用于判断是否需要执行初始化逻辑）
  bool _hasCompletedInitialLoad = false;

  /// 初始化数据流监听
  void _initializeDataStream() {
    _dataChangeSubscription = _repository.dataChangeStream.listen((eventType) {
      final isExportStatusRefresh = eventType
          .startsWith(AigcRequestConst.proofingListExportStatusRefresh);
      final isProofingStatusRefresh =
          eventType.startsWith(AigcRequestConst.listProofingStatusRefresh);

      if (isProofingStatusRefresh || isExportStatusRefresh) {
        PGLog.d('收到列表打样状态刷新事件');

        // 解析事件中的projectId
        // String? eventProjectId;
        // final parts = eventType.split(':');
        // if (parts.length > 1) {
        //   eventProjectId = parts[1];
        // }
        // 1.获取最新打样状态列表并判断是否需要刷新
        final isChange = isProofingStatusRefresh
            ? _checkListProofingStatusChange()
            : _checkListExportStatusChange();
        if (!isChange) {
          PGLog.d('列表${isProofingStatusRefresh ? '打样' : '导出'}状态未变更，不刷新');
          return;
        }
        // 2.变更后先停止轮询，获取最新数据后会重新检查是否需要轮询
        _pollingProvider.stopPolling();
        // 3.更新列表状态数据并检查是否还需要轮询
        PGLog.d(
            '更新当前项目列表${isProofingStatusRefresh ? '打样' : '导出'}状态，当前项目ID: $_projectId');
        _refreshSampleList();
      }
    });
  }

  // 获取本地项目列表
  Future<void> loadLocalProjectList() async {
    final projects = await _repository.getAigcSampleProjectList();
    // 按照更新时间排序
    final sortedProjects = projects.toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    if (sortedProjects.isNotEmpty) {
      _localProjectList = sortedProjects;
      loadSampleList(refresh: true);
    }
  }

  Future<void> _refreshSampleList() async {
    try {
      _loadState = LoadState.loading;
      // 1.获取打样列表
      final samples = await _repository.getAigcSampleList('');
      // 按照更新时间排序
      final sortedSamples = samples.toList()
        ..sort((a, b) => b.updateAt.compareTo(a.updateAt));
      // 2.更新本地数据
      _processAndUpdateSampleData(sortedSamples);
      // 3.检查是否需要开始或停止轮询
      _checkPollingStatus();
      // 4.检查是否需要开始或停止导出轮询
      _checkExportPollingStatus();
      // 5.通知监听器 - 已经在_processAndUpdateSampleData中调用了notifyListeners()，这里不需要重复调用
      // notifyListeners();
      PGLog.d('刷新打样列表完成，当前选中Tab: $_selectedTab');
    } catch (e) {
      PGLog.e("加载打样列表失败: $e");
      _clearSampleData(projectId);
      _loadState = LoadState.error;
      notifyListeners(); // 错误情况下需要通知UI更新
    }
  }

  // 根据项目获取打样列表
  Future<void> loadSampleList({bool refresh = false}) async {
    if (refresh) {
      _refreshSampleList();
      return;
    }

    if (!refresh &&
            (_selectedTab == 0 && _completedListMap[projectId] != null) ||
        (_selectedTab == 1 && _processingListMap[projectId] != null)) {
      // 根据选中的项目名称，获取对应的打样列表
      _updateCurrentDisplayList(projectId);
      // 检查是否需要开始或停止轮询
      _checkPollingStatus();
      notifyListeners();
      return;
    }

    // 如果缓存中没有数据，尝试从Repository获取（可能从缓存或本地获取）
    _refreshSampleList();
  }

  /// 处理和更新样本数据的通用方法
  void _processAndUpdateSampleData(List<AigcSampleModel> samples) {
    // 更新已完成和正在处理的列表
    final completedSamples =
        samples.where((e) => e.status != AigcRequestConst.running).toList();

    _runningSampleList =
        samples.where((e) => e.status == AigcRequestConst.running).toList();

    // 根据项目ID，将样本列表分组
    for (final project in _localProjectList) {
      final projectSamples =
          samples.where((e) => e.projectId == project.projectId).toList();
      _completedListMap[project.projectId] = projectSamples
          .where((e) => e.status != AigcRequestConst.running)
          .toList();
      _processingListMap[project.projectId] = projectSamples
          .where((e) => e.status == AigcRequestConst.running)
          .toList();
    }

    // 过滤掉没有任何打样数据的项目（既没有已完成也没有正在处理的）
    _filterProjectList();

    // 如果已经加载过样本数据，则计算新增的已完成项目
    if (!_hadLoadSamples) {
      _hadLoadSamples = true;
      _historicalCompletedSampleIds.addAll(completedSamples.map((e) => e.id));
      PGLog.d('首次加载，将所有已完成项目添加到历史记录');
    }
    // 计算新增的已完成项目（使用静态历史记录进行对比）
    PGLog.d('计算新增的已完成项目');
    _newCompletedSampleList = completedSamples
        .where((sample) => !_historicalCompletedSampleIds.contains(sample.id))
        .toList();

    // 更新历史记录
    for (final sample in completedSamples) {
      // 如果新增的已完成项目中不包含这个id，则添加到历史记录
      if (!_newCompletedSampleList.any((e) => e.id == sample.id)) {
        _historicalCompletedSampleIds.add(sample.id);
      }
    }

    PGLog.d('新增已完成项目数量: ${_newCompletedSampleList.length}');
    PGLog.d('历史已完成项目总数: ${_historicalCompletedSampleIds.length}');

    // 只有在首次初始化时才执行特殊逻辑
    if (!_hasCompletedInitialLoad) {
      PGLog.d('执行首次初始化逻辑');
      // 如果正在处理的项目列表不为空，则切换到正在处理tab
      if (_runningSampleList.isNotEmpty) {
        _selectedTab = 1;
        // 如果正在处理的项目列表不为空，则切换到正在处理tab
        // 找出第一个正在处理的项目的项目id,并选中
        final firstRunningSampleProjectId = _findFirstRunningSampleProjectId();
        if (firstRunningSampleProjectId != null) {
          _projectId = firstRunningSampleProjectId;
          _selectedProjectIndex = _localProjectList
              .indexWhere((e) => e.projectId == firstRunningSampleProjectId);
        }
        PGLog.d('首次加载：有正在处理的打样，切换到正在处理Tab');
      } else if (_selectedTab != 0) {
        _selectedTab = 0;
        PGLog.d('首次加载：没有正在处理的打样，切换到已完成Tab');
      }

      // 标记已完成首次初始化
      _hasCompletedInitialLoad = true;
      PGLog.d('标记首次初始化完成');
    }

    // 标记为已加载完成（无论是否是初始加载，处理完数据后都应该是loaded状态）
    _loadState = LoadState.loaded;
    PGLog.d('设置加载状态为已完成');

    // 更新当前显示的列表
    _updateCurrentDisplayList(projectId);
    PGLog.d('更新当前显示列表完成');

    // 设置当前样本
    if (_aigcSampleList.isNotEmpty) {
      _currentSample = _aigcSampleList.first;
      PGLog.d('设置当前样本: ${_currentSample?.id}');
    } else {
      _currentSample = null;
      PGLog.d('无样本数据，清空当前样本');
    }

    // 所有状态更新完成后，只调用一次notifyListeners
    PGLog.d('准备通知UI更新');
    notifyListeners();
    PGLog.d(
        '处理和更新样本数据完成，当前选中Tab: $_selectedTab，正在处理数量: ${_runningSampleList.length}');
  }

  // 过滤掉没有任何打样数据的项目（既没有已完成也没有正在处理的）
  void _filterProjectList() {
    try {
      PGLog.d('开始过滤项目，当前项目数量: ${_localProjectList.length}');

      final validProjects = <AigcSampleProjectModel>[];

      for (final project in _localProjectList) {
        try {
          final projectId = project.projectId;
          final completedList = _completedListMap[projectId] ?? [];
          final processingList = _processingListMap[projectId] ?? [];

          // 如果项目有任何数据（已完成或正在处理），则保留
          if (completedList.isNotEmpty || processingList.isNotEmpty) {
            validProjects.add(project);
            PGLog.d(
                '保留项目: ${project.projectName} (已完成: ${completedList.length}, 处理中: ${processingList.length})');
          } else {
            PGLog.d('过滤掉项目: ${project.projectName} (无数据)');
          }
        } catch (e) {
          PGLog.e('处理项目时出错: $e, 项目: ${project.toString()}');
          // 出错时保留项目，避免意外删除
          validProjects.add(project);
        }
      }

      _localProjectList = validProjects;
      PGLog.d('过滤完成，剩余项目数量: ${_localProjectList.length}');
    } catch (e) {
      PGLog.e('过滤项目时发生异常: $e');
      // 如果过滤过程出错，保持原有项目列表不变
    }

    // 确保有项目可选，并设置默认选中项目
    if (_localProjectList.isNotEmpty) {
      // 如果当前没有选中项目，或者选中的项目已被过滤掉，则选择第一个项目
      if (_selectedProjectIndex == -1 ||
          _selectedProjectIndex >= _localProjectList.length ||
          !_localProjectList.any((p) => p.projectId == _projectId)) {
        _selectedProjectIndex = 0;
        _projectId = _localProjectList[0].projectId;
        PGLog.d('设置默认选中项目: ${_localProjectList[0].projectName}');
      }
    } else {
      // 如果没有任何项目，重置状态
      _selectedProjectIndex = -1;
      _projectId = '';
      PGLog.w('没有找到任何有效的项目');
    }
  }

  /// 清理样本数据的通用方法
  void _clearSampleData(String projectId) {
    _aigcSampleList = [];
    _completedListMap[projectId] = [];
    _processingListMap[projectId] = [];
    _selectedProjectIndex = -1;
  }

  /// 更新当前显示的列表
  void _updateCurrentDisplayList([String? projectId]) {
    final targetProjectId = projectId ?? _projectId;

    if (_selectedTab == 0) {
      _aigcSampleList = _completedListMap[targetProjectId] ?? [];
    } else {
      _aigcSampleList = _processingListMap[targetProjectId] ?? [];
    }
  }

  /// 检查轮询状态
  void _checkPollingStatus() {
    final processingList = _processingListMap.values.expand((e) => e).toList();

    if (processingList.isNotEmpty) {
      // 有正在处理的项目，开始轮询
      final processingIds = processingList.map((e) => e.id).toList();
      _pollingProvider.startPolling(processingIds, _projectId);
      PGLog.d('开始轮询，正在处理的数量: ${processingList.length}');
    } else {
      // 没有正在处理的项目，停止可能已有的轮询
      _pollingProvider.stopPolling();
      PGLog.d('停止轮询，没有正在处理的项目');
    }
  }

  // 检查列表打样状态是否变更
  bool _checkListProofingStatusChange() {
    final statusList =
        _repository.getLocalAigcSampleProofingStatusList(_projectId);
    if (statusList.isEmpty) {
      return true;
    }
    for (final status in statusList) {
      // 只要有一个状态不是在处理，就说明数据发生了变更
      if (status.status != AigcRequestConst.running) {
        return true;
      }
    }
    return false;
  }

  // 检查列表导出状态是否变更
  bool _checkListExportStatusChange() {
    final exportStatusList =
        _repository.getLocalAigcSampleExportStatusList(_projectId);
    if (exportStatusList.isEmpty) {
      return true;
    }
    PGLog.d('检查列表导出状态是否变更，当前项目ID: $_projectId，导出状态列表: $exportStatusList');
    // 获取服务器最新结果，转化成工程和是否完成的map
    final Map<String, bool> projectExportCompletedMap = {};
    for (final exportStatus in exportStatusList) {
      final projectId = exportStatus.id;
      if (!projectExportCompletedMap.containsKey(projectId)) {
        // 还没有过先默认设置为已完成
        projectExportCompletedMap[projectId] = true;
      }
      // 只要有一个效果是running状态，就说明这个工程的导出未完成(导出成功和失败都是完成)
      if (exportStatus.status == AigcRequestConst.running) {
        projectExportCompletedMap[projectId] = false;
      }
    }

    // 只要有一个工程的所有效果都是导出完成状态，就说明发生了变更，需要刷新列表
    for (final isCompleted in projectExportCompletedMap.values) {
      if (isCompleted) {
        PGLog.d('检查列表导出状态是否变更，有工程导出是完成状态，需要刷新列表');
        return true;
      }
    }

    PGLog.d('检查列表导出状态是否变更，没有工程导出是完成状态，不需要刷新列表');
    return false;
  }

  /// 检查导出轮询状态
  void _checkExportPollingStatus() {
    final completedList = _completedListMap[_projectId] ?? [];

    // 在_completedListMap里面去查找哪些打样效果在导出，则记录下打样id和对应的效果id
    final List<AigcSampleListExportRequest> exportingRequests = [];

    for (final sample in completedList) {
      for (final effect in sample.effects) {
        // 如果效果图的导出状态是running，则需要轮询
        if (effect.exportStatus == AigcRequestConst.running) {
          exportingRequests.add(AigcSampleListExportRequest(
            proofingId: sample.id,
            effectCode: effect.effectCode,
          ));
        }
      }
    }

    if (exportingRequests.isNotEmpty) {
      // 有需要轮询的导出任务，开始轮询
      _exportPollingProvider.startPolling(exportingRequests, _projectId);
      PGLog.d(
          '开始轮询导出状态，正在导出的数量: ${exportingRequests.length}，项目ID: $_projectId');
    } else {
      // 没有正在导出的任务，停止可能已有的轮询
      _exportPollingProvider.stopPolling();
      PGLog.d('停止导出轮询，没有正在导出的任务');
    }
  }

  void setCurrentSample(AigcSampleModel? sample) {
    _currentSample = sample;
    notifyListeners();
  }

  // 设置选中的tab
  void setSelectedTab(int index) {
    if (_selectedTab != index) {
      _selectedTab = index;
      _updateCurrentDisplayList(projectId);
      PGLog.d('设置选中Tab: $index，更新显示列表');
      notifyListeners();
    }
  }

  // 设置选中项目
  void setSelectedProjectIndex(int index) {
    if (index < 0 || index >= _localProjectList.length) {
      PGLog.w('无效的项目索引: $index，项目列表长度: ${_localProjectList.length}');
      return;
    }

    _selectedProjectIndex = index;
    _projectId = _localProjectList[_selectedProjectIndex].projectId;
    loadSampleList(refresh: false);
  }

  // 删除打样
  Future<void> deleteAigcSample(String id) async {
    await _repository.deleteAigcSample(id);
    loadSampleList(refresh: true);
  }

  // 删除新增的已完成样本
  void deleteNewCompletedSample(AigcSampleModel sample) {
    _newCompletedSampleList.removeWhere((e) => e.id == sample.id);
    // 将删除的样本添加到历史记录
    _historicalCompletedSampleIds.add(sample.id);
    notifyListeners();
  }

  /// 清除历史已完成样本记录
  static void clearHistoricalCompletedSamples() {
    _historicalCompletedSampleIds.clear();
    PGLog.d('已清除历史已完成样本记录');
  }

  /// 获取历史已完成样本数量
  static int getHistoricalCompletedSamplesCount() {
    return _historicalCompletedSampleIds.length;
  }

  String? _findFirstRunningSampleProjectId() {
    for (final project in _localProjectList) {
      if (_processingListMap[project.projectId]?.isNotEmpty ?? false) {
        return project.projectId;
      }
    }
    return null;
  }

  /// 初始化重试导出刷新事件监听
  void _initializeRetryRefreshStream() {
    _retryRefreshSubscription =
        _exportManager.retryRefreshStream.listen((event) {
      PGLog.d('收到重试导出刷新事件，涉及的打样ID: ${event.proofingIds}');
      _refreshSampleList();
    });
  }

  @override
  void dispose() {
    _dataChangeSubscription?.cancel();
    _retryRefreshSubscription?.cancel();
    super.dispose();
  }
}
