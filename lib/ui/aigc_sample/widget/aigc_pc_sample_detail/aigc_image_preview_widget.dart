import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_effect_model.dart';
import 'package:turing_art/ui/aigc_sample/view_model/aigc_sample_detail_view_model.dart';
import 'package:turing_art/ui/common/image_page/image_pagination_widget.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/widgets/common/cached_image_widget.dart';

class AigcImagePreviewWidget extends StatefulWidget {
  final AigcSampleEffectModel effect;
  final String originalUrl;
  final int currentIndex;
  final int totalPage;
  final ValueChanged<int>? onIndexChanged;

  const AigcImagePreviewWidget({
    super.key,
    required this.effect,
    required this.originalUrl,
    this.currentIndex = 0,
    required this.totalPage,
    this.onIndexChanged,
  });

  @override
  State<AigcImagePreviewWidget> createState() => _AigcImagePreviewWidgetState();
}

class _AigcImagePreviewWidgetState extends State<AigcImagePreviewWidget> {
  late int _currentIndex;
  // 使用ValueNotifier管理是否显示原图的状态
  final ValueNotifier<bool> _isShowingOriginalNotifier =
      ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.currentIndex;
  }

  @override
  void dispose() {
    // 释放ValueNotifier资源
    _isShowingOriginalNotifier.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(AigcImagePreviewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.currentIndex != oldWidget.currentIndex) {
      _currentIndex = widget.currentIndex;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 957,
      height: 957,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          // 图片预览区域 - 使用ValueListenableBuilder只在值变化时重建
          Positioned.fill(
            child: ValueListenableBuilder<bool>(
              valueListenable: _isShowingOriginalNotifier,
              builder: (context, isShowingOriginal, _) {
                return _buildImageView(isShowingOriginal);
              },
            ),
          ),

          // 页码组件
          Positioned(
            bottom: 16,
            left: 0,
            right: 0,
            child: Center(
              child: ImagePaginationWidget(
                currentPage: _currentIndex + 1,
                totalPages: widget.totalPage,
                onPrevious: _previousImage,
                onNext: _nextImage,
              ),
            ),
          ),

          // 对比按钮
          Positioned(
            bottom: 16,
            right: 16,
            child: _buildCompareButton(),
          ),
        ],
      ),
    );
  }

  Widget _buildTipView(String tip) {
    return Container(
      width: 957,
      height: 957,
      color: Colors.transparent,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/icons/aigc_credit_star_new.png',
              width: 32,
              height: 32,
              fit: BoxFit.fitWidth,
            ),
            const SizedBox(height: 12),
            Text(
              tip,
              style: TextStyle(
                color: Colors.white.withOpacity(0.35),
                fontSize: 16,
                fontFamily: Fonts.defaultFontFamily,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 修改为接收isShowingOriginal参数
  Widget _buildImageView(bool isShowingOriginal) {
    // 如果显示原图，则使用originalUrl，否则使用photoUrl
    final String displayUrl =
        isShowingOriginal ? widget.originalUrl : widget.effect.photoUrl;
    if (displayUrl.isEmpty) {
      return _buildTipView('正在打样中，请稍后...');
    }
    return Center(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: CachedImageWidget(
          imageUrl: displayUrl,
          placeholder: Image.asset(
            'assets/icons/aigc_credit_star_new.png',
            width: 32,
            height: 32,
            fit: BoxFit.fitWidth,
          ),
          errorWidget: Container(
            color: Colors.transparent,
            child: Center(
              child: Text(
                '预览图加载失败',
                style: TextStyle(
                  color: Colors.white54,
                  fontSize: 16,
                  fontFamily: Fonts.defaultFontFamily,
                ),
              ),
            ),
          ),
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildCompareButton() {
    return ValueListenableBuilder<bool>(
      valueListenable: _isShowingOriginalNotifier,
      builder: (context, isShowingOriginal, _) {
        return PlatformMouseRegion(
          onEnter: (_) {},
          onExit: (_) {},
          child: GestureDetector(
            onTapDown: (_) async {
              // 检查网络连接状态
              final viewModel = context.read<AigcSampleDetailViewModel>();
              final isConnected = await viewModel.checkNetworkConnection();
              if (!isConnected) {
                PGDialog.showToast('网络错误，请检查网络连接');
                return;
              }
              _isShowingOriginalNotifier.value = true;
            },
            onTapUp: (_) {
              _isShowingOriginalNotifier.value = false;
            },
            onTapCancel: () {
              _isShowingOriginalNotifier.value = false;
            },
            child: Container(
              width: 32,
              height: 32,
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isShowingOriginal
                    ? const Color(0xFF0D0D0D)
                    : const Color(0xFF383838),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Image.asset(
                'assets/icons/aigc_sample_detail_compar.png',
                color: isShowingOriginal ? Colors.white : Colors.white,
                width: 32,
                height: 24,
              ),
            ),
          ),
        );
      },
    );
  }

  void _previousImage() {
    widget.onIndexChanged?.call(_currentIndex - 1);
  }

  void _nextImage() {
    widget.onIndexChanged?.call(_currentIndex + 1);
  }
}
