import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';
import 'package:turing_art/datalayer/domain/models/export_record/export_record.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 导出类型枚举
enum ExportType {
  retouching, // 精修
  aigc, // AIGC
}

class ExportUiStatus {
  final String guid;
  final String projectName;
  final ValueNotifier<double> progress;
  int totalCount;
  int successCount;
  int createTime;
  ExportState state;
  String? failedReason;
  int? successTime;
  // 添加导出类型字段
  final ExportType exportType;
  // 添加小样标识字段，当exportFileConfig的editorType=1时为true
  final bool isSample;

  ExportUiStatus({
    required this.guid,
    required this.projectName,
    required this.state,
    required this.totalCount,
    required this.successCount,
    required this.progress,
    required this.createTime,
    required this.exportType, // 添加导出类型参数
    this.isSample = false, // 默认不是小样
    this.failedReason,
    this.successTime,
  });

  factory ExportUiStatus.fromRecord(ExportRecord record) {
    final files = record.exportFiles;
    final state = ExportState.fromCode(record.exportState);
    // if (record.errorNum != null && record.errorNum! > 0) {
    //   state = ExportState.error;
    // }
    final time = _parseOperateTime(record.operateTime);

    // 根据记录判断导出类型（这里需要根据实际情况调整判断逻辑）
    // 假设记录中有一个标志位或命名规则可以区分AIGC和精修
    final exportType = record.name?.toLowerCase().contains('aigc') ?? false
        ? ExportType.aigc
        : ExportType.retouching;

    return ExportUiStatus(
      guid: record.guid,
      createTime: record.createTime,
      state: state,
      projectName: record.showName ?? record.name ?? "未命名项目",
      totalCount: files.length,
      successCount: record.successNum,
      progress: ValueNotifier(record.successNum / files.length),
      successTime: time,
      exportType: exportType, // 设置导出类型
      isSample: record.isSample, // 直接使用record的isSample字段
      failedReason: null,
      // 暂时不需要展示异常信息，异常信息再dialog中展示
      // state == ExportState.error || state == ExportState.ioError
      //     ? _parseErrorMessage(record.errorNum,
      //         errorMessage: record.errorMessage)
      //     : null,
    );
  }

  /// 创建AIGC类型的导出状态
  factory ExportUiStatus.createAigc({
    required String guid,
    required String projectName,
    required ExportState state,
    required int totalCount,
    required int successCount,
    required int createTime,
    bool isSample = false,
    String? failedReason,
    int? successTime,
  }) {
    return ExportUiStatus(
      guid: guid,
      projectName: projectName,
      state: state,
      totalCount: totalCount,
      successCount: successCount,
      progress: ValueNotifier(totalCount > 0 ? successCount / totalCount : 0.0),
      createTime: createTime,
      exportType: ExportType.aigc,
      isSample: isSample,
      failedReason: failedReason,
      successTime: successTime,
    );
  }

  /// 创建精修类型的导出状态
  factory ExportUiStatus.createRetouching({
    required String guid,
    required String projectName,
    required ExportState state,
    required int totalCount,
    required int successCount,
    required int createTime,
    bool isSample = false,
    String? failedReason,
    int? successTime,
  }) {
    return ExportUiStatus(
      guid: guid,
      projectName: projectName,
      state: state,
      totalCount: totalCount,
      successCount: successCount,
      progress: ValueNotifier(totalCount > 0 ? successCount / totalCount : 0.0),
      createTime: createTime,
      exportType: ExportType.retouching,
      isSample: isSample,
      failedReason: failedReason,
      successTime: successTime,
    );
  }

  static int? _parseOperateTime(String timeStr) {
    try {
      final format = DateFormat('yyyy-MM-dd HH:mm:ss');
      return format.parse(timeStr).millisecondsSinceEpoch;
    } catch (e) {
      PGLog.w("时间解析失败: $timeStr");
      return null;
    }
  }

  // static String? _parseErrorMessage(int? errorNum, {String? errorMessage}) {
  //   if (errorNum == null) {
  //     return null;
  //   }

  //   if (errorNum <= 0) {
  //     return null;
  //   }

  //   // 使用ExportErrorType的fromValue方法获取对应的错误类型
  //   final errorType = ExportErrorType.fromValue(errorNum);
  //   // 当错误类型为磁盘空间不足时，使用 errorMessage
  //   if (errorType == ExportErrorType.diskSpaceNotEnough &&
  //       errorMessage != null) {
  //     return errorMessage;
  //   }
  //   return errorType.message;
  // }
}
