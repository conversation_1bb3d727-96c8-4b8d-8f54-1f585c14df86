import 'dart:async';
import 'dart:io';

import 'package:cross_file/cross_file.dart';
import 'package:flutter/material.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/domain/enums/sort_option.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/message_to_unity/message_to_unity.dart';
import 'package:turing_art/datalayer/domain/models/ops_operation/ops_operation.dart';
import 'package:turing_art/datalayer/domain/models/preset/preset_item.dart';
import 'package:turing_art/datalayer/domain/models/project_info/project_info.dart';
import 'package:turing_art/datalayer/domain/models/user/user.dart';
import 'package:turing_art/datalayer/repository/auth_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/photo_thumbnail_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/update_event.dart';
import 'package:turing_art/datalayer/repository/version_intro_repository.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/providers/workspace_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handler_chain.dart';
import 'package:turing_art/ui/common/project_create_select_handler/project_create_select_handler_factory.dart';
import 'package:turing_art/ui/ui_status/process_files_ui_status.dart';
import 'package:turing_art/ui/ui_status/select_project_error_type.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/ui/project_home/services/cover_thumbnail_service.dart';
import 'package:turing_art/ui/common/drag_event_handler/servers/drag_drop_service.dart';
import 'package:turing_art/core/service/image_selection_service/image_selection_service.dart';
import 'package:turing_art/ui/project_home/use_case/migrat_project_use_case.dart';
import 'package:turing_art/ui/project_home/widgets/pc/project_home_pc_grid_calculator.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/ui/use_case/project/project_usecase_provider.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

import '../../../core/external_message/external_message_manager.dart';
import '../../../core/external_message/handler/external_message_result.dart';
import '../../../core/external_message/handler/import_project_message_handler.dart';
import '../../../core/external_message/handler/open_project_message_handler.dart';
import '../../../core/external_message/handler/smart_message_handler.dart';
import '../../../core/tapj/history_file_manager.dart';
import '../services/import_project_dialog_service.dart';

class HomeViewModel extends ChangeNotifier {
  HomeViewModel(
    this._navigator,
    this._authRepository,
    this._projectRepository,
    this._projectUseCase,
    this._unityUseCase,
    this._currentUserRepository,
    this._projectStateProvider,
    this._unityController,
    this._photoThumbnailRepository,
    this._versionIntroRepository,
    this._workspaceUseCase,
    this._noviceGuideManager,
    this._mediaRepository,
  ) {
    _init();
    // 初始化责任链工厂
    _projectHandlerChainFactory = ProjectCreateSelectHandlerFactory(
      projectUseCase: _projectUseCase,
      unityUseCase: _unityUseCase,
      workspaceUseCase: _workspaceUseCase,
      projectStateProvider: _projectStateProvider,
      unityController: _unityController,
      currentUserRepository: _currentUserRepository,
      mediaRepository: _mediaRepository,
      projectRepository: _projectRepository,
    );
    _initExternalMessageManager();
  }
  final NavigatorService _navigator;
  final AuthRepository _authRepository;
  final ProjectRepository _projectRepository;
  final ProjectUseCaseProvider _projectUseCase;
  final UnityUseCaseProvider _unityUseCase;
  final CurrentUserRepository _currentUserRepository;
  final ProjectStateProvider _projectStateProvider;
  final WorkspaceUseCaseProvider _workspaceUseCase;
  final UnityController _unityController;
  final PhotoThumbnailRepository _photoThumbnailRepository;
  final VersionIntroRepository _versionIntroRepository;
  final ImageSelectionService _imageSelectionService =
      ImageSelectionService.forPlatform();
  final DragDropService _dragDropService = DragDropService.forPlatform();
  final MediaRepository _mediaRepository;
  CoverThumbnailService get coverThumbnailService =>
      _coverThumbnailService ??= CoverThumbnailService(
        _unityController,
        _photoThumbnailRepository,
      );
  CoverThumbnailService? _coverThumbnailService;

  // 常量
  static const double maxImageDimension = ImageConstants.maxDimension;
  static const int maxImageQuality = ImageConstants.quality;

  final NoviceGuideManager _noviceGuideManager;
  NoviceGuideManager get noviceGuideManager => _noviceGuideManager;

  // 外部消息管理器
  late final ExternalMessageManager _externalMessageManager;
  // 历史文件管理器
  late final HistoryFileManager _historyFileManager;

  bool _isProcessing = false;
  bool get isProcessing => _isProcessing;

  bool _isPicking = false;

  // 当前用户的所有项目
  List<ProjectInfo> _projects = <ProjectInfo>[];
  List<ProjectInfo> get projects => _projects;

  // 当前用户是否有项目
  bool get hasProjects => _projects.isNotEmpty;

  // 添加 Unity 等待状态
  bool _isWaitingForUnity = false;
  bool get isWaitingForUnity => _isWaitingForUnity;

  // 批量处理标识
  bool _isBatchProcessing = false;
  bool get isBatchProcessing => _isBatchProcessing;

  // 创建工程标识
  bool _isCreateNewProject = false;
  bool get isCreateNewProject => _isCreateNewProject;

  int _itemsPerRow = 0;
  int get itemsPerRow => _itemsPerRow;

  double _sideMargin = 0;
  double get sideMargin => _sideMargin;

  double _itemWidth = 0;
  double get itemWidth => _itemWidth;

  double _itemHeight = 0;
  double get itemHeight => _itemHeight;

  // 获取当前选中的个数
  int get mutiCount => _batchProcessedProjectIds.length;

  // 批量处理的项目ID列表
  List<String> _batchProcessedProjectIds = <String>[];
  List<String> get batchProcessedProjectIds => _batchProcessedProjectIds;

  // 从 UserPreferencesService 加载排序选项
  SortOption get currentSortOption => UserPreferencesService.getSortOption();

  // 数据仓库更新的通知
  late StreamSubscription<UpdateEvent<String>> _dataUpdateSubscription;
  bool get isInEditMode => _projectStateProvider.isEditing;

  // 添加项目处理链工厂
  late final ProjectCreateSelectHandlerFactory _projectHandlerChainFactory;
  final Set<String> _migrationProjectIds = <String>{};

  // 初始化
  Future<void> _init() async {
    try {
      await refreshToken();
      await loadProjects();
      initialSubscription();
    } on Exception catch (e) {
      PGLog.e('初始化失败: $e');
    }
  }

  /// 初始化信号更新
  void initialSubscription() {
    // 订阅更新
    _dataUpdateSubscription =
        _projectRepository.dataUpdates.listen((UpdateEvent<String> event) {
      PGLog.d('项目${event.updated}发生了${event.type}操作');
      if (isInEditMode) {
        return;
      }
      PGLog.d('项目${event.updated}发生了${event.type}操作, 触发界面刷新或数据同步');
      loadProjects();
    });
  }

  /// 使用责任链模式处理选中的图片文件（新入口，保留原有方法）
  /// 1.创建工程
  /// 2.发送导入工作区信息到Unity
  /// 3.发送导出设置信息到Unity
  /// 4.导航到编辑页面
  Future<ProcessFilesUiStatus> processSelectedFiles2(
    List<File> files,
    String? projectName,
    String? projectId, {
    bool shouldNavigateToEdit = true,
    Map<String, List<int>>? historyFiles,
    ImportProjectData? importData,
    bool isOverwriteMode = false,
  }) async {
    PGLog.d('HomeViewModel - processSelectedFiles2 - 使用责任链模式');
    if (_isProcessing) {
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.processing,
      );
    }

    PGLog.d('HomeViewModel - processSelectedFiles2 - start');
    try {
      _setProcessing(true);
      _isCreateNewProject = !isOverwriteMode; // 覆盖模式下不是创建新项目

      // 创建项目处理上下文
      final handlerContext = ProjectCreateSelectHandlerContext(
        files: files,
        projectName: projectName,
        projectId: projectId,
        historyFiles: historyFiles,
        fileList: importData?.fileList,
        isOverwriteMode: isOverwriteMode,
      );

      // 1. 使用工厂创建责任链 - 创建项目部分（覆盖模式下会跳过实际创建）
      final createProjectChain =
          _projectHandlerChainFactory.createRetouchedProjectChain();
      final createResult = await createProjectChain.process(handlerContext);
      if (createResult is ErrorStatus) {
        _resetCreateAndProcessing();
        return createResult;
      }

      // 2. 检查Unity是否初始化完成
      if (!_unityController.isInitialized) {
        _isWaitingForUnity = true;
        notifyListeners();
        // 避免阻塞UI线程
        _unityController.isReady.then((_) {
          _isWaitingForUnity = false;
          notifyListeners();
          // 继续处理后续操作(保持原有逻辑不等待)
          _afterInitUnityToEdit(handlerContext, shouldNavigateToEdit);
        });
      }
      _afterInitUnityToEdit(handlerContext, shouldNavigateToEdit);

      return const ProcessFilesUiStatus.success();
    } on Exception catch (e) {
      PGLog.e('处理文件时出错: $e');
      _resetCreateAndProcessing();
      return const ProcessFilesUiStatus.error(
        errorType: ProcessFilesErrorType.other,
      );
    } finally {
      _setProcessing(false);
    }
  }

  Future<void> _afterInitUnityToEdit(
      ProjectCreateSelectHandlerContext handlerContext,
      bool shouldNavigateToEdit) async {
    // 3. 创建工程成功，设置工作区和导入图片
    final afterUnityInitChain = _projectHandlerChainFactory
        .createAfterInitUnityToSwitchWorkspaceChain();
    final afterInitResult = await afterUnityInitChain.process(handlerContext);
    if (afterInitResult is SuccessStatus) {
      // 导航到编辑页面
      if (shouldNavigateToEdit &&
          handlerContext.createdProjectId != null &&
          handlerContext.createdProjectId!.isNotEmpty) {
        // 设置当前项目ID
        _noviceGuideManager
            .setSelectedProjectId(handlerContext.createdProjectId ?? '');
        _projectStateProvider
            .editProjectId(handlerContext.createdProjectId ?? '');
        await _navigator.navigateToEdit(handlerContext.createdProjectId ?? '');
      }
    }
    _resetCreateAndProcessing();
  }

  /// TODO: pzy PC端跳转到AIGC编辑场景, 临时方法用于测试，后续删除
  Future<void> jumpToAIGCEidtScene() async {
    await _navigator.navigateToAigcEditing('');
  }

  void _resetCreateAndProcessing() {
    _isCreateNewProject = false;
    _setProcessing(false);
  }

  /// 从相册选择图片
  Future<DealImageFilesResult?> pickImagesFromGallery() async {
    if (_isPicking) {
      return null;
    }
    _isPicking = true;
    final result = await _imageSelectionService.pickImagesFromGallery();
    _isPicking = false;
    return result;
  }

  /// 从文件系统选择图片
  Future<DealImageFilesResult?> pickImagesFromFiles() async {
    if (_isPicking) {
      return null;
    }
    _isPicking = true;
    final result = await _imageSelectionService.pickImagesFromFiles();
    _isPicking = false;
    return result;
  }

  // 添加新方法处理拖放文件
  Future<DealImageFilesResult?> processDroppedFiles(List<XFile> files) async {
    _setProcessing(true);
    final t = DateTime.now().millisecondsSinceEpoch;
    final result = _dragDropService.processDroppedFiles(files);
    PGLog.d(
        'processDroppedFiles - ${DateTime.now().millisecondsSinceEpoch - t}ms');
    _setProcessing(false);
    return result;
  }

  // 刷新token
  Future<void> refreshToken() async {
    if (!await _currentUserRepository.needToRefreshToken()) {
      return;
    }
    final User? curUser = _currentUserRepository.user;
    if (curUser == null) {
      return;
    }
    final User? newUser = await _authRepository.refreshUserToken(curUser);
    if (newUser != null) {
      // 刷新token，只关注token，store用缓存
      _currentUserRepository.syncCurrentUser(
        newUser,
        _currentUserRepository.store,
      );
    }
  }

  // 获取项目列表
  Future<void> loadProjects() async {
    _projects = await _projectUseCase.loadEditProjects.invoke();
    PGLog.d('loadProjects count: ${_projects.length}, 通知列表刷新');
    notifyListeners();
  }

  // 项目重命名
  void renameProject(String projectId, String newName) {
    Future.microtask(() async {
      await _projectRepository.renameProject(projectId, newName);
      _sendRenameWorkspacesToUnity(projectId);
    });
  }

  void updateProject(ProjectInfo info) {
    _projectRepository.updateProject(info);
  }

  Future<bool> deleteProject(String projectId) async {
    return await _projectUseCase.deleteProject.invoke(projectId);
  }

  Future<SelectProjectErrorType?> trySelectedProject(int idx) async {
    if (idx < 0 || idx >= _projects.length) {
      PGLog.e('无效的项目索引: $idx');
      return SelectProjectErrorType.indexError;
    }
    if (_isBatchProcessing) {
      return null;
    } else {
      final result =
          await _workspaceUseCase.openWorkspaceDiskSpaceGuard.invoke();
      if (!result) {
        return SelectProjectErrorType.diskSpace;
      }

      // 判断工程该工程是否需要进行迁移
      final project = _projects[idx];
      final migrator = MigratProjectUseCase(_projectRepository);
      final status = await migrator.invoke(project.uuid);
      if (status == MigrationStatus.failed) {
        return SelectProjectErrorType.migrationFailed;
      }
      if (status == MigrationStatus.success) {
        // 保存本次迁移的id
        _migrationProjectIds.add(project.uuid);
      }
      return null;
    }
  }

  void selectedProject(int idx) {
    if (idx < 0 || idx >= _projects.length) {
      PGLog.e('无效的项目索引: $idx');
      return;
    }

    final ProjectInfo project = _projects[idx];

    selectedProjectInfo(project);
  }

  void selectedProjectInfo(ProjectInfo project) {
    // 设置当前选中的工程ID
    _noviceGuideManager.setSelectedProjectId(project.uuid);
    if (_isBatchProcessing) {
      if (_batchProcessedProjectIds.contains(project.uuid)) {
        _batchProcessedProjectIds.remove(project.uuid);
      } else {
        _batchProcessedProjectIds.add(project.uuid);
      }
      notifyListeners();
      return;
    }

    _setProcessing(true);
    batchProcessEnd();

    // 使用责任链模式处理选择项目
    _navigateToEditProcess(project.uuid);

    // 更新项目打开时间（延迟更新，避免在导航前刷新列表）
    Future.delayed(const Duration(seconds: 3), () {
      final ProjectInfo newProject =
          project.copyWith(updateDate: DateTime.now());
      updateProject(newProject);
    });
  }

  void _navigateToEditProcess(String projectId) {
    // 创建项目处理上下文
    final handlerContext = ProjectCreateSelectHandlerContext(
      files: [], // 选择项目时不需要文件
      projectId: projectId, // 设置项目ID
      isMigrated: _migrationProjectIds.contains(projectId), // 是否迁移
    );

    // 使用工厂创建选择项目处理链
    final chain =
        _projectHandlerChainFactory.createSelectProjectSwitchWorkspaceChain();

    // 执行责任链
    chain.process(handlerContext).then((_) {
      // 设置当前项目ID
      _projectStateProvider.editProjectId(projectId);

      // 导航到编辑页面
      _navigator.navigateToEdit(projectId).then((_) {
        // 进入工程后下次再开启就不算迁移流程，需要删除flag
        _migrationProjectIds.remove(projectId);
        _setProcessing(false);
      });
    });
  }

  /// 开启批量处理模式
  void batchProcess() {
    _isBatchProcessing = true;
    _batchProcessedProjectIds = <String>[];
    notifyListeners();
  }

  /// 结束批量处理模式
  void batchProcessEnd() {
    if (_isBatchProcessing) {
      _isBatchProcessing = false;
      _batchProcessedProjectIds = <String>[];
      notifyListeners();
    }
  }

  Future<bool> batchExportWithPreset(PresetItem preset, String path) async {
    final projectIds = _batchProcessedProjectIds;
    if (projectIds.isEmpty) {
      return false;
    }
    final MessageToUnity? message = await _unityUseCase.exportWithPreset.invoke(
      projectIds,
      preset,
      path,
    );
    if (message != null) {
      _unityController.sendMessage(message);
      return true;
    }
    return false;
  }

  /// 选中所有项目
  /// 假设当前没有选中任何项目，则选中全部
  /// 假设当前有部分被选中，则选中全部
  /// 假设当前全部被选中，则取消全部选中
  void batchAllPreset() {
    if (_batchProcessedProjectIds.length == _projects.length) {
      // 全部取消选中
      _batchProcessedProjectIds.clear();
    } else {
      // 选中全部
      _batchProcessedProjectIds =
          _projects.map((ProjectInfo project) => project.uuid).toList();
    }
    notifyListeners();
  }

  /// 查询项目是否被选中
  bool queryProjectInBatch(String projectId) {
    return _batchProcessedProjectIds.contains(projectId);
  }

  // 设置处理状态
  void _setProcessing(bool value) {
    _isProcessing = value;
  }

  // 发送工作区改名信息到Unity
  Future<void> _sendRenameWorkspacesToUnity(String projectId) async {
    try {
      final MessageToUnity? message =
          await _unityUseCase.renameWorkspace.invoke(projectId);
      if (message != null) {
        final result = await _unityController.sendMessage(message);
        PGLog.e('发送工作区改名信息result : $result');
      }
    } on Exception catch (e) {
      PGLog.e('发送工作区改名信息到Unity失败: $e');
    }
  }

  // 计算PC端网格
  void calculatePcGrid(double width) {
    final GridLayoutInfo layout = ProjectHomePCGridCalculator.calculate(width);
    PGLog.e('calculatePcGrid _itemsPerRow ');
    _itemsPerRow = layout.itemsPerRow;
    _sideMargin = layout.sideMargin;
    _itemWidth = layout.itemWidth;
    _itemHeight = layout.itemHeight;
  }

  void setSortOption(SortOption option) {
    UserPreferencesService.setSortOption(option);
    notifyListeners();
  }

  /// 获取版本介绍数据
  Future<List<OperationActivity>> getVersionIntroData() async {
    try {
      return await _versionIntroRepository.getVersionIntroInfo();
    } catch (e) {
      PGLog.e('获取版本介绍信息失败: $e');
      return [];
    }
  }

  @override
  void dispose() {
    _dataUpdateSubscription.cancel();
    // 清理外部消息管理器
    _externalMessageManager.dispose();
    super.dispose();
  }

  /// 初始化外部消息管理器
  void _initExternalMessageManager() {
    _externalMessageManager = ExternalMessageManager();

    // 初始化历史文件管理器
    _historyFileManager = HistoryFileManager(
      currentUserRepository: _currentUserRepository,
      projectRepository: _projectRepository,
    );

    // 初始化消息管理器
    _externalMessageManager.initialize(
      onMessageResult: (result) {
        // 处理消息结果，可以在这里添加额外的业务逻辑
        PGLog.d('外部消息处理结果: $result');
      },
      onMessageReceived: () {
        // 有新消息到达时通知UI
        notifyListeners();
      },
    );

    // 注册导入项目处理器
    _externalMessageManager.registerHandler(
      ImportProjectMessageHandler(
        onImportProject: _handleImportProject,
      ),
    );

    // 注册智能导入处理器
    _externalMessageManager.registerHandler(
      SmartMessageHandler(
        onImportProject: _handleImportProject,
      ),
    );

    // 注册打开项目处理器
    _externalMessageManager.registerHandler(
      OpenProjectMessageHandler(
        onOpenProject: _handleOpenProject,
      ),
    );
  }

  /// 检查是否有待处理的外部消息
  bool get hasPendingExternalMessages =>
      _externalMessageManager.hasPendingExternalMessages;

  /// 处理所有等待中的外部消息（在有context可用时调用）
  Future<void> processPendingExternalMessages(BuildContext context) async {
    await _externalMessageManager.processPendingMessages(context);
  }

  /// 处理导入项目消息
  Future<ExternalMessageResult> _handleImportProject(
    BuildContext context,
    ImportProjectData data,
    Map<String, List<int>>? historyFiles,
  ) async {
    try {
      PGLog.d(
          '开始处理导入项目: projectId=${data.projectId}, files=${data.getAllFilePaths().length}');

      // 使用use_case检查是否可以覆盖项目
      final bool canOverwrite =
          await _projectUseCase.checkProjectOverwrite.invoke(data.projectId);

      // 检查context是否仍然有效
      if (!context.mounted) {
        PGLog.w('Context已失效，取消导入项目操作');
        return const ExternalMessageError('导入项目失败: Context已失效');
      }

      // 将文件路径转换为File对象
      final files = data.getAllFilePaths().map((path) => File(path)).toList();

      if (!data.newProject && canOverwrite) {
        // 显示确认对话框
        PGLog.d('项目已存在，显示确认对话框');

        final success = await ImportProjectDialogService
            .showImportProjectChoiceDialogWithAction(
          context,
          onCreateNew: () async {
            // 新建工程操作
            PGLog.d('用户选择新建工程，开始执行新建逻辑');
            if (!context.mounted) {
              return false;
            }

            final result = await processSelectedFiles2(
              files,
              data.projectName,
              null, // 项目存在且可覆盖，传入空Id以创建新项目
              shouldNavigateToEdit: data.autoNavigate,
              historyFiles: historyFiles,
              importData: data,
            );

            return result is SuccessStatus;
          },
          onOverwrite: () async {
            // 确保projectId不为空（进入覆盖逻辑说明项目已存在）
            final projectId = data.projectId;
            if (projectId == null || projectId.isEmpty) {
              PGLog.e('项目ID为空，无法执行覆盖操作');
              return false;
            }

            try {
              PGLog.d('开始覆盖项目：使用责任链模式处理选版状态，项目ID: $projectId');

              // 将文件路径转换为File对象（虽然覆盖模式不会实际导入）
              final files =
                  data.getAllFilePaths().map((path) => File(path)).toList();

              // 调用processSelectedFiles2，使用覆盖模式
              final result = await processSelectedFiles2(
                files,
                data.projectName,
                projectId, // 传入现有项目ID进行选版覆盖
                shouldNavigateToEdit: data.autoNavigate,
                historyFiles: historyFiles,
                importData: data,
                isOverwriteMode: true, // 设置覆盖模式
              );

              PGLog.d('覆盖项目完成，结果: $result');
              return result is SuccessStatus;
            } catch (e) {
              PGLog.e('覆盖项目失败: $e');
              return false;
            }
          },
        );

        if (success) {
          return ExternalMessageSuccess(
              '导入项目成功: ${data.projectName ?? "未命名项目"}');
        } else {
          return const ExternalMessageError('导入项目失败或被用户取消');
        }
      } else {
        // 直接创建新项目
        PGLog.d('项目不存在或强制创建新项目');
        // 项目不存在时，优先使用传入的projectId，否则使用null创建新工程ID
        final targetProjectId = canOverwrite ? null : data.projectId;

        // 显示Loading
        PGDialog.showLoading();
        try {
          final result = await processSelectedFiles2(
            files,
            data.projectName,
            targetProjectId,
            shouldNavigateToEdit: data.autoNavigate,
            historyFiles: historyFiles,
            importData: data,
          );

          if (result is SuccessStatus) {
            return ExternalMessageSuccess(
                '导入项目成功: ${data.projectName ?? "未命名项目"}');
          } else {
            return ExternalMessageError('导入项目失败: ${result.toString()}');
          }
        } catch (e) {
          PGLog.e('直接创建项目操作失败: $e');
          return ExternalMessageError('导入项目失败: $e');
        } finally {
          // 隐藏Loading
          if (context.mounted) {
            PGDialog.dismiss(tag: DialogTags.loading);
          }
        }
      }
    } catch (e) {
      PGLog.e('处理导入项目消息时发生错误: $e');
      return ExternalMessageError('导入项目失败: $e');
    }
  }

  /// 处理打开项目消息
  Future<ExternalMessageResult> _handleOpenProject(
    BuildContext context,
    OpenProjectData data,
  ) async {
    return const ExternalMessageError('open协议暂未实现');
  }

  // 发送从历史记录导入预设信息到Unity
  Future<void> _sendImportPresetFromHistoryToUnity(
    String projectId,
    Map<String, String> fileIdToHistoryIdMap,
  ) async {
    try {
      if (fileIdToHistoryIdMap.isEmpty) {
        PGLog.d('没有需要导入的历史预设数据');
        return;
      }

      PGLog.d('准备发送ImportPresetFromHistory消息，映射数据: $fileIdToHistoryIdMap');

      final MessageToUnity? message = await _unityUseCase
          .importPresetFromHistory
          .invoke(projectId, fileIdToHistoryIdMap);

      if (message != null) {
        final result = await _unityController.sendMessage(message);
        PGLog.d('发送ImportPresetFromHistory消息结果: $result');
      }
    } catch (e) {
      PGLog.e('发送ImportPresetFromHistory消息到Unity失败: $e');
    }
  }

  /// 处理导入项目（tapj文件）
  Future<void> handleImportProject(
    BuildContext context,
    ImportProjectData data,
    Map<String, List<int>>? historyFiles,
  ) async {
    await _handleImportProject(context, data, historyFiles);
  }
}
