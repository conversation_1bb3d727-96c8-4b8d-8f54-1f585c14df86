import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:turing_art/core/manager/novice_guide_manager.dart';
import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/core/unity/unity_messages.dart';
import 'package:turing_art/datalayer/domain/models/message_from_unity/message_from_unity.dart';
import 'package:turing_art/datalayer/domain/models/setting/setting_constants.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/providers/export_project_provider.dart';
import 'package:turing_art/ui/export_result/use_case/export_usecase_provider.dart';
import 'package:turing_art/ui/setting/provider/current_device_information_provider.dart';
import 'package:turing_art/ui/setting/use_case/fetch_settings_use_case.dart';
import 'package:turing_art/ui/unity/use_case/get_cache_disk_use_cache.dart';
import 'package:turing_art/ui/unity/use_case/handle_current_device_information_use_case.dart';
import 'package:turing_art/ui/unity/use_case/handle_dynamic_encryption_params_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_encrypted_lut_params_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_completed_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_failed_use_case.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_project_use_case.dart';
import 'package:turing_art/ui/unity/use_case/handle_export_token_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_local_key_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_novice_guide_completed_usecase.dart';
import 'package:turing_art/ui/unity/use_case/handle_novice_guide_usecase.dart';
import 'package:turing_art/ui/unity/use_case/unity_usecase_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

class PreloadUnityViewModel extends ChangeNotifier {
  final UnityUseCaseProvider _useCaseProvider;
  final UnityController _unityController;
  final ExportUseCaseProvider _exportUseCaseProvider;
  final SettingRepository _settingRepository;
  final CurrentDeviceInformationProvider _currentDeviceInformationProvider;
  final ExportProjectProvider _exportProjectProvider;
  late final HandleEncryptedLutParamsUseCase _encryptedLutHandler;
  late final HandleExportCompletedUseCase _exportCompletedHandler;
  late final HandleExportTokenUseCase _exportTokenHandler;
  late final HandleDynamicEncryptionParamsUseCase _dynamicEncryptionHandler;
  late final HandleExportFailedUseCase _exportFailedHandler;
  late final GetCacheDiskSpaceUseCase _getCacheDiskSpaceHandler;
  late final HandleExportProjectUseCase _exportProjectHandler;
  late final HandleCurrentDeviceInformationUseCase
      _currentDeviceInformationHandler;
  late final HandleNoviceGuideUseCase _noviceGuideHandler;
  late final HandleNoviceGuideCompletedUseCase _noviceGuideCompletedHandler;
  late final HandleLocalKeyUseCase _getLocalKeyHandler;
  final NoviceGuideManager _noviceGuideManager;

  PreloadUnityViewModel({
    required UnityUseCaseProvider useCaseProvider,
    required UnityController unityController,
    required ExportUseCaseProvider exportUseCaseProvider,
    required SettingRepository settingRepository,
    required CurrentDeviceInformationProvider currentDeviceInformationProvider,
    required NoviceGuideManager noviceGuideManager,
    required ExportProjectProvider exportProjectProvider,
  })  : _useCaseProvider = useCaseProvider,
        _unityController = unityController,
        _exportUseCaseProvider = exportUseCaseProvider,
        _settingRepository = settingRepository,
        _currentDeviceInformationProvider = currentDeviceInformationProvider,
        _noviceGuideManager = noviceGuideManager,
        _exportProjectProvider = exportProjectProvider {
    _initialSubscription();
    _initMessageHandlersUseCase();
  }

  /// 初始化消息处理器
  void _initMessageHandlersUseCase() {
    _exportTokenHandler = _useCaseProvider.createExportTokenHandler(
      _unityController,
      _exportUseCaseProvider,
    );
    _dynamicEncryptionHandler =
        _useCaseProvider.createDynamicEncryptionHandler(_unityController);
    _encryptedLutHandler = _useCaseProvider.createEncryptedLutHandler(
      _unityController,
      _exportUseCaseProvider,
    );
    _exportCompletedHandler =
        _useCaseProvider.createExportCompletedHandler(_exportUseCaseProvider);
    _exportFailedHandler = _useCaseProvider.createExportFailedHandler(
      _exportProjectProvider,
    );
    _getCacheDiskSpaceHandler =
        _useCaseProvider.createGetCacheDiskSpaceHandler(_unityController);
    _currentDeviceInformationHandler =
        _useCaseProvider.createCurrentDeviceInformationHandler();
    _noviceGuideHandler = _useCaseProvider.createNoviceGuideHandler(
      _unityController,
      _noviceGuideManager,
    );
    _noviceGuideCompletedHandler =
        _useCaseProvider.createGuideCompletedHandler(_noviceGuideManager);
    _exportProjectHandler = _useCaseProvider.createExportProjectHandler(
      _exportProjectProvider,
    );
    _getLocalKeyHandler =
        _useCaseProvider.createLocalKeyHandler(_unityController);
  }

  /// 初始化信号更新
  void _initialSubscription() async {
    PGLog.d('PreloadUnityViewModel.initialized waiting');
    await _unityController.initialized;
    PGLog.d('PreloadUnityViewModel.initialized success');
    // 再设置一次Unity隐藏，触发焦点设置，将焦点设置为Flutter窗口，保证外壳正常输入
    await _unityController.setUnityVisibility(visible: false);
    PGLog.d('PreloadUnityViewModel.setUnityVisibility false');

    _sendLoginMessages();
    _sendFetchDeviceInformationMessages();
  }

  void onUnityCreated() {
    if (Platform.isWindows) {
      PGLog.d('PreloadUnityViewModel - Platform is Windows');
    }
    PGLog.d('PreloadUnityViewModel onUnityCreated');
  }

  void updateLatestMessage(String message) {
    PGLog.d('PreloadUnityViewModel - updateLatestMessage: $message');
  }

  Future<void> _sendLoginMessages() async {
    PGLog.d('PreloadUnityViewModel - sending login messages');

    try {
      // 发送登录消息
      final loginMessage =
          await _useCaseProvider.generateLoginUnityMessage.invoke();
      if (loginMessage != null) {
        await _unityController.sendMessage(loginMessage);
        _unityController.login();
        PGLog.d('PreloadUnityViewModel - login success');
      }
    } catch (e) {
      PGLog.e('Failed to send login messages: $e');
    }
  }

  Future<void> _sendFetchDeviceInformationMessages() async {
    PGLog.d(
        'PreloadUnityViewModel - sending fetch device information messages');
    try {
      // 发送登录消息
      final fetchDeviceInformationMessage =
          _useCaseProvider.fetchDeviceInformationMessage.invoke();
      await _unityController.sendMessage(fetchDeviceInformationMessage);
      PGLog.d(
          'PreloadUnityViewModel - send fetch device information messages success');
    } catch (e) {
      PGLog.e('Failed to send fetch device information messages: $e');
    }
  }

  Future<void> _sendSyncResolutionConfigMessages() async {
    try {
      final settingConfig = await FetchSettingsUseCase(
        settingRepository: _settingRepository,
        deviceInformationProvider: _currentDeviceInformationProvider,
      ).invoke();
      // 找到预览设置类别中的预览尺寸设置项
      final previewSizeItem = settingConfig.categories
          .firstWhere(
            (category) => category.key == SettingCategoryConstant.preview,
          )
          .items
          .firstWhere(
            (item) => item.key == SettingKeyConstant.previewSize,
          );

      // 找到当前选中的选项
      final selectedChoice = previewSizeItem.choices.firstWhere(
        (choice) => choice.isSelected,
        orElse: () => previewSizeItem.choices.first,
      );

      // 获取当前选中的预览尺寸
      final previewSize = int.tryParse(selectedChoice.value);
      if (previewSize == null) {
        return;
      }

      // 创建并发送消息到Unity
      final message =
          _useCaseProvider.createSetupResolutionHandler().invoke(previewSize);
      await _unityController.sendMessage(message);
    } catch (e) {
      PGLog.e('Failed to send sync resolution config messages: $e');
    }
  }

  Future<void> onUnityMessage(MessageFromUnity message) async {
    PGLog.d('PreloadUnityViewModel: 收到Unity消息: ${message.method}');

    if (message.method == UnityMessage.getExportTokenAsync.value) {
      _exportTokenHandler.invoke(message);
    } else if (message.method ==
        UnityMessage.getDynamicEncryptionParams.value) {
      _dynamicEncryptionHandler.invoke(message);
    } else if (message.method == UnityMessage.getEncryptedLutParams.value) {
      await _encryptedLutHandler.invoke(message);
    } else if (message.method == UnityMessage.onExportItemCompleted.value) {
      await _exportCompletedHandler.invoke(message);
    } else if (message.method == UnityMessage.onExportItemUpdated.value) {
      await _exportProjectHandler.invoke(message);
    } else if (message.method == UnityMessage.onExportBatchItemsUpdated.value) {
      await _exportProjectHandler.invokeBatch(message);
    } else if (message.method == UnityMessage.onExportTaskFailed.value) {
      // await _exportFailedHandler.invoke(message);
      await _exportProjectHandler.invokeReloadProjectRecords(message);
    } else if (message.method == UnityMessage.onReloadExportItems.value) {
      await _exportProjectHandler.invokeReloadProjectRecords(message);
    } else if (message.method == UnityMessage.getCacheDiskSpaceAsync.value) {
      await _getCacheDiskSpaceHandler.invoke(message);
    } else if (message.method == UnityMessage.currentDeviceInformation.value) {
      _currentDeviceInformationHandler.invoke(message);
      _sendSyncResolutionConfigMessages();
    } else if (message.method == UnityMessage.getProjectGuideStepInfo.value) {
      await _noviceGuideHandler.invoke(message);
    } else if (message.method == UnityMessage.onProjectGuideStepInfo.value) {
      await _noviceGuideCompletedHandler.invoke(message);
    } else if (message.method == UnityMessage.workspaceChanged.value) {
      // 工作区文件发生改变
      _useCaseProvider.workspaceChanged.invoke(message);
    } else if (message.method == UnityMessage.getLocalKey.value) {
      await _getLocalKeyHandler.invoke(message);
    } else {
      PGLog.d('未处理的Unity消息类型: ${message.method}');
    }
  }
}
