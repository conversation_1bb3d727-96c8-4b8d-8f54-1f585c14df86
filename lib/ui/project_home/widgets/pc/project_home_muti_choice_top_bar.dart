import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';
import 'package:turing_art/ui/core/widgets/platform_mouse_region.dart';

import '../../view_models/home_view_model.dart';

class ProjectHomeMutiChoiceTopBar extends StatefulWidget {
  const ProjectHomeMutiChoiceTopBar({
    super.key,
    this.batchPresetClick,
    this.allClick,
    this.cancelClick,
    required this.sideMargin,
  });

  final VoidCallback? batchPresetClick;
  final VoidCallback? allClick;
  final VoidCallback? cancelClick;
  final double sideMargin;

  @override
  State<ProjectHomeMutiChoiceTopBar> createState() =>
      _ProjectHomeMutiChoiceTopBarState();
}

class _ProjectHomeMutiChoiceTopBarState
    extends State<ProjectHomeMutiChoiceTopBar> {
  bool _isConfirmHovered = false;
  bool _hoveredBack = false;

  @override
  Widget build(BuildContext context) {
    final viewModel = context.watch<HomeViewModel>();
    final mutiCount = viewModel.mutiCount;
    final totalCount = viewModel.projects.length;
    return Container(
      height: 56,
      color: const Color(0xFF0D0D0D),
      child: Row(
        children: [
          SizedBox(width: widget.sideMargin),
          GestureDetector(
            onTap: widget.cancelClick,
            child: PlatformMouseRegion(
              cursor: SystemMouseCursors.click,
              onEnter: (_) => setState(() => _hoveredBack = true),
              onExit: (_) => setState(() => _hoveredBack = false),
              child: Container(
                width: 102,
                height: 40,
                decoration: BoxDecoration(
                  color: _hoveredBack
                      ? Colors.white.withOpacity(0.1)
                      : Colors.white.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/icons/home_pc_top_back.png',
                      color: Colors.white,
                      width: 20,
                      height: 20,
                    ),
                    const SizedBox(width: 4),
                    const Text(
                      '批量修图',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                        fontFamily: Fonts.fontFamilySF,
                        fontWeight: Fonts.semiBold,
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                ),
              ),
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.only(left: 0, top: 0),
            child: Row(
              children: [
                Text(
                  '已选 $mutiCount',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: Fonts.medium,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '/ 总共$totalCount个项目',
                  style: TextStyle(
                    color: const Color(0xFFEBF2F5).withAlpha(150),
                    fontSize: 12,
                    fontWeight: Fonts.regular,
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          PlatformMouseRegion(
            onEnter: (_) => setState(() => _isConfirmHovered = true),
            onExit: (_) => setState(() => _isConfirmHovered = false),
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: mutiCount > 0 ? widget.batchPresetClick : null,
              child: Opacity(
                opacity: mutiCount > 0 ? 1.0 : 0.6,
                child: Container(
                  height: 32,
                  width: 120,
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: mutiCount > 0
                        ? _isConfirmHovered
                            ? const Color(0xFF8253FF)
                            : const Color(0xFF7B4AFF)
                        : const Color(0xFF7B4AFF),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '确认',
                        style: TextStyle(
                          fontSize: 12,
                          color: mutiCount > 0
                              ? const Color(0xFFFFFFFF)
                              : const Color(0xFFEBF2F5).withOpacity(0.6),
                          fontFamily: Fonts.fontFamilySF,
                          fontWeight: Fonts.semiBold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: widget.sideMargin),
        ],
      ),
    );
  }
}
