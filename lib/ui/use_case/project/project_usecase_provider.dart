import 'package:turing_art/core/unity/unity_controller.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/repository/setting_repository.dart';
import 'package:turing_art/ui/export_result/use_case/delete_workspace_use_case.dart';
import 'package:turing_art/ui/export_result/use_case/delete_export_task_use_case.dart';
import 'package:turing_art/ui/export_result/use_case/fetch_export_report_use_case2.dart';
import 'package:turing_art/ui/use_case/project/check_project_overwrite_use_case.dart';
import 'package:turing_art/ui/use_case/project/delete_project_use_case.dart';
import 'package:turing_art/ui/use_case/project/generate_empty_project_use_case.dart';
import 'package:turing_art/ui/use_case/project/generate_project_use_case.dart';
import 'package:turing_art/ui/use_case/project/load_aigc_projects_use_case.dart';
import 'package:turing_art/ui/use_case/project/load_edit_projects_use_case.dart';

import 'delete_project_use_case2.dart';

class ProjectUseCaseProvider {
  final DeleteProjectUseCase2 deleteProject;
  final GenerateEmptyProjectUseCase generateEmptyProject;
  final LoadEditProjectsUseCase loadEditProjects;
  final LoadAigcProjectsUseCase loadAigcProjects;
  final GenerateProjectUseCase generateProject;
  final CheckProjectOverwriteUseCase checkProjectOverwrite;

  ProjectUseCaseProvider({
    required MediaRepository mediaRepository,
    required ProjectRepository projectRepository,
    required CurrentUserRepository currentUserRepository,
    required SettingRepository settingRepository,
    required UnityController unityController,
  })  : deleteProject = DeleteProjectUseCase2(
          projectRepository,
          currentUserRepository,
          DeleteWorkspaceUseCase(unityController),
        ),
        generateEmptyProject = GenerateEmptyProjectUseCase(
          projectRepository,
          currentUserRepository,
          settingRepository,
        ),
        loadEditProjects = LoadEditProjectsUseCase(
          projectRepository,
          currentUserRepository,
        ),
        loadAigcProjects = LoadAigcProjectsUseCase(
          projectRepository,
          currentUserRepository,
        ),
        generateProject = GenerateProjectUseCase(
          mediaRepository,
          projectRepository,
          currentUserRepository,
          settingRepository,
        ),
        checkProjectOverwrite = CheckProjectOverwriteUseCase(
          projectRepository,
          currentUserRepository,
        );
}
