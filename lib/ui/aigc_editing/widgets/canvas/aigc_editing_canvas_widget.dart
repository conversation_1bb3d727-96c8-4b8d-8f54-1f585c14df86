import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/service/share_preferences/shared_preferences_service.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_image_overlay_provider.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_editing_canvas_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_edit_icon_toast.dart';
import 'package:turing_art/ui/aigc_editing/widgets/canvas/aigc_canvas_painter_widget.dart';
import 'package:turing_art/ui/aigc_editing/widgets/canvas/aigc_canvas_state_widget.dart';

/// AIGC编辑画布组件
///
/// 画布区域的基础视图，只起到挂载图片视图的作用
/// 根据新的架构要求简化功能
class AigcEditingCanvasWidget extends StatefulWidget {
  /// 画布宽度
  final double width;

  /// 画布高度
  final double height;

  /// 视图模型（可选）
  final AigcEditingCanvasViewModel? viewModel;

  /// 构造函数
  const AigcEditingCanvasWidget({
    super.key,
    this.width = double.infinity,
    this.height = double.infinity,
    this.viewModel,
  });

  @override
  State<AigcEditingCanvasWidget> createState() =>
      _AigcEditingCanvasWidgetState();
}

class _AigcEditingCanvasWidgetState extends State<AigcEditingCanvasWidget> {
  /// 画布ViewModel
  late AigcEditingCanvasViewModel _canvasViewModel;

  /// 是否正在处理成功或者失败信息
  bool _handlingResultTips = false;

  /// 画布Widget的GlobalKey，用于Toast相对定位
  final GlobalKey _canvasKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    // 初始化ViewModel
    _canvasViewModel =
        widget.viewModel ?? AigcEditingCanvasViewModel.create(context);

    _canvasViewModel.onStateEvent = _handleCanvasStateEvent;
  }

  @override
  void dispose() {
    // 如果是内部创建的ViewModel，需要释放
    if (widget.viewModel == null) {
      _canvasViewModel.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 使用LayoutBuilder获取实际可用空间
        final Size actualSize = _getActualCanvasSize(constraints);
        return Consumer<AigcImageOverlayProvider>(
          builder: (context, imageOverlayProvider, child) {
            // 监听selectedImage变化，当为null时隐藏Toast
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (imageOverlayProvider.isSelectedImageNull()) {
                AigcEditIconToast.dismissIconToast();
              }
            });

            return Container(
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  // border: Border.all(
                  //   color: Colors.white.withOpacity(0.1),
                  //   width: 1,
                  // ),
                ),
                child: Stack(
                  children: [
                    SizedBox(
                      key: _canvasKey,
                      width: widget.width,
                      height: widget.height,
                      child: AigcCanvasPainterWidget(
                        width: actualSize.width,
                        height: actualSize.height,
                        onImageInitialized: _handleImageInitialized,
                      ),
                    ),
                    // 添加状态视图，当backgroundImage为空时显示
                    if (imageOverlayProvider.backgroundImage == null)
                      Positioned.fill(
                        child: AigcCanvasStateWidget(
                          displayMode:
                              imageOverlayProvider.backgroundLoadError != null
                                  ? AigcCanvasStateDisplayMode.image
                                  : AigcCanvasStateDisplayMode.imageText,
                          customText: '图灵精修-添加图片以开始',
                        ),
                      ),
                  ],
                ));
          },
        );
      },
    );
  }

  /// 处理图像初始化完成事件
  void _handleImageInitialized(Size imageSize) {}

  /// 获取画布的实际尺寸
  Size _getActualCanvasSize([BoxConstraints? constraints]) {
    if (!mounted) {
      return const Size(0, 0);
    }

    // 如果提供了约束，优先使用约束计算尺寸
    if (constraints != null) {
      double actualWidth = widget.width == double.infinity
          ? constraints.maxWidth
          : math.min(widget.width, constraints.maxWidth);

      double actualHeight = widget.height == double.infinity
          ? constraints.maxHeight
          : math.min(widget.height, constraints.maxHeight);

      // 确保尺寸有效
      actualWidth = actualWidth > 0 ? actualWidth : 100;
      actualHeight = actualHeight > 0 ? actualHeight : 100;

      return Size(actualWidth, actualHeight);
    }

    // 如果没有提供约束，使用MediaQuery
    if (widget.width == double.infinity || widget.height == double.infinity) {
      final mediaQuery = MediaQuery.of(context);
      final size = mediaQuery.size;

      // 计算实际可用空间
      double actualWidth =
          widget.width == double.infinity ? size.width : widget.width;
      double actualHeight =
          widget.height == double.infinity ? size.height : widget.height;

      // 确保尺寸有效
      actualWidth = actualWidth > 0 ? actualWidth : 100;
      actualHeight = actualHeight > 0 ? actualHeight : 100;

      return Size(actualWidth, actualHeight);
    }

    // 如果已经有明确的尺寸，直接使用
    return Size(widget.width, widget.height);
  }

  /// 处理画布状态事件
  void _handleCanvasStateEvent(CanvasStateEvent event) {
    if (!mounted) {
      return;
    }

    switch (event.type) {
      case CanvasStateEventType.imageLoadStart:
        // 显示图片加载Loading Toast
        AigcEditIconToast.dismissIconToast();
        // 等待消失动画完成后显示成功提示
        Future.delayed(const Duration(milliseconds: 350), () {
          AigcEditIconToast.showIconToast(
            context,
            event.message ?? '正在加载图片...',
            iconPath: 'assets/icons/aigc_editing_toast_icon.png',
            isAutoDismiss: false,
            parentWidget: _canvasKey,
          );
        });
        break;

      case CanvasStateEventType.imageLoadSuccess:
        if (_handlingResultTips) {
          return;
        }
        _handlingResultTips = true;
        // 延迟2秒后关闭Loading Toast，然后显示成功提示
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            AigcEditIconToast.dismissIconToast();
            // 等待消失动画完成后显示成功提示
            Future.delayed(const Duration(milliseconds: 350), () {
              if (mounted) {
                if (!SharedPreferencesService
                    .hasShowAIGCBackImageSuccessToast) {
                  AigcEditIconToast.showIconToast(
                    context,
                    event.message ?? 'AI已自动建立主体保护区域，可手动修改区域，打样结果中主体区域将不受影响。',
                    iconPath: 'assets/icons/aigc_editing_toast_icon.png',
                    isAutoDismiss: true,
                    parentWidget: _canvasKey,
                  );
                  SharedPreferencesService.setHasShowAIGCBackImageSuccessToast(
                      hasShow: true);
                }
              }
            });
            _handlingResultTips = false;
          }
        });
        break;

      case CanvasStateEventType.imageLoadError:
        if (_handlingResultTips) {
          return;
        }
        _handlingResultTips = true;
        // 延迟2秒后关闭Loading Toast，然后显示错误提示
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            AigcEditIconToast.dismissIconToast();
            // 等待消失动画完成后显示错误提示
            Future.delayed(const Duration(milliseconds: 350), () {
              if (mounted) {
                AigcEditIconToast.showIconToast(
                  context,
                  event.message ?? '加载失败',
                  icon: Icons.error,
                  isAutoDismiss: true,
                  parentWidget: _canvasKey,
                );
              }
            });
            _handlingResultTips = false;
          }
        });
        break;
    }
  }
}
