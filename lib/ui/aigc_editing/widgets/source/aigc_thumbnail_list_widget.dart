import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/constants/aigc_thumbnail_constants.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_thumbnail_list_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_edit_icon_toast.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_thumbnail_title.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_thumbnail_widget.dart';
import 'package:turing_art/ui/aigc_sample/widget/aigc_pc_delete_confirm_dialog.dart';
import 'package:turing_art/utils/pg_dialog/dialog_tags.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/shortcut/service/shortcut_key_service.dart';

/// 缩略图列表组件V2 - 遵循MVVM架构，职责完全分离
/// UI层只关心ViewModel，不知道SharedDataProvider的存在
class AigcThumbnailListWidget extends StatefulWidget {
  /// 列表高度
  final double height;

  /// 背景颜色
  final Color backgroundColor;

  /// 滚动物理效果
  final ScrollPhysics? scrollPhysics;

  /// 缩略图大小
  final double thumbnailSize;

  /// 缩略图间距
  final double spacing;

  const AigcThumbnailListWidget({
    super.key,
    this.height = 100,
    this.backgroundColor = const Color(0xFF424242),
    this.scrollPhysics = const BouncingScrollPhysics(),
    this.thumbnailSize = 80,
    this.spacing = 8,
  });

  @override
  State<AigcThumbnailListWidget> createState() =>
      _AigcThumbnailListWidgetState();
}

class _AigcThumbnailListWidgetState extends State<AigcThumbnailListWidget>
    with WidgetsBindingObserver {
  late ScrollController _scrollController;
  Timer? _scrollDebounceTimer;
  late FocusNode _focusNode;
  late ShortcutKeyService _shortcutKeyService;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _focusNode = FocusNode();
    _shortcutKeyService = ShortcutKeyService.forPlatform();

    // 添加应用生命周期监听器，用于检测从系统弹窗返回
    WidgetsBinding.instance.addObserver(this);

    // 确保在组件构建完成后获取焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _requestFocus();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollDebounceTimer?.cancel();
    _scrollController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 滚动到指定索引位置使其可见
  void _scrollToVisible(int index) {
    if (!_scrollController.hasClients) {
      return;
    }

    final itemWidth = widget.thumbnailSize + widget.spacing;
    final screenWidth = MediaQuery.of(context).size.width;
    final targetOffset = index * itemWidth - (screenWidth / 2 - itemWidth / 2);

    _scrollController.animateTo(
      targetOffset.clamp(0.0, _scrollController.position.maxScrollExtent),
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: _focusNode,
      autofocus: true,
      onKeyEvent: _handleKeyEvent,
      child: _buildScene(),
    );
  }

  Widget _buildScene() {
    return Consumer<AigcThumbnailListViewModel>(
      builder: (context, viewModel, child) {
        // 监听删除确认弹窗状态
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (viewModel.showDeleteConfirmDialog) {
            _showDeleteConfirmDialog(viewModel);
          }
        });

        return Column(
          children: [
            Container(
                color: const Color(0xFFFFFFFF).withOpacity(0.1), height: 1),
            // 标题
            AigcThumbnailTitle(onImageSelection: (type) {
              viewModel.handleFileSelection(type).then((result) {
                if (result.isNotEmpty) {
                  PGDialog.showToast('本次共导入${result.length}张照片');
                }
                // 文件选择完成后恢复焦点
                unawaited(_requestFocusDelayed());
              });
            }),
            Expanded(
              child: _buildScrollableList(viewModel),
            )
          ],
        );
      },
    );
  }

  /// 构建可滚动列表
  Widget _buildScrollableList(AigcThumbnailListViewModel viewModel) {
    return ScrollbarTheme(
      data: ScrollbarThemeData(
        // 滚动条拖拽区域的颜色 - 20%透明度的白色
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.hovered)) {
            return const Color(0x40FFFFFF); // 悬停时稍微更明显一些 (25%透明度)
          }
          if (states.contains(MaterialState.dragged)) {
            return const Color(0x4DFFFFFF); // 拖拽时更明显 (30%透明度)
          }
          return const Color(0x33FFFFFF); // 默认20%透明度的白色
        }),
        trackColor: MaterialStateProperty.all(const Color(0x0DFFFFFF)),
        trackBorderColor: MaterialStateProperty.all(Colors.transparent),
        thickness: MaterialStateProperty.all(4.0),
        radius: const Radius.circular(2),
        // 滚动条与内容的间距
        crossAxisMargin: 8.0,
        // 滚动条的主轴边距，使滚动条与内容区域对齐
        mainAxisMargin: 8.0,
      ),
      child: Scrollbar(
        controller: _scrollController,
        thumbVisibility: false, // 不始终显示，只在需要时显示
        trackVisibility: false,
        child: Listener(
          onPointerSignal: (pointerSignal) {
            if (pointerSignal is PointerScrollEvent &&
                _scrollController.hasClients) {
              // 处理鼠标滚轮事件，将垂直滚动转换为水平滚动
              final offset = _scrollController.offset;
              final delta = pointerSignal.scrollDelta.dy;

              // 滚动灵敏度调整 - 根据缩略图大小调整
              final scrollSensitivity = widget.thumbnailSize * 0.5;
              final newOffset = offset + (delta * scrollSensitivity / 120);

              // 确保滚动控制器有客户端连接后再滚动
              if (_scrollController.position.maxScrollExtent > 0) {
                _scrollController.jumpTo(
                  newOffset.clamp(
                      0.0, _scrollController.position.maxScrollExtent),
                );
              }
            }
          },
          child: ScrollConfiguration(
            behavior: ScrollConfiguration.of(context).copyWith(
              dragDevices: {
                PointerDeviceKind.touch,
                PointerDeviceKind.mouse, // 启用鼠标拖拽
              },
              scrollbars: false, // 禁用默认滚动条，使用自定义滚动条
            ),
            child: ListView.builder(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              physics: widget.scrollPhysics,
              padding: const EdgeInsets.only(left: 8, right: 8),
              itemCount: viewModel.images.length,
              itemBuilder: (context, index) =>
                  _buildThumbnailItem(viewModel, index),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建单个缩略图项
  Widget _buildThumbnailItem(AigcThumbnailListViewModel viewModel, int index) {
    if (index >= viewModel.images.length) {
      return const SizedBox();
    }

    final imageData = viewModel.images[index];
    final isSelected = index == viewModel.selectedIndex;
    final isInMultiSelection = viewModel.selectedIds.contains(imageData.fileId);

    return Container(
        margin: EdgeInsets.only(left: index == 0 ? 0 : 2),
        alignment: Alignment.topCenter,
        child: AigcThumbnailWidget(
          index: index,
          imageData: imageData,
          isSelected: isSelected,
          isInMultiSelection: isInMultiSelection,
          isMultiSelectMode: viewModel.isMultiSelectMode,
          onTap: ({required bool isControlPressed}) {
            viewModel.selectImage(index, isControlPressed: isControlPressed);
            AigcEditIconToast.dismissIconToast();
          },
          onThumbnailRequest: (index) => viewModel.requestThumbnail(index),
          onThumbnailRelease: (index) => viewModel.releaseThumbnail(index),
          // loadManager: widget.loadManager!,
        )
        // : _buildBasicThumbnailItem(
        //     viewModel, index, imageData, isSelected, isInMultiSelection),
        );
  }

  /// 应用生命周期状态变化监听（仅处理从系统弹窗返回的情况）
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      // 应用恢复时，延迟获取焦点（处理从系统文件选择器返回的情况）
      unawaited(_requestFocusDelayed());
    }
  }

  /// 请求焦点
  void _requestFocus() {
    if (mounted && _focusNode.canRequestFocus) {
      _focusNode.requestFocus();
      PGLog.d('请求焦点: ${_focusNode.hasFocus}');
    }
  }

  /// 延迟请求焦点
  Future<void> _requestFocusDelayed([int delayMs = 200]) async {
    await Future.delayed(Duration(milliseconds: delayMs));
    if (mounted) {
      _requestFocus();
    }
  }

  /// 处理键盘事件
  void _handleKeyEvent(KeyEvent event) {
    if (event is KeyDownEvent) {
      final viewModel = context.read<AigcThumbnailListViewModel>();

      // 检查是否按下了Escape键退出多选模式
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        if (viewModel.isMultiSelectMode) {
          PGLog.d('Escape键按下，退出多选模式');
          viewModel.setMultiSelectMode(enabled: false);
          return;
        }
      }

      // 使用快捷键服务检查是否按下了删除键
      if (_shortcutKeyService.isDeletePressed(event)) {
        PGLog.d('删除键按下，准备显示删除确认弹窗');
        viewModel.requestDeleteConfirmation();
      }
    }
  }

  /// 显示删除确认弹窗
  void _showDeleteConfirmDialog(AigcThumbnailListViewModel viewModel) {
    if (PGDialog.isDialogVisible(DialogTags.aigcPcDeleteConfirm)) {
      return;
    }

    // 根据多选模式显示不同的提示文本
    final selectedCount = viewModel.isMultiSelectMode
        ? viewModel.selectedIds.length
        : 1;

    final title = AigcThumbnailConstants.getDeleteConfirmTitle(selectedCount);
    const content = AigcThumbnailConstants.deleteConfirmContent;

    AigcPcDeleteConfirmDialog.show(
      context,
      title: title,
      content: content,
      onCancel: () {
        viewModel.hideDeleteConfirmation();
        // 关闭弹窗后重新获取焦点
        unawaited(_requestFocusDelayed());
      },
      onConfirm: () async {
        await viewModel.confirmDelete();
        PGDialog.dismiss(tag: DialogTags.aigcPcDeleteConfirm);
        // 删除完成后重新获取焦点
        unawaited(_requestFocusDelayed());
      },
    );
  }
}
