import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:provider/provider.dart';
import 'package:turing_art/ui/aigc_editing/viewmodels/aigc_thumbnail_list_view_model.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_image_selection_popup.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 缩略图顶部显示标题的 widget
class AigcThumbnailTitle extends StatefulWidget {
  final void Function(ImageSelectionType type)? onImageSelection;

  const AigcThumbnailTitle({
    super.key,
    this.onImageSelection,
  });

  @override
  State<AigcThumbnailTitle> createState() => _AigcThumbnailTitleState();
}

class _AigcThumbnailTitleState extends State<AigcThumbnailTitle> {
  final GlobalKey _addButtonKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Selector<AigcThumbnailListViewModel,
            ({int selectedCount, int totalCount, String imageName})>(
        selector: (context, viewModel) {
      final selectedCount = viewModel.isMultiSelectMode
          ? viewModel.selectedIds.length
          : (viewModel.selectedIndex != -1 ? 1 : 0);
      final totalCount = viewModel.images.length;

      String imageName = '';
      if (viewModel.isMultiSelectMode) {
        // 多选模式下显示选中数量信息
        if (selectedCount == 0) {
          imageName = '多选模式 - 未选择图片';
        } else if (selectedCount == 1) {
          imageName = '多选模式 - 已选择1张图片';
        } else {
          imageName = '多选模式 - 已选择$selectedCount张图片';
        }
      } else {
        // 单选模式下显示文件名
        if (viewModel.selectedIndex != -1 &&
            viewModel.images.isNotEmpty &&
            viewModel.selectedIndex < viewModel.images.length) {
          try {
            imageName = path.basename(
                viewModel.images[viewModel.selectedIndex].originalPath);
          } catch (e) {
            imageName = 'Error';
          }
        } else if (selectedCount == 0 && totalCount > 0) {
          imageName = '未选择图片';
        } else if (totalCount == 0) {
          imageName = '无图片';
        }
      }

      return (
        selectedCount: selectedCount,
        totalCount: totalCount,
        imageName: imageName
      );
    }, builder: (context, data, child) {
      return SizedBox(
        height: 40,
        child: Row(
          children: [
            const SizedBox(width: 8),
            // 添加图片按钮
            _buildAddImageButton(),
            const SizedBox(width: 8),
            // 使用 Flexible 确保左侧文本有最小空间
            Flexible(
              flex: 0,
              child: Text('已选${data.selectedCount} / 总数${data.totalCount}',
                  style: const TextStyle(fontSize: 12, color: Colors.white)),
            ),
            const SizedBox(width: 8),
            // 多选模式下显示退出按钮
            if (context
                .watch<AigcThumbnailListViewModel>()
                .isMultiSelectMode) ...[
              _buildExitMultiSelectButton(context),
              const SizedBox(width: 8),
            ],

            Expanded(
              child: Align(
                alignment: Alignment.centerRight,
                child: Text(
                  data.imageName,
                  style: TextStyle(
                    fontSize: 12,
                    color: const Color(0xB3FFFFFF),
                    fontFamily: Fonts.defaultFontFamily,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  textAlign: TextAlign.right,
                ),
              ),
            ),
            const SizedBox(width: 10),
          ],
        ),
      );
    });
  }

  Widget _buildAddImageButton() {
    return GestureDetector(
      key: _addButtonKey,
      onTap: _showSelectionPopup,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            color: const Color(0xFF3B3B3B)),
        child: Image.asset('assets/icons/aigc_add_small.png',
            width: 24, height: 24),
      ),
    );
  }

  Widget _buildExitMultiSelectButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context
            .read<AigcThumbnailListViewModel>()
            .setMultiSelectMode(enabled: false);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: const Color(0xFF5A5A5A),
          border: Border.all(color: const Color(0xFF707070), width: 1),
        ),
        child: const Text(
          '退出多选',
          style: TextStyle(
            fontSize: 10,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  void _showSelectionPopup() {
    AigcImageSelectionPopup.show(
      context: context,
      buttonKey: _addButtonKey,
      onSelection: widget.onImageSelection,
    );
  }
}
