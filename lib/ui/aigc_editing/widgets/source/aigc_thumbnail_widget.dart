import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/ui/core/themes/fonts.dart';

/// 智能缩略图Widget - 自动管理加载和清理
class AigcThumbnailWidget extends StatefulWidget {
  final int index;
  final AigcPreviewImageItem imageData;
  final bool isSelected;
  final bool isInMultiSelection;
  final bool isMultiSelectMode;
  final Function({required bool isControlPressed}) onTap;
  final Function(int index) onThumbnailRequest;
  final Function(int index) onThumbnailRelease;

  const AigcThumbnailWidget({
    super.key,
    required this.index,
    required this.imageData,
    required this.isSelected,
    this.isInMultiSelection = false,
    this.isMultiSelectMode = false,
    required this.onTap,
    required this.onThumbnailRequest,
    required this.onThumbnailRelease,
  });

  @override
  State<AigcThumbnailWidget> createState() => _AigcThumbnailWidgetState();
}

/// 底部缩略图 widget
class _AigcThumbnailWidgetState extends State<AigcThumbnailWidget>
    with SingleTickerProviderStateMixin {
  // AigcThumbnailViewModel? _viewModel;

  static const double _cornerRadius = 4.0;

  /// 显示的 item 大小
  static const double _itemSize = 64.0;

  // 加载动画控制器
  late AnimationController _loadingController;

  @override
  void initState() {
    super.initState();

    _loadingController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();
    widget.onThumbnailRequest(widget.index);
  }

  @override
  void dispose() {
    _loadingController.dispose();
    super.dispose();
  }

  @override
  void deactivate() {
    super.deactivate();
    widget.onThumbnailRelease(widget.index);
  }

  @override
  Widget build(BuildContext context) {
    return _buildScene();
  }

  _buildScene() {
    return GestureDetector(
      onTap: () {
        // 检测Control键是否按下
        widget.onTap(
          isControlPressed: HardwareKeyboard.instance.isControlPressed,
        );
      },
      behavior: HitTestBehavior.opaque,
      child: SizedBox(
        width: _itemSize,
        height: _itemSize,
        child: Stack(
          children: [
            // 缩略图
            ClipRRect(
              borderRadius: BorderRadius.circular(_cornerRadius),
              child: _buildThumbnailContent(),
            ),

            // 构建索引指示器
            _buildIndexIndicator(),

            // 选中状态边框
            if (widget.isSelected) ...[
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(_cornerRadius),
                    border:
                        Border.all(color: const Color(0xFFFF9EB9), width: 1),
                  ),
                ),
              ),
              // 内层黑色边框
              Positioned(
                left: 1,
                top: 1,
                right: 1,
                bottom: 1,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(_cornerRadius),
                    border:
                        Border.all(color: const Color(0xFF262626), width: 1),
                  ),
                ),
              ),
            ],

            // 多选模式下的选中状态
            if (widget.isMultiSelectMode && widget.isInMultiSelection) ...[
              // 多选选中边框（蓝色）
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(_cornerRadius),
                    border:
                        Border.all(color: const Color(0xFF007AFF), width: 2),
                  ),
                ),
              ),
              // 多选选中指示器
              Positioned(
                top: 4,
                right: 4,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF007AFF),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建缩略图内容
  Widget _buildThumbnailContent() {
    // 如果是已损坏的图片，显示错误占位符
    if (widget.imageData.isDamaged) {
      return _buildErrorPlaceholder();
    }
    // 如果有缩略图路径，显示真实缩略图
    else if (widget.imageData.hasThumbnail &&
        widget.imageData.thumbnailPath != null) {
      return _buildRealThumbnail(widget.imageData.thumbnailPath!);
    }
    // 如果没有缩略图，显示加载占位符
    else {
      return _buildLoadingPlaceholder();
    }
  }

  /// 构建真实缩略图
  Widget _buildRealThumbnail(String thumbnailPath) {
    return SizedBox(
      width: _itemSize,
      height: _itemSize,
      child: Image.file(
        File(thumbnailPath),
        fit: BoxFit.cover,
        filterQuality: FilterQuality.medium,
        errorBuilder: (context, error, stackTrace) {
          // 缩略图加载失败，显示错误占位符
          return _buildErrorPlaceholder();
        },
      ),
    );
  }

  /// 构建加载占位符
  Widget _buildLoadingPlaceholder() {
    return Container(
      width: _itemSize,
      height: _itemSize,
      alignment: Alignment.bottomRight,
      decoration: BoxDecoration(
          color: const Color(0xFF2B2B2B),
          borderRadius: BorderRadius.circular(_cornerRadius)),
      child: Container(
        width: 16,
        height: 16,
        margin: const EdgeInsets.only(right: 5, bottom: 5),
        child: RotationTransition(
          turns: _loadingController,
          child: Image.asset(
            'assets/icons/icon_loading_small.png',
            width: 24,
            height: 24,
          ),
        ),
      ),
    );
  }

  /// 构建错误占位符
  Widget _buildErrorPlaceholder() {
    return Container(
      width: _itemSize,
      height: _itemSize,
      decoration: BoxDecoration(
        color: const Color(0xFF0D0D0D),
        borderRadius: BorderRadius.circular(_cornerRadius),
      ),
      child: Center(
        child: Image.asset(
          'assets/icons/aigc_image_unavailable.png',
          width: 32,
          height: 32,
        ),
      ),
    );
  }

  Positioned _buildIndexIndicator() {
    return Positioned(
        left: 4,
        bottom: 4,
        child: Container(
          width: 12,
          height: 12,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              color: const Color(0x99000000),
              borderRadius: BorderRadius.circular(1)),
          child: Text(
            '${(widget.index + 1)}',
            style: TextStyle(
                fontSize: 7,
                color: Colors.white,
                fontFamily: Fonts.defaultFontFamily),
          ),
        ));
  }
}
