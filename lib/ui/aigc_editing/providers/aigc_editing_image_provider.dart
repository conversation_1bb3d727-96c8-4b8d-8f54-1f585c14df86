import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_matting_mask_darw_path_image_data_info.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';

/// AIGC编辑事件类型枚举
enum AigcEditingEventType {
  /// 蒙版笔触更新事件
  mattingMaskBrushStrokeUpdate,

  /// 重置交互式蒙版事件
  resetInteractiveMask,

  /// 图片选择变化事件
  imageSelectionChanged,
}

/// AIGC编辑场景的共享数据Provider
/// 管理需要在多个ViewModel间共享的状态，如图片列表、选中索引等
class AigcEditingImageProvider extends ChangeNotifier {
  // 实例ID，用于验证单例
  final String _instanceId = DateTime.now().millisecondsSinceEpoch.toString();

  final Set<String> _selectedIds = <String>{};

  // 多选模式
  bool _isMultiSelectMode = false;

  // 单个图片的蒙版数据
  final AigcMattingMaskDarwPathImageDataInfo _mattingMaskImageDataInfo =
      AigcMattingMaskDarwPathImageDataInfo();

  // 事件流控制器
  final StreamController<AigcEditingEventType> _eventStreamController =
      StreamController<AigcEditingEventType>.broadcast();

  // 单个图片的蒙版数据
  AigcMattingMaskDarwPathImageDataInfo get mattingMaskImageDataInfo =>
      _mattingMaskImageDataInfo;

  /// 获取事件流
  Stream<AigcEditingEventType> get eventStream => _eventStreamController.stream;

  /// 获取实例ID（用于调试验证单例）
  String get instanceId => _instanceId;

  /// 获取多选ID集合（只读）
  Set<String> get selectedIds => Set.unmodifiable(_selectedIds);

  /// 是否为多选模式
  bool get isMultiSelectMode => _isMultiSelectMode;

  // 图片选择变化
  bool _imageSelectionChanged = false;

  bool get hasImageSelectionChanged {
    if (_imageSelectionChanged) {
      _imageSelectionChanged = false; // 读取后重置
      return true;
    }
    return false;
  }

  AigcPreviewImageItem? _selectedImage;

  set selectedImage(AigcPreviewImageItem? image) {
    if (_selectedImage != image) {
      _selectedImage = image;
      _imageSelectionChanged = true;

      // 触发图片选择变化事件
      _eventStreamController.add(AigcEditingEventType.imageSelectionChanged);

      notifyListeners();
    }

    if (selectedImage?.fileId != image?.fileId) {
      _imageSelectionChanged = true;
    }
  }

  AigcPreviewImageItem? get selectedImage => _selectedImage;

  /// 设置多选模式
  void setMultiSelectMode({required bool enabled}) {
    if (_isMultiSelectMode != enabled) {
      _isMultiSelectMode = enabled;

      // 如果退出多选模式，清空多选ID
      if (!enabled) {
        _selectedIds.clear();
      }

      notifyListeners();
    }
  }

  /// 添加选中ID
  void addSelectedId(String id) {
    if (_selectedIds.add(id)) {
      notifyListeners();
    }
  }

  /// 移除选中ID
  void removeSelectedId(String id) {
    if (_selectedIds.remove(id)) {
      notifyListeners();
    }
  }

  /// 切换选中ID
  void toggleSelectedId(String id) {
    if (_selectedIds.contains(id)) {
      removeSelectedId(id);
    } else {
      addSelectedId(id);
    }
  }

  /// 清空所有选中ID
  void clearSelectedIds() {
    if (_selectedIds.isNotEmpty) {
      _selectedIds.clear();
      notifyListeners();
    }
  }

  /// 检查ID是否被选中
  bool isIdSelected(String id) {
    return _selectedIds.contains(id);
  }

  /// 更新单个图片的蒙版数据 - 传递新涂抹的笔触路径以及标记是否是涂抹模式
  /// regionalFrameDataInfo为空则表示为正常涂抹，非空则为框选涂抹
  void updateMattingMaskBrushStroke(
      AigcRegionalFrameDataInfo? regionalFrameDataInfo,
      Uint8List? strokePathDataBytes,
      {required bool isBrushMode}) {
    _mattingMaskImageDataInfo.regionalFrameDataInfo = regionalFrameDataInfo;
    _mattingMaskImageDataInfo.currentStrokesBytes = strokePathDataBytes;
    _mattingMaskImageDataInfo.isBrushMode = isBrushMode;

    // 通过事件流发送事件
    _eventStreamController
        .add(AigcEditingEventType.mattingMaskBrushStrokeUpdate);
  }

  /// 触发重置交互式蒙版事件
  void triggerResetInteractiveMask() {
    // 通过事件流发送事件
    _eventStreamController.add(AigcEditingEventType.resetInteractiveMask);
  }

  @override
  void dispose() {
    _eventStreamController.close();
    super.dispose();
  }
}
