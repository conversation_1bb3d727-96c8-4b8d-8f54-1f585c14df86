import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_canvas_painter_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_control_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_preferred_mask_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_editing/strategies/aigc_background_strategy.dart';

/// AIGC图像叠加Provider
///
/// 负责管理图像叠加相关的数据和状态
/// 通过ChangeNotifier模式驱动UI刷新
class AigcImageOverlayProvider extends ChangeNotifier {
  /// 背景图片对象
  ui.Image? _backgroundImage;

  /// 蒙版图片对象
  ui.Image? _maskImage;

  /// 是否显示蒙版图片
  bool _showMask = true;

  /// 图片加载状态
  bool _isLoadingBackground = false;

  bool _isLoadingMask = false;

  String? _backgroundLoadError;

  String? _maskLoadError;

  /// 背景策略
  AigcBackgroundStrategy? _backgroundStrategy;

  /// 图片提供者（用于监听selectedImage变化）
  final AigcEditingImageProvider _imageProvider;

  /// 当前项目仓库（用于获取项目ID）
  final CurrentEditingProjectRepository _currentProjectRepository;

  /// 画布绘制提供者（用于重置缩放）
  final AigcCanvasPainterProvider _canvasPainterProvider;

  final AigcEditingThemeListProvider _themeListProvider;

  final AigcEditingControlProvider _controlProvider;

  final AigcEditingImagePreferredMaskProvider _preferredMaskProvider;

  /// 上一次选中的图片文件ID（用于判断是否真正切换了图片）
  String? _lastSelectedImageFileId;

  /// 事件流订阅
  StreamSubscription<AigcEditingEventType>? _eventStreamSubscription;

  /// 获取背景图片
  ui.Image? get backgroundImage => _backgroundImage;

  /// 获取蒙版图片
  ui.Image? get maskImage => _maskImage;

  /// 获取是否显示蒙版
  bool get showMask => _showMask;

  /// 获取背景图片加载状态
  bool get isLoadingBackground => _isLoadingBackground;

  /// 获取蒙版图片加载状态
  bool get isLoadingMask => _isLoadingMask;

  /// 获取背景图片加载错误
  String? get backgroundLoadError => _backgroundLoadError;

  /// 获取蒙版图片加载错误
  String? get maskLoadError => _maskLoadError;

  /// 获取背景策略
  AigcBackgroundStrategy? get backgroundStrategy => _backgroundStrategy;

  /// 构造函数
  AigcImageOverlayProvider({
    required AigcEditingImageProvider imageProvider,
    required CurrentEditingProjectRepository currentProjectRepository,
    required AigcCanvasPainterProvider canvasPainterProvider,
    required AigcEditingThemeListProvider themeListProvider,
    required AigcEditingControlProvider controlProvider,
    required AigcEditingImagePreferredMaskProvider preferredMaskProvider,
  })  : _imageProvider = imageProvider,
        _currentProjectRepository = currentProjectRepository,
        _canvasPainterProvider = canvasPainterProvider,
        _themeListProvider = themeListProvider,
        _controlProvider = controlProvider,
        _preferredMaskProvider = preferredMaskProvider {
    // 如果提供了imageProvider，则监听其变化
    _imageProvider.addListener(_handleImageProviderChange);

    // 监听图片选择变化事件，重置智能框选模式
    _eventStreamSubscription = _imageProvider.eventStream.listen((event) {
      if (event == AigcEditingEventType.imageSelectionChanged) {
        _controlProvider.setSmartBoxModeEnabled(value: false);
      }
    });

    // 立即检查是否已经有selectedImage，确保不错过初始状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 在回调中安全调用，避免在已销毁的widget上操作
      try {
        _handleImageProviderChange();
      } catch (e) {
        // 忽略已销毁widget的错误
      }
    });
  }

  /// 工厂方法，从context中创建Provider实例
  factory AigcImageOverlayProvider.fromContext(BuildContext context) {
    final imageProvider = context.read<AigcEditingImageProvider>();
    final currentProjectRepository =
        context.read<CurrentEditingProjectRepository>();
    final canvasPainterProvider = context.read<AigcCanvasPainterProvider>();
    final themeListProvider = context.read<AigcEditingThemeListProvider>();
    final controlProvider = context.read<AigcEditingControlProvider>();
    final preferredMaskProvider =
        context.read<AigcEditingImagePreferredMaskProvider>();
    return AigcImageOverlayProvider(
      imageProvider: imageProvider,
      currentProjectRepository: currentProjectRepository,
      canvasPainterProvider: canvasPainterProvider,
      themeListProvider: themeListProvider,
      controlProvider: controlProvider,
      preferredMaskProvider: preferredMaskProvider,
    );
  }

  @override
  void dispose() {
    // 移除监听器
    _imageProvider.removeListener(_handleImageProviderChange);
    _eventStreamSubscription?.cancel();
    super.dispose();
  }

  /// 处理图片提供者变化
  void _handleImageProviderChange() async {
    reloadBackgroundImageFromPreviewItem();
  }

  Future<void> reloadBackgroundImageFromPreviewItem(
      {bool isForce = false}) async {
    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage != null) {
      final currentFileId = selectedImage.fileId;
      final isImageChanged = _lastSelectedImageFileId != currentFileId;

      // 只有当图片真正切换时才重置画布缩放
      if ((isImageChanged) || isForce) {
        setBackgroundImage(null);
        setMaskImage(null);
        // 不立即重置变换，而是在图片加载完成后一次性设置
        _lastSelectedImageFileId = currentFileId;
        // 当selectedImage更新时，加载新的背景图片
        bool success = await _loadBackgroundImageFromPreviewItem();
        _themeListProvider.setProcessState(ProcessState.disabled);
        _controlProvider.setIsLoadBackgroundImageStatus(value: success);
      } else if (!isImageChanged) {
        // 即使图片未切换，也需要更新_lastSelectedImageFileId，确保后续的更新不会被误判为图片切换
        _lastSelectedImageFileId = currentFileId;
      }
      loadMaskImage();
    } else {
      _lastSelectedImageFileId = null;
      setBackgroundImage(null);
      setMaskImage(null);
      _controlProvider.setIsLoadBackgroundImageStatus(value: false);
    }
  }

  /// 检查selectedImage是否为null
  bool isSelectedImageNull() {
    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage == null) {
      return true;
    }
    return false;
  }

  /// 加载蒙版图片
  Future<void> loadMaskImage() async {
    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage == null) {
      return;
    }

    // 检查是否有蒙版图片需要加载
    try {
      String? maskPathToLoad;

      // 按优先级查找蒙版：interactiveMask > mask > 提交新的mask任务
      final workspaceId =
          _currentProjectRepository.currentWorkspace?.workspaceId;
      if (workspaceId != null) {
        // 1. 优先尝试加载交互式蒙版
        final interactiveMaskFilePath =
            _preferredMaskProvider.currentImageInteractiveMaskPath;

        if (interactiveMaskFilePath != null) {
          maskPathToLoad = interactiveMaskFilePath;
        } else {
          // 2. 如果没有交互式蒙版，尝试加载基础蒙版
          final maskFilePath = _preferredMaskProvider.currentImageMaskPath;

          if (maskFilePath != null) {
            maskPathToLoad = maskFilePath;
          } else {
            // 3. 如果都没有，且图片没有蒙版标记，提交蒙版生成任务
            // if (!selectedImage.hasMask && _processingService != null) {
            //   final maskResourceFile = _mediaRepository.getResourceFilePath(
            //     workspaceId,
            //     selectedImage.fileId,
            //     MediaResourceType.mask,
            //   );

            //   _processingService.submitTask(
            //     inputPath:
            //         selectedImage.previewPath ?? selectedImage.originalPath,
            //     outputPath: maskResourceFile.path,
            //     fileId: selectedImage.fileId,
            //     taskType: AigcTaskType.mask,
            //     executeNow: true,
            //   );
            // }

            // 没有蒙版可加载
            _maskImage = null;
            _maskLoadError = null;
            return;
          }
        }
      }

      // 如果找到了蒙版路径，加载图片
      if (maskPathToLoad != null) {
        await _loadMaskImageFromPath(maskPathToLoad);
      }
    } catch (e) {
      _maskImage = null;
      _maskLoadError = e.toString();
    } finally {
      _isLoadingMask = false;
      notifyListeners();
    }
  }

  /// 从预览项加载背景图片
  Future<bool> _loadBackgroundImageFromPreviewItem() async {
    final previewItem = _imageProvider.selectedImage;
    if (previewItem == null || previewItem.isDamaged) {
      setBackgroundLoadingState(isLoading: false, error: '图片损坏，请重新选择');
      return false;
    }

    // 获取当前项目ID
    final projectId = _currentProjectRepository.currentWorkspace?.workspaceId;
    if (projectId == null) {
      setBackgroundLoadingState(isLoading: false, error: '无法获取当前项目ID');
      return false;
    }

    // 设置加载状态
    setBackgroundLoadingState(isLoading: true, error: null);

    try {
      String? imagePath;

      // 使用高清大图路径
      if (previewItem.readyToDisplay) {
        imagePath = previewItem.highQualityPath;
      }

      if (imagePath == null || imagePath.isEmpty) {
        // setBackgroundLoadingState(isLoading: false, error: '无法获取图片路径');
        return false;
      }

      // 加载图片
      final image = await _loadImageFromPath(imagePath);
      if (image != null) {
        setBackgroundImage(image);
        return true;
      } else {
        setBackgroundLoadingState(isLoading: false, error: '图片加载失败');
      }
    } catch (e) {
      setBackgroundLoadingState(isLoading: false, error: '图片加载异常: $e');
    }
    return false;
  }

  /// 从文件路径加载图片为ui.Image
  Future<ui.Image?> _loadImageFromPath(String filePath) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        return null;
      }

      final Uint8List bytes = await file.readAsBytes();
      final ui.Codec codec = await ui.instantiateImageCodec(bytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();

      return frameInfo.image;
    } catch (e) {
      return null;
    }
  }

  /// 从路径加载蒙版图片
  Future<void> _loadMaskImageFromPath(String maskPath) async {
    // 设置加载状态
    setMaskLoadingState(isLoading: true, error: null);

    try {
      // 加载蒙版图片
      final maskImage = await _loadImageFromPath(maskPath);
      if (maskImage != null) {
        setMaskImage(maskImage);
      } else {
        setMaskLoadingState(isLoading: false, error: '蒙版图片加载失败');
      }
    } catch (e) {
      setMaskLoadingState(isLoading: false, error: '蒙版图片加载异常: $e');
    }
  }

  /// 设置背景图片
  void setBackgroundImage(ui.Image? value) {
    if (_backgroundImage != value) {
      _backgroundImage = value;
      _isLoadingBackground = false;
      _backgroundLoadError = null;

      // 当背景图片更新时，同步更新画布的内容尺寸并计算适配缩放
      if (value != null) {
        final imageSize =
            Size(value.width.ceilToDouble(), value.height.ceilToDouble());
        _canvasPainterProvider.setContentSize(imageSize);
        _canvasPainterProvider.resetDefaultCanvasSize();
        // 计算自动适配缩放比例
        final canvasSize = _canvasPainterProvider.canvasSize;
        if (canvasSize != null) {
          _calculateAndApplyFitScale(imageSize, canvasSize);
        }
      }

      notifyListeners();
    }
  }

  /// 计算并应用适配缩放比例
  void _calculateAndApplyFitScale(Size imageSize, Size canvasSize) {
    // 计算图片相对于画布的缩放比例
    final scaleX = canvasSize.width / imageSize.width;
    final scaleY = canvasSize.height / imageSize.height;

    // 取较小的缩放比例，确保图片能完整显示在画布中
    final fitScale = math.min(scaleX, scaleY);

    // 如果图片超出画布尺寸，则应用适配缩放；否则使用1.0
    final targetScale = fitScale < 1.0 ? fitScale : 1.0;

    // 重置变换并设置适当的缩放比例
    _canvasPainterProvider.resetTransform();
    if (targetScale != 1.0) {
      _canvasPainterProvider.setScale(targetScale);
    }
  }

  /// 处理画布尺寸变化，重新计算适配缩放
  void handleCanvasSizeChanged(Size newCanvasSize) {
    if (_backgroundImage != null) {
      final imageSize = Size(_backgroundImage!.width.ceilToDouble(),
          _backgroundImage!.height.ceilToDouble());
      final canvasSize = newCanvasSize;
      if (canvasSize.width > 0 && canvasSize.height > 0) {
        // 检查图片是否完全不可见
        final isCompletelyOutOfBounds =
            _isImageCompletelyOutOfBounds(imageSize, canvasSize);

        // 只有在用户没有手动缩放过的情况下才进行自适应
        if (!_canvasPainterProvider.hasUserManuallyScaled) {
          _calculateAndApplyFitScale(imageSize, canvasSize);
        }
        // 如果用户手动缩放过，但图片完全不可见，则智能调整位置
        else if (isCompletelyOutOfBounds) {
          _smartAdjustImagePosition(imageSize, canvasSize);
        }
      }
    }
  }

  /// 检查图片是否完全不可见
  bool _isImageCompletelyOutOfBounds(Size imageSize, Size canvasSize) {
    final scaledImageSize = Size(
      imageSize.width * _canvasPainterProvider.scale,
      imageSize.height * _canvasPainterProvider.scale,
    );

    final centerX = canvasSize.width / 2 - scaledImageSize.width / 2;
    final centerY = canvasSize.height / 2 - scaledImageSize.height / 2;

    final imageLeft = centerX + _canvasPainterProvider.offset.dx;
    final imageTop = centerY + _canvasPainterProvider.offset.dy;
    final imageRight = imageLeft + scaledImageSize.width;
    final imageBottom = imageTop + scaledImageSize.height;

    // 检查图片是否完全在画布外
    return imageRight <= 0 ||
        imageLeft >= canvasSize.width ||
        imageBottom <= 0 ||
        imageTop >= canvasSize.height;
  }

  /// 智能调整图片位置（当图片完全不可见时）
  void _smartAdjustImagePosition(Size imageSize, Size canvasSize) {
    final scaledImageSize = Size(
      imageSize.width * _canvasPainterProvider.scale,
      imageSize.height * _canvasPainterProvider.scale,
    );

    // 将图片移回到画布中心
    _canvasPainterProvider.setOffset(Offset.zero);

    // 如果图片太大无法完全显示在画布中，则适当缩小
    if (scaledImageSize.width > canvasSize.width ||
        scaledImageSize.height > canvasSize.height) {
      final scaleX = canvasSize.width / imageSize.width;
      final scaleY = canvasSize.height / imageSize.height;
      final fitScale = math.min(scaleX, scaleY) * 0.9; // 留出一些边距

      // 只在需要缩小时才调整缩放
      if (fitScale < _canvasPainterProvider.scale) {
        _canvasPainterProvider.setScale(fitScale);
      }
    }
  }

  /// 设置蒙版图片
  void setMaskImage(ui.Image? value) {
    if (_maskImage != value) {
      _maskImage = value;
      _isLoadingMask = false;
      _maskLoadError = null;
      notifyListeners();
    }
  }

  /// 设置是否显示蒙版
  void setShowMask({required bool showMask}) {
    if (_showMask != showMask) {
      _showMask = showMask;
      notifyListeners();
    }
  }

  /// 设置背景图片加载状态
  void setBackgroundLoadingState({required bool isLoading, String? error}) {
    if (_isLoadingBackground != isLoading || _backgroundLoadError != error) {
      _isLoadingBackground = isLoading;
      _backgroundLoadError = error;
      notifyListeners();
    }
  }

  /// 设置蒙版图片加载状态
  void setMaskLoadingState({required bool isLoading, String? error}) {
    if (_isLoadingMask != isLoading || _maskLoadError != error) {
      _isLoadingMask = isLoading;
      _maskLoadError = error;
      notifyListeners();
    }
  }

  /// 设置背景策略
  void setBackgroundStrategy(AigcBackgroundStrategy? strategy) {
    if (_backgroundStrategy != strategy) {
      _backgroundStrategy = strategy;
      notifyListeners();
    }
  }

  /// 手动触发背景图片更新
  /// 供外部调用，当需要手动更新背景图片时使用
  void updateBackgroundFromSelectedImage() {
    _handleImageProviderChange();
  }

  /// 传统setter方法（保持向后兼容）
  set backgroundImage(ui.Image? value) => setBackgroundImage(value);

  /// 传统setter方法（保持向后兼容）
  set maskImage(ui.Image? value) => setMaskImage(value);
}
