import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:turing_art/core/service/image_selection_service/image_selection_service.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/project_repository.dart';
import 'package:turing_art/datalayer/service/aigc_processors/aigc_service.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item.dart';
import 'package:turing_art/ui/aigc_editing/model/aigc_preview_image_item_adapter.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/widgets/aigc_reconnect_file_dialog.dart';
import 'package:turing_art/ui/aigc_editing/widgets/source/aigc_image_selection_popup.dart';
import 'package:turing_art/utils/file_manager.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 缩略图列表ViewModel
/// 负责缩略图列表的业务逻辑，包括选择、多选等
class AigcThumbnailListViewModel extends ChangeNotifier {
  final AigcEditingImageProvider _imageProvider;
  final MediaRepository _mediaRepository;
  final CurrentEditingProjectRepository _currentEditingProjectRepository;
  final AigcPreviewImageItemAdapter _aigcPreviewImageItemAdapter;
  final AigcService _processingService;
  final FileManager _fileManager;
  late final ImageSelectionService _imageSelectionService;

  // 添加一个变量来存储订阅
  StreamSubscription<AigcTaskResultMessage>? _resultStreamSubscription;
  StreamSubscription<AigcTaskProgressMessage>? _progressStreamSubscription;

  // 删除确认弹窗状态
  bool _showDeleteConfirmDialog = false;

  // 正在重连的文件ID集合，避免重复触发重连对话框
  String? _reconnectingFileId;

  AigcThumbnailListViewModel._({
    required AigcEditingImageProvider imageProvider,
    required MediaRepository mediaRepository,
    required CurrentEditingProjectRepository currentEditingProjectRepository,
    required ProjectRepository projectRepository,
    required AigcService processingService,
    required FileManager fileManager,
  })  : _imageProvider = imageProvider,
        _processingService = processingService,
        _mediaRepository = mediaRepository,
        _currentEditingProjectRepository = currentEditingProjectRepository,
        _fileManager = fileManager,
        _aigcPreviewImageItemAdapter =
            AigcPreviewImageItemAdapter(mediaRepository) {
    // 初始化 ImageSelectionService
    _imageSelectionService = ImageSelectionService.forPlatform();
    // 监听共享数据变化
    // _sharedDataProvider.addListener(_onSharedDataChanged);
    _setupRepositoryListener();
    _setupThumbnailListener();

    _imageProvider.addListener(_onImageSelectionChanged);
  }

  /// 工厂方法：通过context创建ViewModel
  /// UI组件调用此方法，不需要知道SharedDataProvider的存在
  static AigcThumbnailListViewModel create(BuildContext context) {
    final imageProvider = context.read<AigcEditingImageProvider>();
    final processingService = context.read<AigcService>();
    return AigcThumbnailListViewModel._(
      imageProvider: imageProvider,
      processingService: processingService,
      mediaRepository: context.read<MediaRepository>(),
      currentEditingProjectRepository:
          context.read<CurrentEditingProjectRepository>(),
      projectRepository: context.read<ProjectRepository>(),
      fileManager: FileManager(),
    );
  }

  /// 获取图片列表
  List<AigcPreviewImageItem> get images => List.unmodifiable(_images);

  /// 获取选中索引
  int get selectedIndex => _selectedIndex;

  /// 获取多选ID集合
  Set<String> get selectedIds => _imageProvider.selectedIds;

  /// 是否为多选模式
  bool get isMultiSelectMode => _imageProvider.isMultiSelectMode;

  /// 是否显示删除确认弹窗
  bool get showDeleteConfirmDialog => _showDeleteConfirmDialog;

  // 图片列表
  final List<AigcPreviewImageItem> _images = [];

  // 选中索引
  int _selectedIndex = -1;

  void _setupRepositoryListener() {
    _currentEditingProjectRepository.workspaceUpdated
        .where((workspace) => workspace != null) // 过滤掉 null 值
        .map((workspace) => workspace!) // 确保非空
        .listen((workspace) async {
      // 按创建时间排序
      final sortedFiles = List<WorkspaceFile>.from(workspace.files)
        ..sort((a, b) => a.createTime.compareTo(b.createTime));

      // 转换为预览图片项 - 使用批量转换方法，更高效
      _images.clear();
      _images.addAll(
        _aigcPreviewImageItemAdapter.fromWorkspaceFiles(sortedFiles),
      );

      // 在多选模式下，需要验证选中的ID是否仍然有效
      if (_imageProvider.isMultiSelectMode) {
        // 获取当前文件的所有ID
        final currentFileIds = _images.map((img) => img.fileId).toSet();

        // 找出仍然有效的选中ID
        final validSelectedIds =
            _imageProvider.selectedIds.intersection(currentFileIds);

        // 更新多选ID集合
        _imageProvider.clearSelectedIds();
        for (final id in validSelectedIds) {
          _imageProvider.addSelectedId(id);
        }

        // 如果没有有效的选中项，退出多选模式
        if (validSelectedIds.isEmpty) {
          _imageProvider.setMultiSelectMode(enabled: false);
        }
      }

      // 默认选中第一个item
      if (_selectedIndex == -1) {
        selectImage(0);
      }

      // 同步更新后的数据到_imageProvider
      final selectedImage = _imageProvider.selectedImage;
      if (selectedImage != null) {
        final index =
            _images.indexWhere((img) => img.fileId == selectedImage.fileId);
        if (index >= 0 && index < _images.length) {
          _imageProvider.selectedImage = _images[index];
        }
      }

      notifyListeners();
    });
  }

  void _setupThumbnailListener() {
    _resultStreamSubscription =
        _processingService.resultStream.listen((message) {
      PGLog.d(
          'ThumbnailInterface: 收到结果消息 - taskType: ${message.processorKey}, inputPath: ${message.payload.inputPath}');
      _onMediaStateChanged(message);
    });

    // 监听进度流，过滤缩略图相关进度
    _progressStreamSubscription =
        _processingService.progressStream.listen((message) {
      if (AigcTaskType.values.byName(message.processorKey) ==
          AigcTaskType.thumbnail) {
        // TODO Update progress
      }
    });
  }

  Future<void> requestThumbnail(int index) async {
    final imageData = _images[index];
    PGLog.d(
        'ViewModel: 请求缩略图 - index: $index, path: ${imageData.originalPath}');
    if (imageData.readyToDisplay) {
      return;
    }

    final executeNow = index == selectedIndex;

    final workspaceId =
        _currentEditingProjectRepository.currentWorkspace?.workspaceId;
    if (workspaceId == null) {
      PGLog.d('ThumbnailListViewModel: 无法提交缩略图任务 - 当前工作空间为空');
      return;
    }

    final outputPath = _mediaRepository
        .getResourceFilePath(
          workspaceId,
          imageData.fileId,
          MediaResourceType.previewResource,
        )
        .path;

    _processingService.submitTask(
      inputPath: imageData.originalPath,
      outputPath: outputPath,
      fileId: imageData.fileId,
      taskType: AigcTaskType.thumbnail,
      sortBy: index,
      // 直接传入图片索引
      executeNow: executeNow, // 选中的图片立即执行
    );
  }

  void releaseThumbnail(int index) {
    // 💡 安全检查：如果已经disposed或索引无效，则不执行释放操作
    if (index < 0 || index >= _images.length) {
      PGLog.d('ViewModel: 跳过无效索引的缩略图释放 - index: $index');
      return;
    }

    final imageData = _images[index];
    _processingService.removeTask(
      inputPath: imageData.originalPath,
      taskType: AigcTaskType.thumbnail,
    );
    PGLog.d('ViewModel: 释放缩略图 index=$index, path=${imageData.originalPath}');
  }

  /// 选择图片
  Future<void> selectImage(int index, {bool isControlPressed = false}) async {
    if (index < 0 || index >= _images.length) {
      return;
    }

    // 当前提示绑定避免异常
    if (_reconnectingFileId != null) {
      return;
    }

    await _guardFile(index);

    // 处理多选逻辑
    if (isControlPressed) {
      // 按下Ctrl键，进入或保持多选模式
      if (!_imageProvider.isMultiSelectMode) {
        // 进入多选模式，先将当前选中的项加入多选
        _imageProvider.setMultiSelectMode(enabled: true);
        if (_selectedIndex >= 0 && _selectedIndex < _images.length) {
          _imageProvider.addSelectedId(_images[_selectedIndex].fileId);
        }
      }

      // 切换当前点击项的选中状态
      _imageProvider.toggleSelectedId(_images[index].fileId);

      // 更新单选索引和选中图片
      _selectedIndex = index;
      _imageProvider.selectedImage = _images[index];
    } else {
      // 没有按下Ctrl键
      if (_imageProvider.isMultiSelectMode) {
        // 退出多选模式，保持选中当前点击的item
        _imageProvider.setMultiSelectMode(enabled: false);
      }

      // 正常单选逻辑
      _selectedIndex = index;
      _imageProvider.selectedImage = _images[index];
    }
  }

  Future<void> _guardFile(int index) async {
    final file = _images[index];
    // 刷新一下数据，确保数据是最新的
    final workspaceId =
        _currentEditingProjectRepository.currentWorkspace?.workspaceId;
    if (workspaceId != null) {
      _images[index] = _aigcPreviewImageItemAdapter.refreshMaskPaths(
        file,
        workspaceId,
      );
    }
    if (!file.readyToDisplay) {
      // 判断资源状态假设关键资源不存在则
      final originalFile = File(file.originalPath);
      if (!originalFile.existsSync()) {
        try {
          await _tryReconnectFile(file.fileId);
        } catch (e) {
          PGLog.e('ThumbnailListViewModel: 文件重新连接失败，取消选中操作 - $e');

          // 文件重连失败时，如果在多选模式下，从多选中移除该项
          if (_imageProvider.isMultiSelectMode) {
            _imageProvider.removeSelectedId(file.fileId);
            // 如果没有选中项了，退出多选模式
            if (_imageProvider.selectedIds.isEmpty) {
              _imageProvider.setMultiSelectMode(enabled: false);
            }
          }
        }
      }
    }
  }

  /// 设置多选模式
  void setMultiSelectMode({required bool enabled}) {
    _imageProvider.setMultiSelectMode(enabled: enabled);
  }

  void _onMediaStateChanged(AigcTaskResultMessage message) async {
    if (AigcTaskType.values.byName(message.processorKey) ==
        AigcTaskType.thumbnail) {
      _onThumbnailUpdated(message);
    } else {
      PGLog.d(
          'ThumbnailInterface: 忽略非缩略图消息 - taskType: ${message.processorKey}');
    }
  }

  void _updateImageSelection(AigcPreviewImageItem selectedImage) {
    _selectedIndex =
        _images.indexWhere((img) => img.fileId == selectedImage.fileId);
    if (-1 == _selectedIndex) {
      return;
    }

    requestThumbnail(_selectedIndex);
    notifyListeners();
    return;
  }

  void _onImageSelectionChanged() {
    final selectedImage = _imageProvider.selectedImage;
    if (null == selectedImage) {
      return;
    }

    if (_selectedIndex < 0 || _selectedIndex >= _images.length) {
      _updateImageSelection(selectedImage);
      return;
    }

    final localSelectedImage = _images[_selectedIndex];
    if (selectedImage.fileId != localSelectedImage.fileId) {
      _updateImageSelection(selectedImage);
    } else {
      // 如果有更新，则同步更新_images列表中的对应项
      if (selectedImage != localSelectedImage) {
        _images[_selectedIndex] = selectedImage;
        notifyListeners();
        return;
      }
    }
    PGLog.d('ThumbnailListViewModel: 选中图片未发生变化 - $selectedImage');
  }

  // 当缩略图等资源更新调用
  Future<void> _onThumbnailUpdated(AigcTaskResultMessage message) async {
    final fileId = message.payload.fileId;
    final result = message.resultData;

    WorkspaceFile? file =
        await _currentEditingProjectRepository.getFile(fileId);
    if (file == null) {
      return;
    }

    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      return;
    }

    // 处理缩略图生成失败的情况
    if (result == null) {
      // 标记为已处理（iconized = true），但由于没有生成预览图，readyToDisplay 将为 false
      // 这样就符合了图片损坏的判断条件：iconized == true && readyToDisplay == false
      file = file.copyWith(iconized: true);
      _currentEditingProjectRepository.updateFile(file);
      PGLog.w('ThumbnailListViewModel: 缩略图生成失败，标记为损坏 - fileId: $fileId');
      return;
    }

    // 更新资源存储
    final taskResult = message.resultData as AigcThumbnailTaskResult;

    // 处理高清图
    final highQualityPath = taskResult.highQualityPath;
    if (highQualityPath != null && highQualityPath.isNotEmpty) {
      await _mediaRepository.addOrUpdateFileResource(
        workspace.workspaceId,
        fileId,
        MediaResourceType.largeResource,
        File(highQualityPath),
      );
    }

    // 处理预览图
    final previewPath = taskResult.previewPath;
    if (previewPath != null && previewPath.isNotEmpty) {
      await _mediaRepository.addOrUpdateFileResource(
        workspace.workspaceId,
        fileId,
        MediaResourceType.previewResource,
        File(previewPath),
      );
    }

    // 处理缩略图
    final thumbnailPath = taskResult.thumbnailPath;
    if (thumbnailPath != null && thumbnailPath.isNotEmpty) {
      await _mediaRepository.addOrUpdateFileResource(
        workspace.workspaceId,
        fileId,
        MediaResourceType.minIcon,
        File(thumbnailPath),
      );

      // 同步数据库
      file = file.copyWith(iconized: true);
      _currentEditingProjectRepository.updateFile(file);
    }

    /**
     *  这里存在一定的隐患，只有当小icon生成成功后，才会更新_images列表，如果小icon生成失败，
     * 则_images列表不会更新，由于现在预览图和小图是一组任务，理论上不存在失败的情况，但
     * 假设后期做了拆分一定要注意这里的处理
     */
  }

  /// 处理选择文件
  Future<List<File>> handleFileSelection(ImageSelectionType type) async {
    try {
      DealImageFilesResult? result;
      switch (type) {
        case ImageSelectionType.files:
          result = await _imageSelectionService.pickImagesFromFiles();
          break;
        case ImageSelectionType.folder:
          result = await _imageSelectionService.pickImagesFromDirectory();
          break;
      }

      if (result != null && result.validFiles.isNotEmpty) {
        return await _addFilesToCurrentProject(result.validFiles);
      }
      return [];
    } catch (e) {
      PGLog.e('AigcThumbnailListViewModel: 选择文件时出错: $e');
      return [];
    }
  }

  /// 将文件添加到当前编辑项目
  Future<List<File>> _addFilesToCurrentProject(List<File> files) async {
    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      return [];
    }

    try {
      final workspaceFiles = await _mediaRepository.generateWorkspaceFiles(
          workspace.workspaceId, files);

      _currentEditingProjectRepository.batchAddFiles(workspaceFiles);
      return files;
    } catch (e) {
      PGLog.e('AigcThumbnailListViewModel: 添加文件到项目时出错: $e');
      return [];
    }
  }

  /// 删除当前选中的图片
  Future<void> deleteSelectedImage() async {
    if (_selectedIndex == -1 || _selectedIndex >= _images.length) {
      return;
    }

    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      return;
    }

    try {
      if (_imageProvider.isMultiSelectMode &&
          _imageProvider.selectedIds.isNotEmpty) {
        // 多选模式下删除所有选中的图片
        await _deleteMultipleImages();
      } else {
        // 单选模式下删除当前选中的图片
        await _deleteSingleImage();
      }

      notifyListeners();
    } catch (e) {
      PGLog.e('AigcThumbnailListViewModel: 删除图片时出错: $e');
    }
  }

  /// 删除单个图片
  Future<void> _deleteSingleImage() async {
    final selectedImage = _images[_selectedIndex];

    // 从repository中删除文件
    await _currentEditingProjectRepository.deleteFile(selectedImage.fileId);

    // 从本地列表中移除
    _images.removeAt(_selectedIndex);

    // 调整选中索引
    if (_images.isEmpty) {
      _selectedIndex = -1;
      _imageProvider.selectedImage = null;
    } else {
      // 如果删除的是最后一个，选中前一个；否则保持当前索引
      if (_selectedIndex >= _images.length) {
        _selectedIndex = _images.length - 1;
      }
      _imageProvider.selectedImage = _images[_selectedIndex];
    }
  }

  /// 删除多个图片
  Future<void> _deleteMultipleImages() async {
    final selectedIds = Set<String>.from(_imageProvider.selectedIds);

    // 从后往前删除，避免索引变化影响
    for (int i = _images.length - 1; i >= 0; i--) {
      if (selectedIds.contains(_images[i].fileId)) {
        final image = _images[i];
        // 从repository中删除文件
        await _currentEditingProjectRepository.deleteFile(image.fileId);
        // 从本地列表中移除
        _images.removeAt(i);
      }
    }

    // 退出多选模式
    _imageProvider.setMultiSelectMode(enabled: false);

    // 调整选中索引
    if (_images.isEmpty) {
      _selectedIndex = -1;
      _imageProvider.selectedImage = null;
    } else {
      // 选中第一个可用的图片
      _selectedIndex = 0;
      _imageProvider.selectedImage = _images[0];
    }
  }

  /// 请求显示删除确认弹窗
  void requestDeleteConfirmation() {
    if (_showDeleteConfirmDialog) {
      // 避免重复弹出确认弹窗
      return;
    }
    // 检查是否有选中的图片
    if (_selectedIndex == -1 || _selectedIndex >= _images.length) {
      return;
    }

    _showDeleteConfirmDialog = true;
    notifyListeners();
  }

  /// 隐藏删除确认弹窗
  void hideDeleteConfirmation() {
    _showDeleteConfirmDialog = false;
    notifyListeners();
  }

  /// 确认删除操作
  Future<void> confirmDelete() async {
    // 先隐藏弹窗
    hideDeleteConfirmation();
    // 执行删除
    await deleteSelectedImage();
  }

  @override
  void dispose() {
    // 取消所有流订阅
    _resultStreamSubscription?.cancel();
    _progressStreamSubscription?.cancel();
    _imageProvider.removeListener(_onImageSelectionChanged);

    // 最后释放缩略图资源，这样即使触发notifyListeners也不会影响已disposed的组件
    // 💡 关键修复：在dispose之前先获取需要释放的图片路径
    _processingService.removeTasksByType(AigcTaskType.thumbnail);

    super.dispose();
  }

  // 尝试重新链接
  Future<WorkspaceFile?> _tryReconnectFile(String fileId) async {
    final file = await _currentEditingProjectRepository.getFile(fileId);
    if (file == null) {
      PGLog.d('ThumbnailListViewModel: 文件不存在 - fileId: $fileId');
      return null;
    }
    _reconnectingFileId = fileId;
    // 使用 Completer 来阻塞等待用户操作完成
    final completer = Completer<WorkspaceFile?>();

    AigcReconnectFileDialog.show(
      fileName: file.fileName,
      onConfirm: () async {
        await AigcReconnectFileDialog.hide();
        PGDialog.showLoading();
        try {
          final newFile = await _reconnectFile(fileId);
          completer.complete(newFile); // 重新连接成功
          PGDialog.dismiss();
        } catch (e) {
          PGDialog.dismiss();
          PGLog.e('ThumbnailListViewModel: 重新连接文件失败 - $e');
          completer.complete(null); // 重新连接失败
        }
      },
      onCancel: () {
        AigcReconnectFileDialog.hide();
        completer.complete(null); // 用户取消操作
      },
    );

    // 阻塞等待用户操作完成
    final result = await completer.future;
    _reconnectingFileId = null;
    if (result == null) {
      PGLog.d('ThumbnailListViewModel: 文件重新连接未完成或失败 - fileId: $fileId');
      // 可以选择抛出异常或返回特定状态
      throw Exception('文件重新连接失败或用户取消操作');
    }

    PGLog.d('ThumbnailListViewModel: 文件重新连接成功 - fileId: $fileId');
    return result;
  }

  // 链接文件
  Future<WorkspaceFile> _reconnectFile(String fileId) async {
    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      PGLog.d('ThumbnailListViewModel: 当前工作空间为空');
      throw Exception('当前工作空间为空');
    }

    final preFile = await _currentEditingProjectRepository.getFile(fileId);
    if (preFile == null) {
      PGLog.d('ThumbnailListViewModel: 文件不存在 - fileId: $fileId');
      throw Exception('文件不存在');
    }

    try {
      // 使用文件选择服务让用户选择新的文件
      final result = await _imageSelectionService.pickImagesFromFiles();

      if (result == null || result.validFiles.isEmpty) {
        throw Exception('用户未选择文件');
      }

      final temp = result.validFiles.first; // 只取第一个文件
      PGLog.d('ThumbnailListViewModel: 用户选择新文件 - path: ${temp.path}');

      final workspaceFiles = await _mediaRepository.generateWorkspaceFiles(
        workspace.workspaceId,
        [temp],
      );
// 由于在业务层以创建时间排序，为了达到替换的效果，则需要将新文件的创建时间设置为旧文件的创建时间
      final newFile = workspaceFiles.first.copyWith(
        createTime: preFile.createTime,
      );

      // 删除关联资源，重新生成
      final res = [
        MediaResourceType.mask,
        MediaResourceType.minIcon,
        MediaResourceType.previewResource,
        MediaResourceType.largeResource,
      ];

      for (final resourceType in res) {
        await _mediaRepository.deleteFileResource(
          workspace.workspaceId,
          fileId,
          resourceType,
        );
      }

      // 删除临时文件中所有的蒙版
      _fileManager
          .getTempInteractiveMaskDirectory(
            workspace.workspaceId,
            fileId,
          )
          .deleteSync(recursive: true);

      // 更新数据库
      await _currentEditingProjectRepository.replaceFile(
        fileId,
        newFile,
      );
      return newFile;
    } catch (e) {
      PGLog.e('ThumbnailListViewModel: 重新连接文件失败 - $e');
      rethrow; // 重新抛出异常，让调用者知道操作失败
    }
  }
}
