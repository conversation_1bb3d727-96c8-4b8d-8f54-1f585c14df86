import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/datalayer/domain/models/aigc_presets/aigc_presets_model.internal.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_request.dart';
import 'package:turing_art/datalayer/domain/models/file/deal_image_files_result.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/repository/current_editing_project_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository.dart';
import 'package:turing_art/datalayer/repository/media_repository_impl.dart';
import 'package:turing_art/datalayer/repository/media_upload/media_upload_repository.dart';
import 'package:turing_art/ffi/services/image_processor_service.dart';
import 'package:turing_art/providers/project_state_provider.dart';
import 'package:turing_art/routing/navigator_service.dart';
import 'package:turing_art/ui/aigc_editing/header/aigc_editing_header.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_history_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_preferred_mask_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_image_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_editing_theme_list_provider.dart';
import 'package:turing_art/ui/aigc_editing/providers/aigc_regional_frame_provider.dart';
import 'package:turing_art/utils/pg_log.dart';

class AigcEditingSceneViewModel extends ChangeNotifier {
  final CurrentEditingProjectRepository _currentEditingProjectRepository;
  final AigcSampleRepository _aigcSampleRepository;
  final ProjectStateProvider _projectStateProvider;
  final NavigatorService _navigator;
  final AigcEditingImageProvider _imageProvider; // 共享数据Provider
  final AigcEditingThemeListProvider _themeListProvider;
  final AigcEditingImagePreferredMaskProvider _preferredMaskProvider;
  // 媒体上传管理
  final MediaUploadRepository _mediaUploadRepository;

  final MediaRepository _mediaRepository;

  // 历史记录管理
  final AigcEditingHistoryProvider _aigcEditingHistoryProvider;

  // 区域框Provider
  final AigcRegionalFrameProvider _aigcRegionalFrameProvider;

  // 消耗的积分
  final int _costValue = 3;

  // 已经打样过的图片ID
  final List<String> _processedImageIds = [];

  // 全屏加载状态
  bool _isCreatingSample = false;

  // 添加处理状态和积分消耗的getter
  ProcessState get processState => _themeListProvider.processState;
  int get costValue => _costValue;

  // 获取全屏加载状态
  bool get isCreatingSample => _isCreatingSample;

  // 添加更新处理状态的方法
  void updateProcessState(ProcessState state) {
    _themeListProvider.setProcessState(state);
    notifyListeners();
  }

  // 设置全屏加载状态
  void _setCreatingSampleState(bool isCreating) {
    if (_isCreatingSample != isCreating) {
      _isCreatingSample = isCreating;
      notifyListeners();
    }
  }

  AigcEditingSceneViewModel({
    required CurrentEditingProjectRepository currentEditingProjectRepository,
    required AigcSampleRepository aigcSampleRepository,
    required MediaUploadRepository mediaUploadRepository,
    required NavigatorService navigator,
    required ProjectStateProvider projectStateProvider,
    required AigcEditingImageProvider imageProvider, // 注入共享数据Provider
    required AigcEditingThemeListProvider themeListProvider,
    required MediaRepository mediaRepository,
    required AigcEditingHistoryProvider aigcEditingHistoryProvider,
    required AigcRegionalFrameProvider aigcRegionalFrameProvider,
    required AigcEditingImagePreferredMaskProvider preferredMaskProvider,
  })  : _currentEditingProjectRepository = currentEditingProjectRepository,
        _navigator = navigator,
        _projectStateProvider = projectStateProvider,
        _imageProvider = imageProvider,
        _aigcSampleRepository = aigcSampleRepository,
        _mediaUploadRepository = mediaUploadRepository,
        _themeListProvider = themeListProvider,
        _mediaRepository = mediaRepository,
        _aigcEditingHistoryProvider = aigcEditingHistoryProvider,
        _aigcRegionalFrameProvider = aigcRegionalFrameProvider,
        _preferredMaskProvider = preferredMaskProvider {
    _imageProvider.addListener(_onSharedDataChanged);
    _aigcEditingHistoryProvider.addListener(_onSharedDataChanged);
    _aigcEditingHistoryProvider.addListener(_onHistoryChanged);
  }

  /// 返回
  Future<void> onBackPressed() async {
    try {
      await _currentEditingProjectRepository.exitWorkspace();
      _projectStateProvider.exitEdit();

      // 使用微任务确保在下一个事件循环中执行导航，避免Navigator状态冲突
      await Future.microtask(() {
        _navigator.pop();
      });
    } catch (e) {
      PGLog.e('返回操作失败: $e');
      // 即使出错也要尝试返回，但要安全地执行
      await Future.microtask(() {
        _navigator.pop();
      });
    }
  }

  @override
  void dispose() {
    // 清理缩略图资源
    // releaseAllThumbnails();
    _imageProvider.removeListener(_onSharedDataChanged);
    _aigcEditingHistoryProvider.removeListener(_onSharedDataChanged);
    _aigcEditingHistoryProvider.removeListener(_onHistoryChanged);
    super.dispose();
  }

  // MARK: 监听选中图片逻辑
  // 添加监听回调方法
  void _onSharedDataChanged() {
    // 检查是否有图片选择变化
    if (_imageProvider.hasImageSelectionChanged) {
      // 获取当前选中的图片
      final selectedImage = _imageProvider.selectedImage;

      if (selectedImage != null) {
        // 判断该图片是否已经打样过
        final isProcessed = isProcessedImage(selectedImage.fileId);

        if (isProcessed) {
          updateProcessState(ProcessState.processed);
        } else {
          updateProcessState(ProcessState.initial);
        }

        // 历史记录同步更新对应的容器
        _aigcEditingHistoryProvider.setCurrentImage(selectedImage.fileId);
      }
    }
  }

  void _onHistoryChanged() {
    final historyStates =
        _aigcEditingHistoryProvider.getCurrentHistoryValidRegionalStates();
    _aigcRegionalFrameProvider
        .reloadRegionalFramesFromHistoryStates(historyStates);
  }

  // 判断是否打样过
  bool isProcessedImage(String fileId) {
    return _processedImageIds.contains(fileId);
  }

  // MARK: 创建打样
  // isOriginal 是否为原图打样
  // 如果isOriginal为true，则不使用创意和主题，直接使用原图打样
  // 如果isOriginal为false，则使用创意和主题，进行打样,此时需要判断创意和主题是否存在
  Future<(bool, String?)> createSample({bool isOriginal = false}) async {
    // 设置全屏加载状态为true
    _setCreatingSampleState(true);

    final selectedEffect = _themeListProvider.selectedEffect;
    final presetsModel = _themeListProvider.selectedPreset;
    if (!isOriginal && (selectedEffect == null || presetsModel == null)) {
      PGLog.e('不是原图打样，但是创意和主题为空');
      _setCreatingSampleState(false);
      return (false, '未选择主题和创意');
    }
    String effectCode = isOriginal ? '' : selectedEffect?.effectCode ?? '';
    // 原图增强effectCode为空
    if (effectCode == AigcPresetsModelInternal.internalAIGCEffectId) {
      effectCode = '';
    }
    String presetId = isOriginal ? '' : presetsModel!.id;
    // 原图增强presetId为空
    if (presetId == AigcPresetsModelInternal.internalAIGCPresetId) {
      presetId = '';
    }

    // 获取当前项目ID
    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      PGLog.e('工作区为空');
      updateProcessState(ProcessState.disabled);
      _setCreatingSampleState(false);
      return (false, null);
    }

    // 获取当前选中的图片
    final selectedImage = _imageProvider.selectedImage;
    if (selectedImage == null) {
      PGLog.e('未选择图片');
      updateProcessState(ProcessState.disabled);
      _setCreatingSampleState(false);
      return (false, null);
    }
    // 如果预览图路径为空
    if (selectedImage.previewPath == null ||
        selectedImage.previewPath!.isEmpty) {
      PGLog.e('预览图路径为空');
      updateProcessState(ProcessState.disabled);
      _setCreatingSampleState(false);
      return (false, null);
    }

    // 更新状态为加载中
    updateProcessState(ProcessState.loading);

    try {
      String originImageResultUrl = '';
      // 1.上传原图（对于当前步骤不是必须的）
      originImageResultUrl = await _uploadLargeImageIfNeed(
          workspace.workspaceId,
          selectedImage.fileId,
          selectedImage.highQualityPath ?? '');
      PGLog.d('createSample上传原图成功: $originImageResultUrl');

      // 2.上传预览图
      final previewImageUrl =
          await _uploadOriginImage(selectedImage.previewPath ?? '');
      if (previewImageUrl.isEmpty) {
        PGLog.e('上传预览图失败');
        if (isProcessedImage(selectedImage.fileId)) {
          updateProcessState(ProcessState.processed);
        } else {
          updateProcessState(ProcessState.initial);
        }
        _setCreatingSampleState(false);
        return (false, null);
      }
      PGLog.d('createSample上传预览图成功: $previewImageUrl');

      // 3.上传遮罩图,蒙版可以为空，因此不需要判断路径是否存在
      // 先处理遮罩图
      final processedMaskPath = await _processMaskImage(
        _preferredMaskProvider.currentImagePreferredMaskPath,
      );
      final maskImageUrl = await _uploadMaskImage(processedMaskPath ?? '');
      PGLog.d('createSample上传遮罩图成功: $maskImageUrl');

      // 4.原图和遮罩图上传成功后，执行创建打样请求
      // 创建打样请求
      final request = AigcSampleRequest(
        clientProject: ClientProject(
          projectId: workspace.workspaceId,
          projectName: workspace.workspaceName,
          updateAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        ),
        preset: Preset(
          presetId: presetId,
          effectCode: effectCode,
        ),
        originPhoto: OriginPhoto(
          fileName: path.basenameWithoutExtension(selectedImage.originalPath),
          fileUrl: previewImageUrl,
        ),
        maskImageUrl: maskImageUrl,
        clientResourceId: selectedImage.fileId,
      );

      // 调用创建打样接口
      final result = await _aigcSampleRepository.createAigcSample(request);
      if (result == null) {
        PGLog.e('创建打样失败');
        if (isProcessedImage(selectedImage.fileId)) {
          updateProcessState(ProcessState.processed);
        } else {
          updateProcessState(ProcessState.initial);
        }
        _setCreatingSampleState(false);
        return (false, null);
      }

      // 5.更新样片详情,告诉服务端大图已经上传
      if (originImageResultUrl.isNotEmpty) {
        await _aigcSampleRepository.updateAigcSampleInfo(
            result.id, originImageResultUrl);
      }
      // 延迟2秒后设置为处理完成状态
      await Future.delayed(const Duration(seconds: 2));
      updateProcessState(ProcessState.processed);
      // 添加已经打样过的图片ID
      _processedImageIds.add(selectedImage.fileId);
      _setCreatingSampleState(false);
      return (true, null);
    } catch (e) {
      PGLog.e('创建打样失败: $e');
      // 延迟2秒后设置为初始状态
      await Future.delayed(const Duration(seconds: 2));
      if (isProcessedImage(selectedImage.fileId)) {
        updateProcessState(ProcessState.processed);
      } else {
        updateProcessState(ProcessState.initial);
      }
      _setCreatingSampleState(false);
      return (false, null);
    }
  }

  Future<String> _uploadLargeImageIfNeed(
      String projectId, String fileId, String largeImagePath) async {
    String originImageResultUrl = '';
    final (isNeedUpload, largeImageUrl) =
        await _isNeedUploadOriginImage(projectId, fileId);
    if (isNeedUpload && largeImageUrl == null) {
      if (largeImagePath.isEmpty) {
        PGLog.e('createSample上传原图失败，大图路径为空');
        return '';
      }
      originImageResultUrl = await _uploadOriginImage(largeImagePath);
      if (originImageResultUrl.isEmpty) {
        PGLog.e('createSample上传原图失败');
        return '';
      }
    } else {
      // 不需要上传，已有原图url
      Uri uri = Uri.parse(largeImageUrl!);
      // 直接去掉查询参数
      originImageResultUrl = uri.replace(query: '').toString();
      originImageResultUrl = originImageResultUrl.replaceAll('?', '');
      PGLog.d('createSample已有原图不需要上传: $originImageResultUrl');
    }
    return originImageResultUrl;
  }

  // 判断是否需要上传大图,不需要上传返回原图url用于更新当前打样的信息（2025-07-03修改需求）
  Future<(bool, String?)> _isNeedUploadOriginImage(
      String projectId, String fileId) async {
    try {
      // 获取所有项目的打样列表
      final samples = await _aigcSampleRepository.getAigcSampleList('');

      // 查找当前项目ID和资源ID对应的打样
      AigcSampleModel? targetSample;
      try {
        targetSample = samples.firstWhere(
          (sample) =>
              sample.projectId == projectId &&
              sample.clientResourceId == fileId,
        );
      } catch (e) {
        // 没有找到匹配的打样
        targetSample = null;
      }

      // 如果找到了对应的打样，且isLargeImageUploaded为true，则不需要上传
      if (targetSample != null && targetSample.isLargeImageUploaded) {
        return (false, targetSample.largePhotoUrl);
      }

      // 其他情况都需要上传
      return (true, null);
    } catch (e) {
      PGLog.e('获取打样列表失败: $e');
      // 出错时默认需要上传
      return (true, null);
    }
  }

  Future<bool> onDragDone(DealImageFilesResult file) async {
    if (file.validFiles.isEmpty) {
      PGLog.e('拖拽文件为空');
      return false;
    }
    final workspace = _currentEditingProjectRepository.currentWorkspace;
    if (workspace == null) {
      PGLog.e('工作区为空');
      return false;
    }

    final files = await _mediaRepository.generateWorkspaceFiles(
      workspace.workspaceId,
      file.validFiles,
    );

    await _currentEditingProjectRepository.batchAddFiles(files);
    return true;
  }

  Future<String> _uploadOriginImage(String imagePath) async {
    if (imagePath.isEmpty) {
      PGLog.e('未选择图片');
      return '';
    }

    final result = await _mediaUploadRepository.uploadSingleFile(
      File(imagePath),
    );

    if (result.success) {
      return result.publicUrl ?? '';
    }

    return '';
  }

  Future<String> _uploadMaskImage(String imagePath) async {
    if (imagePath.isEmpty) {
      return '';
    }

    final result = await _mediaUploadRepository.uploadSingleFile(
      File(imagePath),
    );

    if (result.success) {
      return result.publicUrl ?? '';
    }

    return '';
  }

  /// 处理遮罩图像
  /// 1. 检查路径是否为空，为空则跳过
  /// 2. 检查同级目录下的uploadMask.png文件，存在则删除
  /// 3. 使用convertRGBAFromR方法处理图像并保存为uploadMask.png
  /// 4. 返回处理后的文件路径
  Future<String?> _processMaskImage(String? maskPath) async {
    try {
      // 1. 如果遮罩路径为空则跳过
      if (maskPath == null || maskPath.isEmpty) {
        PGLog.d('遮罩图路径为空，跳过处理');
        return null;
      }

      // 检查原始遮罩文件是否存在
      final maskFile = File(maskPath);
      if (!maskFile.existsSync()) {
        PGLog.e('遮罩图文件不存在: $maskPath');
        return null;
      }

      // 2. 获取同级目录并检查uploadMask.png文件
      final maskDirectory = maskFile.parent;
      final uploadMaskPath = path.join(
          maskDirectory.path, MediaResourceConstants.getPsUploadMaskFileName());
      final uploadMaskFile = File(uploadMaskPath);

      // 如果uploadMask.png存在则删除
      if (uploadMaskFile.existsSync()) {
        await uploadMaskFile.delete();
        PGLog.d('删除已存在的uploadMask.png文件: $uploadMaskPath');
      }

      // 3. 使用processFile方法处理图像，最大边缩放到1440的PNG
      const config = ImageProcessorConfig(
        targetWidth: ImageConstants.aigcPreviewSize,
        targetHeight: ImageConstants.aigcPreviewSize,
        maintainAspect: true,
        quality: 95,
        outputFormat: 'png',
        interpolation: ImageInterpolation.cubic,
      );

      await ImageProcessorService.initialize();

      await ImageProcessorService.processFile(
        maskPath,
        uploadMaskPath,
        config: config,
      );

      PGLog.d('遮罩图处理成功: $uploadMaskPath');
      return uploadMaskPath;
    } catch (e) {
      PGLog.e('处理遮罩图失败: $e');
      return null;
    }
  }
}
