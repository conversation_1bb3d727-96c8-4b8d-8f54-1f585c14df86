import 'package:flutter/material.dart';

/// AIGC缩略图相关常量定义
class AigcThumbnailConstants {
  // 私有构造函数，防止实例化
  AigcThumbnailConstants._();

  /// 多选模式相关常量
  static const Color multiSelectBorderColor = Color(0xFF007AFF);
  static const double multiSelectBorderWidth = 2.0;
  static const double multiSelectIndicatorSize = 16.0;
  static const double multiSelectIndicatorIconSize = 12.0;

  /// 退出多选按钮相关常量
  static const Color exitButtonBackgroundColor = Color(0xFF5A5A5A);
  static const Color exitButtonBorderColor = Color(0xFF707070);
  static const double exitButtonBorderWidth = 1.0;
  static const double exitButtonBorderRadius = 4.0;
  static const EdgeInsets exitButtonPadding = EdgeInsets.symmetric(horizontal: 8, vertical: 4);
  static const double exitButtonFontSize = 10.0;

  /// 缩略图相关常量
  static const double thumbnailCornerRadius = 8.0;
  static const double thumbnailMargin = 2.0;

  /// 文本样式相关常量
  static const double titleFontSize = 12.0;
  static const Color titleTextColor = Colors.white;

  /// 多选模式提示文本
  static const String multiSelectModeNoSelection = '多选模式 - 未选择图片';
  static const String multiSelectModeSingleSelection = '多选模式 - 已选择1张图片';
  static const String multiSelectModeMultipleSelection = '多选模式 - 已选择{count}张图片';
  static const String exitMultiSelectButtonText = '退出多选';

  /// 删除确认对话框文本
  static const String deleteConfirmTitleSingle = '确认删除此文件吗？';
  static const String deleteConfirmTitleMultiple = '确认删除选中的{count}个文件吗？';
  static const String deleteConfirmContent = '文件将仅从图灵精修中删除，磁盘中的原始文件会保留。';

  /// 错误提示文本
  static const String fileReconnectFailedMessage = '文件连接失败，请重试';
  static const String deleteOperationFailedMessage = '删除操作失败，请重试';

  /// 状态文本
  static const String noImageSelected = '未选择图片';
  static const String noImages = '无图片';

  /// 获取多选模式选择数量文本
  static String getMultiSelectCountText(int count) {
    if (count == 0) {
      return multiSelectModeNoSelection;
    } else if (count == 1) {
      return multiSelectModeSingleSelection;
    } else {
      return multiSelectModeMultipleSelection.replaceAll('{count}', count.toString());
    }
  }

  /// 获取删除确认标题文本
  static String getDeleteConfirmTitle(int count) {
    if (count <= 1) {
      return deleteConfirmTitleSingle;
    } else {
      return deleteConfirmTitleMultiple.replaceAll('{count}', count.toString());
    }
  }
}
