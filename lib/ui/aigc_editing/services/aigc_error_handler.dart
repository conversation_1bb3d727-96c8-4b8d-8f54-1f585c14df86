import 'package:flutter/material.dart';
import '../../../common/utils/pg_log.dart';
import '../constants/aigc_thumbnail_constants.dart';

/// AIGC编辑器错误处理服务
class AigcErrorHandler {
  // 私有构造函数，防止实例化
  AigcErrorHandler._();

  /// 处理文件重连失败错误
  static void handleFileReconnectError(
    BuildContext? context,
    String fileId,
    dynamic error, {
    VoidCallback? onRetry,
  }) {
    PGLog.e('文件重新连接失败 - fileId: $fileId, error: $error');
    
    if (context != null) {
      _showErrorSnackBar(
        context,
        AigcThumbnailConstants.fileReconnectFailedMessage,
        action: onRetry != null
            ? SnackBarAction(
                label: '重试',
                onPressed: onRetry,
              )
            : null,
      );
    }
  }

  /// 处理删除操作失败错误
  static void handleDeleteError(
    BuildContext? context,
    dynamic error, {
    VoidCallback? onRetry,
  }) {
    PGLog.e('删除操作失败 - error: $error');
    
    if (context != null) {
      _showErrorSnackBar(
        context,
        AigcThumbnailConstants.deleteOperationFailedMessage,
        action: onRetry != null
            ? SnackBarAction(
                label: '重试',
                onPressed: onRetry,
              )
            : null,
      );
    }
  }

  /// 处理文件选择错误
  static void handleFileSelectionError(
    BuildContext? context,
    dynamic error,
  ) {
    PGLog.e('文件选择失败 - error: $error');
    
    if (context != null) {
      _showErrorSnackBar(
        context,
        '文件选择失败，请重试',
      );
    }
  }

  /// 处理文件添加到项目错误
  static void handleAddFilesToProjectError(
    BuildContext? context,
    dynamic error,
  ) {
    PGLog.e('添加文件到项目失败 - error: $error');
    
    if (context != null) {
      _showErrorSnackBar(
        context,
        '添加文件失败，请重试',
      );
    }
  }

  /// 显示错误提示的SnackBar
  static void _showErrorSnackBar(
    BuildContext context,
    String message, {
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red[600],
        action: action,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示成功提示的SnackBar
  static void showSuccessMessage(
    BuildContext context,
    String message,
  ) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green[600],
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
