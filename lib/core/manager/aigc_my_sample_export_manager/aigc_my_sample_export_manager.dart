import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_export_path_info.dart';
import 'package:turing_art/core/manager/aigc_my_sample_export_manager/aigc_my_sample_models.dart';
import 'package:turing_art/datalayer/domain/enums/export_status.dart';
import 'package:turing_art/datalayer/domain/enums/user_info_change_event_type.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_effect_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_project_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_export_status_model.dart';
import 'package:turing_art/datalayer/domain/models/aigc_sample/aigc_sample_list_export_request.dart';
import 'package:turing_art/datalayer/repository/aigc_sample/aigc_sample_repository.dart';
import 'package:turing_art/datalayer/repository/current_user_repository.dart';
import 'package:turing_art/datalayer/service/share_preferences/user_preferences_service.dart';
import 'package:turing_art/providers/aigc_my_export_list_polling_provider.dart';
import 'package:turing_art/providers/network_provider.dart';
import 'package:turing_art/ui/aigc_presets/utils/aigc_request_const.dart';
import 'package:turing_art/ui/aigc_sample/services/file_path_service.dart';
import 'package:turing_art/utils/color_grading/color_grading_manager.dart';
import 'package:turing_art/utils/color_grading/color_grading_task.dart';
import 'package:turing_art/utils/download/download_manager.dart';
import 'package:turing_art/utils/download/download_task.dart';
import 'package:turing_art/utils/pg_dialog/pg_dialog.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 重试导出刷新事件
class RetryExportRefreshEvent {
  /// 涉及的打样ID列表
  final List<String> proofingIds;

  RetryExportRefreshEvent({required this.proofingIds});
}

/// 我的当前设备AIGC样本导出管理器
class AigcMySampleExportManager extends ChangeNotifier {
  final AigcSampleRepository _repository;
  final CurrentUserRepository _currentUserRepository;

  // 我的打样导出状态轮询提供者
  final AigcMyExportListPollingProvider _pollingProvider;
  // 网络状态提供者
  final NetworkProvider _networkProvider;
  // 文件路径服务
  final FilePathService _filePathService = FilePathService.forPlatform();
  final DownloadManager _downloadManager = DownloadManager();
  final ColorGradingManager _colorGradingManager = ColorGradingManager();

  /// 组装下载任务ID
  /// [proofingId] 打样ID
  /// [effectCode] 效果码
  /// 返回格式: "proofingId_effectCode"
  static String buildTaskId(String proofingId, String effectCode) {
    return '${proofingId}_$effectCode';
  }

  /// 拆分下载任务ID
  /// [taskId] 任务ID，格式: "proofingId_effectCode"
  /// 返回 [proofingId, effectCode] 数组，如果格式不正确返回空数组
  static List<String> parseTaskId(String taskId) {
    final parts = taskId.split('_');
    if (parts.length >= 2) {
      // 支持effectCode中包含下划线的情况
      final proofingId = parts[0];
      final effectCode = parts.sublist(1).join('_');
      return [proofingId, effectCode];
    }
    return [];
  }

  // 重试导出刷新事件流控制器
  final StreamController<RetryExportRefreshEvent> _retryRefreshController =
      StreamController<RetryExportRefreshEvent>.broadcast();

  /// 重试导出刷新事件流
  Stream<RetryExportRefreshEvent> get retryRefreshStream =>
      _retryRefreshController.stream;

  // 导出列表数据（effect里面需要服务端返回effectCode方便轮询）
  List<AigcSampleExportProjectModel> _exportProjectList = [];

  // 导出数据Map缓存，方便根据打样项目ID快速查找
  final Map<String, AigcSampleExportProjectModel> _exportDataMap = {};

  // UI模型列表
  final List<AigcMySampleExportUIModel> _uiModelList = [];
  List<AigcMySampleExportUIModel> get uiModelList => _uiModelList;

  // 需要轮询的请求列表
  final List<AigcSampleListExportRequest> _pollingRequests = [];

  // 缓存上次的状态Map，用于检测状态变化 key: proofingId_effectCode, value: status
  final Map<String, ExportStatus> _lastStatusMap = {};

  // 用户信息变更事件流
  StreamSubscription<UserInfoChangeEventType>?
      _currentUserInfoChangeSubscription;

  //  监听自己的导出状态更新事件
  StreamSubscription<String>? _exportStatusChangeSubscription;

  // 网络状态跟踪
  bool _lastNetworkConnected = false;

  // UI模型生成锁，防止重复执行
  bool _isGeneratingUIModels = false;
  bool _needsRegenerateUIModels = false; // 标记是否需要重新生成

  AigcMySampleExportManager({
    required AigcSampleRepository repository,
    required CurrentUserRepository currentUserRepository,
    required AigcMyExportListPollingProvider pollingProvider,
    required NetworkProvider networkProvider,
  })  : _repository = repository,
        _currentUserRepository = currentUserRepository,
        _pollingProvider = pollingProvider,
        _networkProvider = networkProvider {
    _initializeDataStream();
  }

  /// 初始化数据流监听
  void _initializeDataStream() {
    _exportStatusChangeSubscription =
        _repository.dataChangeStream.listen((eventType) {
      // 监听自己的导出状态更新事件
      if (eventType.startsWith(AigcRequestConst.myListExportStatusRefresh)) {
        // 获取最新的导出状态，检查是否包含完成状态
        _checkExportStatusAndRefresh();
      }
    });

    // 监听用户信息变更事件,user有值后开始流程
    _currentUserInfoChangeSubscription = _currentUserRepository
        .currentUserInfoChange
        .listen(_handleCurrentUserInfoChangeEvent);

    // 监听网络状态变更事件
    _networkProvider.addListener(_handleNetworkChange);
    // 初始化网络状态
    _initializeNetworkStatus();
  }

  /// 处理用户信息变更事件（user是异步获取，需要监听user信息变更事件）
  void _handleCurrentUserInfoChangeEvent(UserInfoChangeEventType event) {
    PGLog.d(
        'AigcMySampleExportManager - _handleCurrentUserInfoChangeEvent $event');
    switch (event) {
      case UserInfoChangeEventType.userRefreshed:
        // 项目启动后user信息有值立即开始流程，获取项目需要依赖user
        refreshExportList();
        break;
      default:
        break;
    }
  }

  /// 刷新导出列表数据
  Future<void> refreshExportList() async {
    try {
      PGLog.d('开始刷新AIGC样本导出列表');

      // 1. 拉取导出列表数据
      final latestExportList = await _repository.getAigcSampleExportList();

      // 2. 更新本地数据
      _exportProjectList = latestExportList;

      // 打印导出列表数据
      PGLog.d('刷新导出列表数据:$_exportProjectList');

      // 3. 更新导出数据Map缓存
      _updateExportDataMap();

      // 4. 处理下载队列和轮询
      await _processDownloadAndPolling();

      // 5. 生成UI模型
      await _generateUIModels();

      // 6. 通知UI更新
      notifyListeners();

      PGLog.d('AIGC样本导出列表刷新完成，共${_exportProjectList.length}个项目');
    } catch (e) {
      PGLog.e('刷新AIGC样本导出列表失败: $e');
    }
  }

  /// 更新导出数据Map缓存
  void _updateExportDataMap() {
    _exportDataMap.clear();
    for (final exportProjectModel in _exportProjectList) {
      _exportDataMap[exportProjectModel.projectId] = exportProjectModel;
    }
    PGLog.d('更新导出数据Map缓存，共${_exportDataMap.length}个项目');
  }

  /// 根据打样项目ID查找对应的AigcSampleExportModel
  AigcSampleExportProjectModel? _findExportProjectModel(String projectId) {
    return _exportDataMap[projectId];
  }

  /// 根据打样ID查找对应的AigcSampleExportModel
  AigcSampleExportEffectModel? _findExportEffectModel(
      String projectId, String proofingId, String effectCode) {
    final project = _exportDataMap[projectId];
    if (project == null) {
      return null;
    }

    try {
      final proofing =
          project.proofings.firstWhere((proofing) => proofing.id == proofingId);
      return proofing.exports
          .firstWhere((effect) => effect.effectCode == effectCode);
    } catch (e) {
      // 找不到匹配的 proofing 或 effect，返回 null
      return null;
    }
  }

  /// 删除（已完成）打样项目导出记录
  Future<void> deleteProofingExport(List<String> projectIds) async {
    try {
      PGLog.d('开始删除打样导出记录，ID列表: $projectIds');

      for (final projectId in projectIds) {
        // 1. 需求更改不删除本地文件
        // await _deleteLocalFileWithProjectId(projectId,
        //     deleteEmptyFolder: false);
        // 2. 调用删除接口
        await _repository
            .deleteAigcSampleExport(_buildDeleteRequestList(projectId));
      }

      // 3. 刷新导出列表（ui刷新）
      await refreshExportList();

      PGLog.d('删除打样导出记录成功');
      PGDialog.showToast('删除成功');
    } catch (e) {
      PGLog.e('删除打样导出记录失败: $e');
      PGDialog.showToast('删除失败，请稍后重试');
      // 即使删除失败，也要刷新列表以确保UI状态正确
      await refreshExportList();
      rethrow;
    }
  }

  /// 外部重试导出(返回是否成功)
  Future<bool> retryExport(String projectId) async {
    PGLog.d('重试导出，项目ID: $projectId');
    // 找到对应的打样项目
    final exportProjectModel = _findExportProjectModel(projectId);
    if (exportProjectModel == null) {
      PGLog.e('未找到导出模型，无法重试导出: $projectId');
      return false;
    }
    // 找到导出状态为失败的效果id(有记录一定是不会再上传大图了) map<打样项目ID, List<效果ID>>
    final Map<String, List<String>> failedEffectIds = {};
    for (final proofing in exportProjectModel.proofings) {
      for (final effect in proofing.exports) {
        if (ExportStatus.fromString(effect.status).isFailed) {
          failedEffectIds[proofing.id] ??= [];
          failedEffectIds[proofing.id]!.add(effect.effectCode);
        }
      }
    }
    try {
      // 提交重新导出命令
      for (final proofingId in failedEffectIds.keys) {
        for (final effectCode in failedEffectIds[proofingId] ?? []) {
          PGLog.d('重试导出，打样ID: $proofingId，效果ID: $effectCode');
          await _repository.exportAigcSample(proofingId, effectCode);
        }
      }
      PGLog.d('重试导出，提交重新导出命令成功');

      // 重试成功后，发送刷新事件
      _sendRetryRefreshEvent(failedEffectIds.keys.toList());

      // 刷新导出列表，变成处理中,轮询导出状态开始(成功需要刷新，避免UI不更新)
      await refreshExportList();
      return true;
    } catch (e) {
      PGLog.e('重试导出失败: $e');
      // 刷新导出列表，变成处理中,轮询导出状态开始(部分成功也需要刷新，避免UI不更新)
      await refreshExportList();
      return false;
    }
  }

  /// 发送重试刷新事件
  void _sendRetryRefreshEvent(List<String> proofingIds) {
    try {
      final event = RetryExportRefreshEvent(proofingIds: proofingIds);
      _retryRefreshController.add(event);
      PGLog.d('重试导出成功，已发送刷新事件，涉及的打样ID: $proofingIds');
    } catch (e) {
      PGLog.e('发送重试刷新事件失败: $e');
    }
  }

  /// 构建删除请求列表(当前项目下的所有打样记录打样记录)
  List<AigcSampleListExportRequest> _buildDeleteRequestList(String projectId) {
    final List<AigcSampleListExportRequest> deleteRequests = [];
    final exportProjectModel = _findExportProjectModel(projectId);
    if (exportProjectModel == null) {
      PGLog.e('未找到导出项目，无法构建删除请求列表: $projectId');
      return [];
    }
    for (final proofing in exportProjectModel.proofings) {
      // 遍历项目中的所有效果
      for (final effect in proofing.exports) {
        // 构建删除请求
        deleteRequests.add(AigcSampleListExportRequest(
          proofingId: proofing.id,
          effectCode: effect.effectCode,
        ));
      }
    }
    PGLog.d(
        '构建删除请求列表，打样项目共${exportProjectModel.proofings.length}个，导出效果共${deleteRequests.length}个');
    return deleteRequests;
  }

  /// 统一处理下载队列和轮询
  Future<void> _processDownloadAndPolling() async {
    // 1. 追加新完成的项目到下载队列
    await _appendNewCompletedItemsToDownloadAndColorGradingQueue();

    // 2. 更新轮询请求列表
    _updatePollingRequests();
  }

  /// 追加新完成的项目到下载或者调色队列
  Future<void> _appendNewCompletedItemsToDownloadAndColorGradingQueue() async {
    final downloadTasks = <DownloadTask>[]; // 直接使用Task而不是Params
    final colorGradingTasks = <ColorGradingTask>[]; // 直接使用Task而不是Params

    for (final exportProjectModel in _exportProjectList) {
      for (final proofing in exportProjectModel.proofings) {
        for (int i = 0; i < proofing.exports.length; i++) {
          final effect = proofing.exports[i];
          final effectCode = effect.effectCode;
          final businessTaskId =
              buildTaskId(proofing.id, effectCode); // 使用公共方法组装TaskID
          final status = ExportStatus.fromString(effect.status);
          if (status.isCompleted &&
              effect.exportPhotoUrl != null &&
              effect.exportPhotoUrl!.isNotEmpty) {
            // 如果已经下载过了，跳过文件检查和下载队列添加
            if (effect.isDownload) {
              PGLog.d('效果已标记为已下载，跳过下载检查，暂时不处理调色: $effectCode');
              continue;
            }

            // 检查文件是否已存在(不存在项目不会走到此处产生空文件夹)
            final exportPathInfo = ExportPathInfo.fromExportModel(
                exportProjectModel, proofing.id, effectCode);
            final targetPath =
                await _getProofingExportFilePathInternal(exportPathInfo);
            final file = File(targetPath);

            if (!file.existsSync()) {
              // 文件未下载，添加到下载任务列表（如果任务不存在）
              _addDownloadTaskIfNotExists(
                downloadTasks,
                businessTaskId,
                effect.exportPhotoUrl!,
                targetPath,
              );
            } else {
              // 文件已下载，检查是否需要调色
              _addColorGradingTaskIfNeeded(
                colorGradingTasks,
                businessTaskId,
                targetPath,
              );
            }
          }
        }
      }
    }

    // 批量启动下载任务
    if (downloadTasks.isNotEmpty) {
      _startDownloadTasks(downloadTasks);
    }

    // 批量启动调色任务
    if (colorGradingTasks.isNotEmpty) {
      _startColorGradingTasks(colorGradingTasks);
    }
  }

  /// 添加下载任务（如果任务不存在）
  void _addDownloadTaskIfNotExists(
    List<DownloadTask> downloadTasks,
    String businessTaskId,
    String url,
    String filePath,
  ) {
    // 简化检查：如果管理器中已有任务，直接跳过
    if (_downloadManager.allTasks.containsKey(businessTaskId)) {
      PGLog.d('跳过重复下载任务: $businessTaskId');
      return;
    }

    downloadTasks.add(DownloadTask(
      taskId: businessTaskId, // 使用业务TaskID
      url: url,
      filePath: filePath,
    ));
    PGLog.d('添加下载任务: $businessTaskId -> $filePath');
  }

  /// 添加调色任务（如果需要调色且任务不存在）
  void _addColorGradingTaskIfNeeded(
    List<ColorGradingTask> colorGradingTasks,
    String businessTaskId,
    String filePath,
  ) {
    // 检查是否需要调色
    /*
    final lutFilePath = ColorGradingManager.checkColorGradingNeeded(filePath);
    if (lutFilePath == null) {
      return; // 不需要调色
    }

    // 简化检查：如果管理器中已有任务，直接跳过
    if (_colorGradingManager.hasTask(businessTaskId)) {
      PGLog.d('跳过重复调色任务: $businessTaskId');
      return;
    }

    // 直接创建调色任务
    colorGradingTasks.add(ColorGradingTask.autoLut(
      taskId: businessTaskId,
      inputFilePath: filePath,
    ));
    PGLog.d('添加调色任务: $businessTaskId -> $filePath');
    */
  }

  /// 批量启动下载任务
  void _startDownloadTasks(List<DownloadTask> downloadTasks) {
    // 使用新的基于DownloadTask的API
    _downloadManager.downloadConcurrent(
      downloadTasks,
      maxConcurrent: 5, // 直接指定此次下载的并发数
      onProgress: (taskId, progress, speed, remainingTime) {
        // 打印当前时间，格式为yyyy-MM-dd HH:mm:ss
        final now = DateTime.now();
        final formattedTime =
            '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}';
        PGLog.d(
            '[$formattedTime] 导出高清大图下载进度 $taskId: ${(progress * 100).toStringAsFixed(1)}%');
      },
      onComplete: (taskId, file) async {
        PGLog.d('导出高清大图下载完成: $taskId -> ${file.path}');
        // 下载完成后上报下载状态
        await _reportDownloadStatus(taskId);
        // 下载完成后检查是否需要调色（直接使用同一个TaskID）
        _checkAndAddColorGradingTask(taskId, file.path);
        // 更新UI模型
        await _generateUIModels();
        notifyListeners();
      },
      onError: (taskId, error) {
        PGLog.e('下载失败: $taskId -> $error');
      },
    );
  }

  /// 批量启动调色任务
  void _startColorGradingTasks(List<ColorGradingTask> colorGradingTasks) {
    // 直接使用Task列表，无需转换
    _colorGradingManager.processMultipleColorGradingFromTasks(
      colorGradingTasks,
      onComplete: (taskId, result) async {
        PGLog.d('调色完成: $taskId -> ${result.isSuccess}');
        // 刷新UI
        await _generateUIModels();
        notifyListeners();
      },
      onError: (taskId, error) {
        PGLog.e('调色失败: $taskId -> $error');
      },
    ).then((taskIds) {
      PGLog.d('启动批量调色，任务数: ${taskIds.length}');
    });
  }

  /// 上报下载完成状态
  Future<void> _reportDownloadStatus(String taskId) async {
    try {
      final taskIdParts = parseTaskId(taskId);
      if (taskIdParts.length == 2) {
        final proofingId = taskIdParts[0];
        final effectCode = taskIdParts[1];

        final request = AigcSampleListExportRequest(
          proofingId: proofingId,
          effectCode: effectCode,
        );

        await _repository.updateAigcSampleDownloadStatus([request]);
        PGLog.d('上报下载完成状态成功: $taskId');
      } else {
        PGLog.e('解析taskId失败，无法上报下载状态: $taskId');
      }
    } catch (e) {
      PGLog.e('上报下载完成状态失败: $taskId, 错误: $e');
    }
  }

  /// 检查下载完成的文件是否需要调色（使用同一个TaskID）
  void _checkAndAddColorGradingTask(String taskId, String filePath) {
    /*
    try {
      // 检查是否需要调色且任务不存在
      final lutFilePath = ColorGradingManager.checkColorGradingNeeded(filePath);
      if (lutFilePath != null && !_colorGradingManager.hasTask(taskId)) {
        PGLog.d('检查下载完成的文件需要调色: $taskId -> $filePath');
        // 直接创建调色任务
        final task = ColorGradingTask.autoLut(
          taskId: taskId,
          inputFilePath: filePath,
        );

        _colorGradingManager.processColorGradingFromTask(
          task,
          onComplete: (taskId, result) async {
            PGLog.d('调色完成: $taskId -> ${result.isSuccess}');
            // 刷新UI
            await _generateUIModels();
            notifyListeners();
          },
          onError: (taskId, error) {
            PGLog.e('调色失败: $taskId -> $error');
          },
        ).ignore(); // 明确表示不关心返回值
      }
    } catch (e) {
      PGLog.e('检查下载文件调色需求失败: $e');
    }*/
  }

  /// 内部获取目标文件路径用于下载和调色,找不到都默认备用桌面(public,导出列表可调用-不传效果,打样详情可调用-包含效果)
  Future<String> _getProofingExportFilePathInternal(
      ExportPathInfo exportPathInfo) async {
    String basePath = await _checkBasePathIsExists();
    if (basePath.isEmpty) {
      PGLog.w('导出基础路径不存在，使用桌面路径作为导出基础路径');
      basePath = await _filePathService.getDesktopPath();
    }
    return await _appSubPath(
        basePath, exportPathInfo.projectFolderName, exportPathInfo);
  }

  // 外部获取目标文件,返回结果包含导出路径类型，导出路径，预期导出路径是否存在
  /// [projectId] 项目ID，导出列表使用
  /// [exportPathInfo] 导出路径信息，打样详情，别人的打样项目，需要下载，传导出路径信息，因为没有导出记录
  Future<CheckPathResult> getProofingExportFilePath(
      String projectId, ExportPathInfo? exportPathInfo) async {
    final pathType = UserPreferencesService.getExportPathType();
    CheckPathResult result = CheckPathResult(pathType: pathType);
    String basePath = await _checkBasePathIsExists();
    if (basePath.isEmpty) {
      PGLog.w('预期导出基础路径不存在，使用桌面路径作为导出基础路径');
      basePath = await _filePathService.getDesktopPath();
      result.isExpectedPathExists = false;
    }
    String projectFolderName = exportPathInfo?.projectFolderName ?? '';
    if (projectFolderName.isEmpty) {
      projectFolderName = getMyExportListProjectFolderName(projectId);
      if (projectFolderName.isEmpty) {
        PGLog.w('异常：未找到导出项目，无法获取导出路径: $projectId');
        return result;
      }
    }
    result.resultPath =
        await _appSubPath(basePath, projectFolderName, exportPathInfo);
    return result;
  }

  /// 导出列表获取项目文件夹名
  /// [projectId] 项目ID
  String getMyExportListProjectFolderName(String projectId) {
    final exportProjectModel = _findExportProjectModel(projectId);
    if (exportProjectModel == null) {
      return '';
    }
    return ExportPathInfo.getProjectFolderName(
        exportProjectModel.projectName, exportProjectModel.projectId);
  }

  /// 拼接导出路径
  /// [basePath] 基础路径
  /// [projectFolderName] 项目文件夹名
  /// [exportPathInfo] 导出路径信息
  Future<String> _appSubPath(String basePath, String projectFolderName,
      ExportPathInfo? exportPathInfo) async {
    // 创建完整的文件夹路径：基础路径 + firstFolderName + projectFolderName
    final fullDirPath =
        path.join(basePath, ExportPathInfo.firstFolderName, projectFolderName);
    // 不存在则创建文件夹，以免跳转查看路径失败
    final fullDirectory = Directory(fullDirPath);
    if (!fullDirectory.existsSync()) {
      await fullDirectory.create(recursive: true);
    }
    if (exportPathInfo != null) {
      return path.join(fullDirPath, exportPathInfo.fileName);
    }
    return fullDirPath;
  }

  /// 检查导出基础路径是否存在
  Future<String> _checkBasePathIsExists() async {
    // 目前需求只有指定路径，没有原图路径
    final basePath = UserPreferencesService.getCustomExportPath();
    try {
      // 如果路径不存在，则使用桌面路径
      if (Directory(basePath).existsSync()) {
        return basePath;
      }
      return '';
    } catch (e) {
      PGLog.e('检查导出基础路径是否存在失败: $e');
      return '';
    }
  }

  /// 生成UI模型
  Future<void> _generateUIModels() async {
    if (_isGeneratingUIModels) {
      _needsRegenerateUIModels = true;
      PGLog.d('UI模型生成中，标记需要重新生成 - needsRegenerateUIModels: true');
      return;
    }

    // 开始生成循环，直到不再需要重新生成，以免丢失数据；
    // 对于您当前的场景，我推荐继续使用当前的方案（防抖节流机制对比），因为：
    // 1.数据可靠性至关重要 - UI模型的准确性直接影响用户体验
    // 2.调用频率不算太高 - 主要是下载/调色完成时触发
    // 3.日志完整 - 便于调试和监控
    do {
      _needsRegenerateUIModels = false;
      _isGeneratingUIModels = true;

      final startTime = DateTime.now().millisecondsSinceEpoch;
      final methodId = 'generateUI_$startTime';

      try {
        PGLog.d('[$methodId] 开始 - 清空UI列表前: ${_uiModelList.length}');
        _uiModelList.clear();
        PGLog.d(
            '[$methodId] 清空UI列表后: ${_uiModelList.length}, 导出列表数量: ${_exportProjectList.length}');

        for (int index = 0; index < _exportProjectList.length; index++) {
          final exportProjectModel = _exportProjectList[index];
          PGLog.d(
              '[$methodId] 处理第${index + 1}个导出项目: ${exportProjectModel.projectId}');
          var totalCount = 0;
          var completedCount = 0;
          var hasProcessing = false;
          var hasFailed = false;
          // 从exports中找到最新的updateAt作为显示时间
          var latestUpdateTime = DateTime.fromMillisecondsSinceEpoch(0);

          // 遍历每个项目下的打样
          for (int proofingIndex = 0;
              proofingIndex < exportProjectModel.proofings.length;
              proofingIndex++) {
            final proofingModel = exportProjectModel.proofings[proofingIndex];
            totalCount += proofingModel.exports.length;

            for (int i = 0; i < proofingModel.exports.length; i++) {
              final effect = proofingModel.exports[i];
              final effectCode = effect.effectCode;
              final status = ExportStatus.fromString(effect.status);

              // 检查文件是否已下载
              final exportPathInfo = ExportPathInfo.fromExportModel(
                  exportProjectModel, proofingModel.id, effectCode);

              // 是否上报已下载
              var isDowloadResult = effect.isDownload;
              if (!isDowloadResult) {
                // 如果没有上报了已下载，则需要检查本地是否真实下载
                final targetPath =
                    await _getProofingExportFilePathInternal(exportPathInfo);
                isDowloadResult = File(targetPath).existsSync();
              }

              if (status.isCompleted && isDowloadResult) {
                completedCount++;
                PGLog.d('更新UI模型，导出高清大图下载完成: $effectCode');
                // 检查调色文件是否存在

                // final lutEffectCode = '${effectCode}_lut';
                // final lutFilePath =
                //     await getProofingExportFilePath(exportModel.id, lutEffectCode);
                // final isLutDownloaded = File(lutFilePath).existsSync();

                // if (isLutDownloaded) {
                //   completedCount++;
                // } else {
                //   hasProcessing = true; // 已下载但调色文件不存在，显示为处理中
                // }
              } else if (status.isCompleted && !isDowloadResult) {
                hasProcessing = true; // 已完成但未下载的，显示为处理中
              } else if (status != ExportStatus.completed) {
                hasProcessing = true; // 未完成，显示为处理中
                if (status.isFailed) {
                  hasFailed = true; // 失败,在处理中的失败
                }
              }
              // 从exports中找到最新的updateAt作为显示时间
              final effectUpdateTime =
                  DateTime.fromMillisecondsSinceEpoch(effect.updateAt * 1000);
              if (effectUpdateTime.isAfter(latestUpdateTime)) {
                latestUpdateTime = effectUpdateTime;
              }
            }
          }
          // 确定整体进度状态
          AigcMySampleExportProgressStatus progressStatus;
          if (hasProcessing) {
            progressStatus = AigcMySampleExportProgressStatus.processing;
            // 如果处理中包含失败，则显示为失败
            if (hasFailed) {
              progressStatus = AigcMySampleExportProgressStatus.failed;
            }
          } else {
            progressStatus = AigcMySampleExportProgressStatus.completed;
          }

          _uiModelList.add(AigcMySampleExportUIModel(
            id: exportProjectModel.projectId,
            name: exportProjectModel.projectName,
            exportNum: '$completedCount/$totalCount',
            progressStatus: progressStatus,
            updateTime: latestUpdateTime,
          ));
          PGLog.d(
              '[$methodId] 添加UI模型后当前数量: ${_uiModelList.length}, 项目ID: ${exportProjectModel.projectId}');
        }
        PGLog.d('[$methodId] 完成 - 最终UI模型数量: ${_uiModelList.length}');
      } finally {
        _isGeneratingUIModels = false;
      }

      // 检查是否需要重新生成
      if (_needsRegenerateUIModels) {
        PGLog.d('检测到需要重新生成UI模型，开始下一轮生成');
      }
    } while (_needsRegenerateUIModels);

    PGLog.d('UI模型生成全部完成，无需再次生成');
  }

  /// 检查导出状态是否包含完成状态，包含完成才调用refreshExportList
  Future<void> _checkExportStatusAndRefresh() async {
    // 1.获取当前轮询的请求列表，获取最新状态(传null获取我的打样列表)
    final statusList = _repository.getLocalAigcSampleExportStatusList(null);

    // 2.检查状态是否真正发生变化
    bool statusChanged = _hasStatusChanged(statusList);

    PGLog.d(
        '轮询列表数量发生变化: ${_pollingRequests.length} != ${statusList.length}， 或者状态发生变化 statusChanged: $statusChanged');
    if (statusChanged) {
      PGLog.d('检测到状态变化或数量变化，刷新导出列表');
      // 停止轮询
      _pollingProvider.stopPolling();
      // 刷新导出列表
      refreshExportList();
    } else {
      PGLog.d('轮询列表没有状态变化，不刷新导出列表');
    }
  }

  /// 检查状态是否真正发生变化
  bool _hasStatusChanged(List<AigcSampleExportStatusModel> currentStatusList) {
    // 如果数量不同，说明有变化
    if (_lastStatusMap.length != currentStatusList.length) {
      return true;
    }

    // 检查每个状态是否发生变化
    for (final status in currentStatusList) {
      final lastStatus = _lastStatusMap[status.effectCode];

      // 如果是新增的状态或状态发生变化
      final currentStatus = ExportStatus.fromString(status.status);
      if (lastStatus == null || lastStatus != currentStatus) {
        PGLog.d(
            '检测到状态变化: ${status.effectCode}, 原状态: $lastStatus, 新状态: ${status.status}');
        return true;
      }
    }

    return false;
  }

  /// 外部检查指定项目是否有需要下载的效果文件
  /// [originPhotoUrl] 原图URL，别人的打样项目，需要下载，传原图URL，因为没有导出记录
  /// [downloadPath] 下载路径，别人的打样项目，需要下载，传下载路径，因为没有导出记录
  /// [exportPathInfo] 导出路径信息，别人的打样项目，需要下载，传导出路径信息，因为没有导出记录
  /// 返回needDownload表示有需要下载的文件，isOtherAuthor表示是否是别人的打样项目
  Future<CheckDownloadQueueResult> hasDownloadablePendingFiles(
      String projectId,
      String? originPhotoUrl,
      String? downloadPath,
      ExportPathInfo? exportPathInfo) async {
    try {
      if (exportPathInfo != null) {
        final effectModelIsInList = _findExportEffectModel(projectId,
                exportPathInfo.proofingId, exportPathInfo.effectCode) !=
            null;

        // 在导出列表没有找到此效果，就是别人的需要下载的
        bool isInTaskQueue = _isInTaskQueue(
            buildTaskId(exportPathInfo.proofingId, exportPathInfo.effectCode));

        final isDownloaded = await checkFileExists(exportPathInfo);
        PGLog.d(
            '未找到导出模型，可能是别人的打样项目，需要下载，打样项目ID: ${exportPathInfo.proofingId}, 效果码: ${exportPathInfo.effectCode}, 是否已下载: $isDownloaded, 是否在下载队列中: $isInTaskQueue');
        // 需要下载的文件信息
        List<DownloadTask>? needDownLoadTasks;
        if (!isDownloaded &&
            originPhotoUrl != null &&
            originPhotoUrl.isNotEmpty &&
            downloadPath != null &&
            downloadPath.isNotEmpty) {
          needDownLoadTasks = [
            DownloadTask(
                taskId: buildTaskId(
                    exportPathInfo.proofingId, exportPathInfo.effectCode),
                url: originPhotoUrl,
                filePath: downloadPath)
          ];
        }
        return CheckDownloadQueueResult(
            needDownload: !isDownloaded,
            isInTaskQueue: isInTaskQueue,
            isOtherAuthor: !effectModelIsInList,
            needDownLoadTasks: needDownLoadTasks);
      } else {
        PGLog.d('检查项目列表的当前项目所有文件是否已下载');
        final exportProjectModel = _findExportProjectModel(projectId);
        if (exportProjectModel == null) {
          PGLog.d('异常未找到导出模型，需要传递具体的打样id和效果码');
          return CheckDownloadQueueResult(
              needDownload: false, isInTaskQueue: false, isOtherAuthor: true);
        }
        // 在导出列表有此效果，检查文件是否已下载
        List<DownloadTask>? needDownLoadTasks = [];
        // 先找到所有未下载的效果
        for (final proofingModel in exportProjectModel.proofings) {
          for (final effect in proofingModel.exports) {
            final status = ExportStatus.fromString(effect.status);
            // 检查是否是已完成状态且有下载URL
            if (status.isCompleted &&
                effect.exportPhotoUrl != null &&
                effect.exportPhotoUrl!.isNotEmpty) {
              // 检查文件是否已下载
              final exportPathInfo = ExportPathInfo.fromExportModel(
                  exportProjectModel, proofingModel.id, effect.effectCode);
              // 点击查看的时候，不用管是否上报，只有管是否真实下载
              final isDownloaded = await checkFileExists(exportPathInfo);
              if (!isDownloaded) {
                final exportPathInfo = ExportPathInfo.fromExportModel(
                    exportProjectModel, proofingModel.id, effect.effectCode);
                final targetPath =
                    await _getProofingExportFilePathInternal(exportPathInfo);
                needDownLoadTasks.add(DownloadTask(
                    taskId: buildTaskId(proofingModel.id, effect.effectCode),
                    url: effect.exportPhotoUrl!,
                    filePath: targetPath));
                PGLog.d('发现需要下载的文件: ${effect.effectCode}');
              }
            }
          }
        }

        // 如果有未下载的效果，检查队列状态
        if (needDownLoadTasks.isNotEmpty) {
          bool isInTaskQueue = true;

          for (final needDownLoadTask in needDownLoadTasks) {
            final businessTaskId = needDownLoadTask.taskId;
            if (businessTaskId != null && !_isInTaskQueue(businessTaskId)) {
              PGLog.d('只要有一个任务不在下载队列中就不在需要弹窗下载: $businessTaskId');
              isInTaskQueue = false;
              break;
            }
          }
          PGLog.d('检查队列状态，是否都在下载队列中: $isInTaskQueue');
          // 根据统计结果返回
          return CheckDownloadQueueResult(
              needDownload: true,
              isInTaskQueue: isInTaskQueue,
              isOtherAuthor: false,
              needDownLoadTasks: needDownLoadTasks);
        }

        PGLog.d('没有需要下载的文件: $projectId');
        return CheckDownloadQueueResult(
            needDownload: false,
            isInTaskQueue: false,
            isOtherAuthor: false,
            needDownLoadTasks: null);
      }
    } catch (e) {
      PGLog.e('检查需要下载的文件失败: $e');
      return CheckDownloadQueueResult(
          needDownload: false,
          isInTaskQueue: false,
          isOtherAuthor: false,
          needDownLoadTasks: null);
    }
  }

  Future<bool> checkFileExists(ExportPathInfo exportPathInfo) async {
    final targetPath = await _getProofingExportFilePathInternal(exportPathInfo);
    final file = File(targetPath);

    return file.existsSync();
  }

  /// 将指定打样项目的待下载文件添加到下载队列
  /// 外部点击下载（已完成，但是未下载）
  Future<void> addToDownloadQueue(List<DownloadTask> needDownLoadTasks) async {
    try {
      // 启动下载任务
      if (needDownLoadTasks.isNotEmpty) {
        _startDownloadTasks(needDownLoadTasks);
        PGDialog.showToast('已添加${needDownLoadTasks.length}个文件到下载队列');
        PGLog.d('成功添加${needDownLoadTasks.length}个文件到下载队列');
      } else {
        PGLog.d('没有需要下载的文件');
      }
    } catch (e) {
      PGLog.e('添加到下载队列失败: $e');
      PGDialog.showToast('添加到下载队列失败');
    }
  }

  /// 检查任务是否在下载队列中
  bool _isInTaskQueue(String taskId) {
    return _downloadManager.allTasks.containsKey(taskId);
  }

  /// 更新轮询请求列表
  void _updatePollingRequests() {
    _pollingRequests.clear();

    for (final exportProjectModel in _exportProjectList) {
      for (final proofingModel in exportProjectModel.proofings) {
        // 检查项目ID是否在有效的AIGC项目列表中，自己删除了项目，但是导出记录还在，暂时还是轮询，以免一直在未完成的状态
        for (int i = 0; i < proofingModel.exports.length; i++) {
          final effect = proofingModel.exports[i];
          final effectCode = effect.effectCode;
          final status = ExportStatus.fromString(effect.status);
          // 只轮询未完成的项目
          if (!status.isCompleted) {
            _lastStatusMap[effectCode] = status;
            _pollingRequests.add(AigcSampleListExportRequest(
              proofingId: proofingModel.id,
              effectCode: effectCode,
            ));
          }
        }
      }
    }

    // 重新开始轮询
    if (_pollingRequests.isNotEmpty) {
      _pollingProvider.startPolling(_pollingRequests);
      PGLog.d('更新轮询列表，当前轮询${_pollingRequests.length}个项目');
    } else {
      _pollingProvider.stopPolling();
      PGLog.d('所有项目已完成，停止轮询');
    }
  }

  /// 停止所有操作
  void stop() {
    _pollingProvider.stopPolling();
    _downloadManager.stop();
    _colorGradingManager.stop();
  }

  @override
  void dispose() {
    stop();
    _networkProvider.removeListener(_handleNetworkChange);
    _retryRefreshController.close();
    _exportStatusChangeSubscription?.cancel();
    _currentUserInfoChangeSubscription?.cancel();

    super.dispose();
  }

  /// 处理网络状态变更事件
  void _handleNetworkChange() async {
    try {
      final currentConnected = await _networkProvider.isConnected();
      PGLog.d('网络状态变更: 之前=$_lastNetworkConnected, 现在=$currentConnected');

      // 检查是否从无网变成有网
      if (!_lastNetworkConnected && currentConnected) {
        PGLog.d('检测到网络从无网变成有网，重新初始化');
        // 从无网变成有网，重新调用initialize
        refreshExportList();
      }

      // 更新网络状态
      _lastNetworkConnected = currentConnected;
    } catch (e) {
      PGLog.e('处理网络状态变更失败: $e');
    }
  }

  /// 初始化网络状态
  Future<void> _initializeNetworkStatus() async {
    try {
      _lastNetworkConnected = await _networkProvider.isConnected();
      PGLog.d('初始化网络状态: $_lastNetworkConnected');
    } catch (e) {
      PGLog.e('初始化网络状态失败: $e');
      _lastNetworkConnected = false;
    }
  }
}
