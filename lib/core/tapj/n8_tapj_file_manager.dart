import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/utils/pg_log.dart';

import 'n8_tapj_models.dart';

/// N8 TAPJ(Turing_Art_Project)二进制文件管理器
/// 负责读取.tapj格式的二进制工程文件
class N8TapjFileManager {
  /// TAPJ文件魔数标识
  static const List<int> _tapjMagic = [84, 65, 80, 74]; // "TAPJ" in ASCII

  /// TAPJ文件魔数字符串
  static const String tapjMagicString = 'TAPJ';

  /// 文件格式版本
  static const int _fileVersion = 1;

  /// 支持的TAPJ文件扩展名
  static const List<String> supportedTapjExtensions = [
    'tapj',
  ];

  /// 描述文件后缀
  static const String _descriptionFileSuffix = '_description.json';

  /// 读取N8 TAPJ工程文件
  ///
  /// [tapjFilePath] TAPJ文件路径
  /// 返回工程数据和历史记录文件字典，以及可能的描述文件信息
  static Future<N8TapjReadResult> readN8TapjFile(String tapjFilePath) async {
    final file = File(tapjFilePath);
    if (!await file.exists()) {
      throw FileSystemException('TAPJ文件不存在', tapjFilePath);
    }

    final bytes = await file.readAsBytes();
    final tapjResult = _readTapjFile(bytes);

    // 尝试读取描述文件
    final descriptionFiles = await _readDescriptionFile(tapjFilePath);

    return N8TapjReadResult(
      projectData: tapjResult.projectData,
      historyFiles: tapjResult.historyFiles,
      descriptionFiles: descriptionFiles,
    );
  }

  /// 构建描述文件路径
  ///
  /// [tapjFilePath] tapj文件路径
  /// 返回对应的描述文件路径
  static String _getDescriptionFilePath(String tapjFilePath) {
    final dir = path.dirname(tapjFilePath);
    final fileName = path.basenameWithoutExtension(tapjFilePath);
    return path.join(dir, '$fileName$_descriptionFileSuffix');
  }

  /// 读取描述文件
  ///
  /// [tapjFilePath] tapj文件路径
  /// 返回描述文件信息列表，如果文件不存在或读取失败则返回null
  static Future<List<N8SelectedFileInfo>?> _readDescriptionFile(
      String tapjFilePath) async {
    try {
      final descriptionFilePath = _getDescriptionFilePath(tapjFilePath);
      final descriptionFile = File(descriptionFilePath);

      if (!await descriptionFile.exists()) {
        PGLog.d('描述文件不存在: $descriptionFilePath');
        return null;
      }

      PGLog.d('读取描述文件: $descriptionFilePath');
      final jsonContent = await descriptionFile.readAsString();
      final List<dynamic> jsonList = jsonDecode(jsonContent);

      final descriptionFiles = jsonList
          .map((item) =>
              N8SelectedFileInfo.fromJson(item as Map<String, dynamic>))
          .toList();

      PGLog.d('成功读取描述文件，包含 ${descriptionFiles.length} 个文件信息');
      return descriptionFiles;
    } catch (e) {
      PGLog.e('读取描述文件失败: $e');
      return null;
    }
  }

  /// 提取TAPJ文件到指定目录
  ///
  /// [tapjFilePath] TAPJ文件路径
  /// [extractFolder] 提取目录
  /// 返回是否提取成功
  static Future<bool> extractTapjFile(
      String tapjFilePath, String extractFolder) async {
    try {
      PGLog.d('【N8导出】开始提取TAPJ文件: $tapjFilePath 到 $extractFolder');

      final result = await readN8TapjFile(tapjFilePath);

      // 创建提取目录
      final extractDir = Directory(extractFolder);
      if (!await extractDir.exists()) {
        await extractDir.create(recursive: true);
      }

      // 保存project.json
      final projectJson = jsonEncode(result.projectData.toJson());
      final projectFilePath = path.join(extractFolder, 'project.json');
      await File(projectFilePath).writeAsString(projectJson);

      // 保存历史记录文件
      for (final entry in result.historyFiles.entries) {
        final historyFilePath = path.join(extractFolder, entry.key);
        await File(historyFilePath).writeAsBytes(entry.value);
      }

      PGLog.d('【N8导出】TAPJ文件提取成功: $extractFolder');
      return true;
    } catch (e) {
      PGLog.d('【N8导出】提取TAPJ文件失败: $e');
      return false;
    }
  }

  /// 读取TAPJ二进制文件
  static N8TapjReadResult _readTapjFile(Uint8List bytes) {
    var offset = 0;

    // 验证文件头 - 读取魔数
    if (bytes.length < 4) {
      throw const FormatException('文件太小，不是有效的TAPJ文件');
    }

    final magic = bytes.sublist(offset, offset + 4);
    if (!_listEquals(magic, _tapjMagic)) {
      throw const FormatException('不是有效的TAPJ文件');
    }
    offset += 4;

    // 读取版本号
    if (bytes.length < offset + 4) {
      throw const FormatException('文件格式错误：版本号读取失败');
    }

    final version = _readInt32(bytes, offset);
    offset += 4;

    if (version != _fileVersion) {
      throw FormatException('不支持的TAPJ文件版本: $version');
    }

    // 读取工程JSON数据
    if (bytes.length < offset + 4) {
      throw const FormatException('文件格式错误：JSON长度读取失败');
    }

    final jsonLength = _readInt32(bytes, offset);
    offset += 4;

    if (bytes.length < offset + jsonLength) {
      throw const FormatException('文件格式错误：JSON数据读取失败');
    }

    final jsonBytes = bytes.sublist(offset, offset + jsonLength);
    offset += jsonLength;

    final projectJson = utf8.decode(jsonBytes);
    PGLog.d('projectJson: $projectJson');
    final projectData = N8ExportProjectData.fromJson(jsonDecode(projectJson));

    // 读取历史记录文件
    if (bytes.length < offset + 4) {
      throw const FormatException('文件格式错误：历史文件数量读取失败');
    }

    final fileCount = _readInt32(bytes, offset);
    offset += 4;

    final historyFiles = <String, List<int>>{};

    for (int i = 0; i < fileCount; i++) {
      // 读取文件名
      if (bytes.length < offset + 4) {
        throw const FormatException('文件格式错误：历史文件名长度读取失败');
      }

      final fileNameLength = _readInt32(bytes, offset);
      offset += 4;

      if (bytes.length < offset + fileNameLength) {
        throw const FormatException('文件格式错误：历史文件名读取失败');
      }

      final fileNameBytes = bytes.sublist(offset, offset + fileNameLength);
      offset += fileNameLength;
      final fileName = utf8.decode(fileNameBytes);

      // 读取文件数据
      if (bytes.length < offset + 4) {
        throw const FormatException('文件格式错误：历史文件数据长度读取失败');
      }

      final fileDataLength = _readInt32(bytes, offset);
      offset += 4;

      if (bytes.length < offset + fileDataLength) {
        throw const FormatException('文件格式错误：历史文件数据读取失败');
      }

      final fileData = bytes.sublist(offset, offset + fileDataLength);
      offset += fileDataLength;

      historyFiles[fileName] = fileData;
    }

    return N8TapjReadResult(
      projectData: projectData,
      historyFiles: historyFiles,
    );
  }

  /// 从字节数组中读取32位整数（小端序）
  static int _readInt32(Uint8List bytes, int offset) {
    if (bytes.length < offset + 4) {
      throw const FormatException('字节数组太短，无法读取32位整数');
    }

    return bytes[offset] |
        (bytes[offset + 1] << 8) |
        (bytes[offset + 2] << 16) |
        (bytes[offset + 3] << 24);
  }

  /// 比较两个列表是否相等
  static bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  /// 创建文件列表JSON（兼容原有格式）
  ///
  /// [outputFolder] 输出目录
  /// [workspaceName] 工程名
  /// [projectData] 工程数据
  static Future<void> createSelectedFilesJson(
    String outputFolder,
    String workspaceName,
    N8ExportProjectData projectData,
  ) async {
    try {
      // 创建文件列表数据
      final fileListData = projectData.fileList
          .map((file) => N8SelectedFileInfo(
                originalPath: file.originalPath,
                exportPath: file.exportPath,
                isSelected: file.isSelected,
              ))
          .toList();

      // 生成唯一的JSON文件路径
      final jsonFilePath = _generateUniqueFileName(
        outputFolder,
        workspaceName,
        '_description.json',
      );

      // 序列化并保存JSON
      final jsonContent =
          jsonEncode(fileListData.map((e) => e.toJson()).toList());
      await File(jsonFilePath).writeAsString(jsonContent);

      PGLog.d('【N8导出】文件列表JSON创建成功: $jsonFilePath');
    } catch (e) {
      PGLog.d('【N8导出】创建文件列表JSON失败: $e');
    }
  }

  /// 生成唯一的文件路径，避免重名
  ///
  /// [outputFolder] 输出目录
  /// [baseName] 基础文件名（不含扩展名）
  /// [suffix] 文件后缀（含扩展名）
  /// 返回唯一的完整文件路径
  static String _generateUniqueFileName(
    String outputFolder,
    String baseName,
    String suffix,
  ) {
    // 清理基础名称中的非法字符
    String cleanName = baseName;
    const invalidChars = r'<>:"/\|?*';
    for (int i = 0; i < invalidChars.length; i++) {
      cleanName = cleanName.replaceAll(invalidChars[i], '_');
    }

    String fullFileName = '$cleanName$suffix';
    String fullPath = path.join(outputFolder, fullFileName);

    // 如果文件不存在，直接返回完整路径
    if (!File(fullPath).existsSync()) {
      return fullPath;
    }

    // 如果文件存在，尝试添加数字后缀
    int counter = 1;
    while (true) {
      String numberedBaseName = '${cleanName}_$counter';
      String numberedFullFileName = '$numberedBaseName$suffix';
      String numberedFullPath = path.join(outputFolder, numberedFullFileName);

      if (!File(numberedFullPath).existsSync()) {
        return numberedFullPath;
      }

      counter++;
    }
  }
}
