import 'dart:async';

import 'package:flutter/material.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'command_line_args/command_line_message_manager.dart';
import 'external_message_listener.dart';
import 'handler/external_message_base_handler.dart';
import 'handler/external_message_dispatcher.dart';
import 'handler/external_message_result.dart';

/// 统一的外部消息管理器
/// 管理外部消息的监听、分发和队列处理
class ExternalMessageManager {
  static final ExternalMessageManager _instance =
      ExternalMessageManager._internal();
  factory ExternalMessageManager() => _instance;
  ExternalMessageManager._internal();

  ExternalMessageDispatcher? _dispatcher;
  ExternalMessageListener? _listener;
  CommandLineMessageManager? _commandLineManager;

  // 消息队列
  final List<ExternalMessage> _pendingMessages = [];
  bool _isProcessingPendingMessages = false;

  // 消息处理结果回调
  Function(ExternalMessageResult)? _onMessageResult;

  // 通知回调 - 用于通知UI有新消息等
  VoidCallback? _onMessageReceived;

  bool _isInitialized = false;

  /// 初始化消息管理器
  void initialize({
    Function(ExternalMessageResult)? onMessageResult,
    VoidCallback? onMessageReceived,
  }) {
    if (_isInitialized) {
      PGLog.w('ExternalMessageManager already initialized');
      return;
    }

    _onMessageResult = onMessageResult;
    _onMessageReceived = onMessageReceived;

    // 初始化分发器
    _dispatcher = ExternalMessageDispatcher();

    // 初始化监听器
    _listener = ExternalMessageListener();
    _listener!.initialize(onMessage: _handleExternalMessage);

    // 初始化命令行消息管理器
    _commandLineManager = CommandLineMessageManager();

    _isInitialized = true;
    PGLog.i('ExternalMessageManager initialized successfully');
  }

  /// 注册消息处理器
  void registerHandler(ExternalMessageHandler handler) {
    if (!_isInitialized) {
      throw StateError('ExternalMessageManager not initialized');
    }
    _dispatcher!.registerHandler(handler);
  }

  /// 处理外部消息
  void _handleExternalMessage(String message) async {
    PGLog.i('ExternalMessageManager - 收到外部消息: $message');

    // 解析外部消息
    final externalMessage = ExternalMessage.fromString(message);
    if (externalMessage == null) {
      PGLog.e('无法解析外部消息: $message');
      return;
    }

    // 将消息加入队列等待处理
    _pendingMessages.add(externalMessage);
    PGLog.d('外部消息已加入处理队列，当前队列长度: ${_pendingMessages.length}');

    // 通知有新消息需要处理
    _onMessageReceived?.call();
  }

  /// 检查是否有待处理的外部消息
  bool get hasPendingExternalMessages {
    final hasRegularMessages = _pendingMessages.isNotEmpty;
    final hasCommandLineMessages =
        _commandLineManager?.hasPendingMessage ?? false;
    return hasRegularMessages || hasCommandLineMessages;
  }

  /// 获取当前待处理消息数量
  int get pendingMessageCount {
    final regularCount = _pendingMessages.length;
    final commandLineCount =
        _commandLineManager?.hasPendingMessage == true ? 1 : 0;
    return regularCount + commandLineCount;
  }

  /// 处理所有等待中的外部消息（在有context可用时调用）
  Future<void> processPendingMessages(BuildContext context) async {
    // 如果正在处理中，直接返回
    if (_isProcessingPendingMessages) {
      return;
    }

    if (!_isInitialized) {
      PGLog.e('ExternalMessageManager not initialized');
      return;
    }

    _isProcessingPendingMessages = true;

    try {
      // 首先处理命令行消息
      await _processCommandLineMessages(context);

      // 然后处理常规外部消息
      await _processRegularMessages(context);
    } finally {
      _isProcessingPendingMessages = false;
    }
  }

  /// 处理命令行消息
  Future<void> _processCommandLineMessages(BuildContext context) async {
    if (_commandLineManager == null ||
        !_commandLineManager!.hasPendingMessage) {
      return;
    }

    final commandLineMessage = _commandLineManager!.getPendingMessage();
    if (commandLineMessage != null) {
      PGLog.d('处理来自命令行的消息: ${commandLineMessage.type}');

      try {
        if (context.mounted) {
          final result =
              await _dispatcher!.dispatch(context, commandLineMessage);
          _handleExternalMessageResult(result);
        }
      } catch (e) {
        PGLog.e('处理命令行消息时发生错误: $e');
        _handleExternalMessageResult(ExternalMessageError('处理命令行消息时发生错误: $e'));
      }
    }
  }

  /// 处理常规外部消息
  Future<void> _processRegularMessages(BuildContext context) async {
    if (_pendingMessages.isEmpty) {
      return;
    }

    PGLog.d('开始处理 ${_pendingMessages.length} 条等待中的外部消息');

    final messagesToProcess = List<ExternalMessage>.from(_pendingMessages);
    _pendingMessages.clear();

    for (final message in messagesToProcess) {
      try {
        if (context.mounted) {
          final result = await _dispatcher!.dispatch(context, message);
          _handleExternalMessageResult(result);
        }
      } catch (e) {
        PGLog.e('处理外部消息时发生错误: $e');
        _handleExternalMessageResult(ExternalMessageError('处理外部消息时发生错误: $e'));
      }
    }
  }

  /// 处理外部消息结果
  void _handleExternalMessageResult(ExternalMessageResult result) {
    switch (result) {
      case ExternalMessageSuccess success:
        PGLog.i('外部消息处理成功: ${success.message}');
        break;
      case ExternalMessageError error:
        PGLog.e('外部消息处理失败: ${error.error}');
        break;
      case ExternalMessageProcessing processing:
        PGLog.d('外部消息处理中: ${processing.message}');
        break;
    }

    // 回调给使用者
    _onMessageResult?.call(result);
  }

  /// 清空待处理消息队列
  void clearPendingMessages() {
    _pendingMessages.clear();
    _commandLineManager?.clearPendingMessage();
    PGLog.d('已清空待处理消息队列');
  }

  /// 获取所有注册的处理器
  List<ExternalMessageHandler> get handlers => _dispatcher?.handlers ?? [];

  /// 销毁消息管理器
  void dispose() {
    if (_isInitialized) {
      _listener?.dispose();
      _pendingMessages.clear();
      _commandLineManager?.clearPendingMessage();
      _isProcessingPendingMessages = false;
      _onMessageResult = null;
      _onMessageReceived = null;
      _isInitialized = false;
      PGLog.i('ExternalMessageManager disposed successfully');
    }
  }

  /// 重置管理器状态（用于测试或重新初始化）
  void reset() {
    dispose();
    _isInitialized = false;
  }
}
