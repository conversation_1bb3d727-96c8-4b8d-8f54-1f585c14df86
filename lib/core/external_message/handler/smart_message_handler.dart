import 'dart:io';

import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/core/tapj/n8_tapj_models.dart';
import 'package:turing_art/datalayer/domain/models/external_message/external_message.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/tapj_processor.dart';

import 'external_message_base_handler.dart';
import 'external_message_result.dart';

/// 智能导入消息处理器
/// 简便的图像导入功能，支持以下功能：
/// 1. 检查图像文件夹中是否有tapj文件
/// 2. 如果有tapj文件，按tapj导入模式处理
/// 3. 如果没有tapj文件，创建新项目
/// 4. 对于已存在的项目，将传入的图像文件标记为selected=true
class SmartMessageHandler extends ExternalMessageHandler {
  final Future<ExternalMessageResult> Function(
    BuildContext context,
    ImportProjectData data,
    Map<String, List<int>>? historyFiles,
  ) _onImportProject;

  SmartMessageHandler({
    required Future<ExternalMessageResult> Function(
      BuildContext context,
      ImportProjectData data,
      Map<String, List<int>>? historyFiles,
    ) onImportProject,
  }) : _onImportProject = onImportProject;

  @override
  String get handlerName => 'SmartHandler';

  @override
  ExternalMessageType get supportedType => ExternalMessageType.smart;

  @override
  Future<ExternalMessageResult> handle(
    BuildContext context,
    ExternalMessage message,
  ) async {
    try {
      if (!validateMessage(message)) {
        return const ExternalMessageError('无效的智能导入消息格式');
      }

      var dataStr = message.data.toString();
      PGLog.d('智能导入消息数据: $dataStr');

      final smartData = SmartData.fromMap(message.data);
      if (smartData == null) {
        return const ExternalMessageError('无法解析智能导入数据');
      }

      // 验证图像文件是否存在
      final validImagePaths = <String>[];
      for (final imagePath in smartData.imagePaths) {
        if (await File(imagePath).exists()) {
          validImagePaths.add(imagePath);
        } else {
          PGLog.w('图像文件不存在: $imagePath');
        }
      }

      if (validImagePaths.isEmpty) {
        return const ExternalMessageError('没有找到有效的图像文件');
      }

      // 检查图像文件夹中是否有tapj文件
      final tapjFile = await _findTapjFileInDirectories(validImagePaths);

      if (tapjFile != null) {
        // 找到tapj文件，按tapj导入模式处理
        PGLog.d('找到tapj文件: $tapjFile，将按tapj导入模式处理');
        return await _handleTapjImport(
          context,
          tapjFile,
          validImagePaths,
          smartData,
        );
      } else {
        // 没有找到tapj文件，创建新项目
        PGLog.d('未找到tapj文件，将创建新项目');
        return await _handleNewProjectImport(
          context,
          validImagePaths,
          smartData,
        );
      }
    } catch (e) {
      PGLog.e('处理导入图像消息失败: $e');
      return ExternalMessageError('处理导入图像消息失败: $e');
    }
  }

  /// 在图像文件的目录中查找tapj文件
  Future<String?> _findTapjFileInDirectories(List<String> imagePaths) async {
    final searchedDirectories = <String>{};

    for (final imagePath in imagePaths) {
      final directory = path.dirname(imagePath);

      // 避免重复搜索同一个目录
      if (searchedDirectories.contains(directory)) {
        continue;
      }
      searchedDirectories.add(directory);

      try {
        final dir = Directory(directory);
        if (await dir.exists()) {
          final files = await dir.list().toList();
          for (final file in files) {
            if (file is File &&
                path.extension(file.path).toLowerCase() == '.tapj') {
              PGLog.d('在目录 $directory 中找到tapj文件: ${file.path}');
              return file.path;
            }
          }
        }
      } catch (e) {
        PGLog.w('搜索目录 $directory 时发生错误: $e');
      }
    }

    return null;
  }

  /// 处理tapj导入模式
  Future<ExternalMessageResult> _handleTapjImport(
    BuildContext context,
    String tapjFilePath,
    List<String> selectedImagePaths,
    SmartData smartData,
  ) async {
    try {
      // 读取tapj文件
      final tapjResult = await TapjProcessor.readTapjFile(tapjFilePath);
      if (tapjResult == null) {
        return const ExternalMessageError('无法读取tapj文件');
      }

      // 通过描述文件进行exportPath到originalPath的映射
      final selectedOriginalPaths = _mapSelectedImagePathsToOriginalPaths(
        selectedImagePaths,
        tapjResult.descriptionFiles,
      );

      // 仅构建包含selectedImagePaths中内容的updatedFileList
      final updatedFileList = <FileItem>[];
      for (final fileInfo in tapjResult.projectData.fileList) {
        if (selectedOriginalPaths.contains(fileInfo.originalPath)) {
          final fileItem = FileItem(
            originalPath: fileInfo.originalPath,
            historyId: fileInfo.historyId != 'org' ? fileInfo.historyId : null,
            isSelected: true, // 只包含选中的文件，所以都标记为选中
          );
          updatedFileList.add(fileItem);
          PGLog.d('添加选中的文件到导入列表: ${fileInfo.originalPath}');
        }
      }

      // 创建ImportProjectData
      final importProjectData = ImportProjectData(
        projectId: tapjResult.projectData.rawWorkspaceId,
        projectName: tapjResult.projectData.workspaceName,
        fileList: updatedFileList,
        autoNavigate: smartData.autoNavigate,
        newProject: smartData.newProject,
      );

      // 调用现有的导入项目处理逻辑
      return await _onImportProject(
        context,
        importProjectData,
        tapjResult.historyFiles,
      );
    } catch (e) {
      PGLog.e('处理tapj导入模式失败: $e');
      return ExternalMessageError('处理tapj导入模式失败: $e');
    }
  }

  /// 通过描述文件将选中的图像路径映射到原始路径
  ///
  /// [selectedImagePaths] 选中的图像文件路径列表
  /// [descriptionFiles] 描述文件信息列表
  /// 返回对应的原始文件路径列表
  Set<String> _mapSelectedImagePathsToOriginalPaths(
    List<String> selectedImagePaths,
    List<N8SelectedFileInfo>? descriptionFiles,
  ) {
    final Set<String> selectedOriginalPaths = <String>{};

    if (descriptionFiles == null || descriptionFiles.isEmpty) {
      PGLog.w('描述文件为空，无法进行exportPath到originalPath的映射');
      return selectedOriginalPaths;
    }

    // 创建exportPath到originalPath的映射表
    final Map<String, String> exportToOriginalMap = {};
    for (final desc in descriptionFiles) {
      exportToOriginalMap[desc.exportPath] = desc.originalPath;
    }

    PGLog.d('创建了${exportToOriginalMap.length}个exportPath到originalPath的映射');

    // 将选中的图像路径与exportPath匹配，找到对应的originalPath
    for (final selectedPath in selectedImagePaths) {
      final originalPath = exportToOriginalMap[selectedPath];
      if (originalPath != null) {
        selectedOriginalPaths.add(originalPath);
        PGLog.d('映射成功: $selectedPath -> $originalPath');
      } else {
        PGLog.w('未找到exportPath对应的originalPath: $selectedPath');

        // 尝试路径标准化匹配（处理路径分隔符差异）
        final normalizedSelectedPath = _normalizePath(selectedPath);
        for (final entry in exportToOriginalMap.entries) {
          if (_normalizePath(entry.key) == normalizedSelectedPath) {
            selectedOriginalPaths.add(entry.value);
            PGLog.d('标准化路径映射成功: $selectedPath -> ${entry.value}');
            break;
          }
        }
      }
    }

    PGLog.d(
        '选中图像路径映射完成: ${selectedImagePaths.length}个输入路径, ${selectedOriginalPaths.length}个映射成功');
    return selectedOriginalPaths;
  }

  /// 标准化文件路径（统一路径分隔符）
  String _normalizePath(String filePath) {
    return filePath.replaceAll('\\', '/').toLowerCase();
  }

  /// 处理新项目导入模式
  Future<ExternalMessageResult> _handleNewProjectImport(
    BuildContext context,
    List<String> imagePaths,
    SmartData smartData,
  ) async {
    try {
      // 创建FileItem列表，所有图像文件都标记为选中
      final fileList = imagePaths.map((imagePath) {
        return FileItem(
          originalPath: imagePath,
          historyId: null,
          isSelected: true, // 默认选中所有图像
        );
      }).toList();

      // 生成项目名称
      final projectName =
          smartData.projectName ?? _generateProjectNameFromPaths(imagePaths);

      // 创建ImportProjectData
      final importProjectData = ImportProjectData(
        projectId: null, // 新项目，不指定projectId
        projectName: projectName,
        fileList: fileList,
        autoNavigate: smartData.autoNavigate,
        newProject: true, // 强制创建新项目
      );

      // 调用现有的导入项目处理逻辑
      return await _onImportProject(
        context,
        importProjectData,
        null, // 新项目没有历史文件
      );
    } catch (e) {
      PGLog.e('处理新项目导入模式失败: $e');
      return ExternalMessageError('处理新项目导入模式失败: $e');
    }
  }

  /// 从图像路径生成项目名称
  String _generateProjectNameFromPaths(List<String> imagePaths) {
    if (imagePaths.isEmpty) {
      return '新建项目';
    }

    // 使用第一个图像文件所在的目录名作为项目名称
    final firstImagePath = imagePaths.first;
    final dirName = path.basename(path.dirname(firstImagePath));

    return dirName.isNotEmpty ? dirName : '新建项目';
  }
}
