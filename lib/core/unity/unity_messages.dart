/// 定义Unity消息名称
enum UnityMessage {
  initialized('OnEngineInitialized'), // 引擎初始化
  error('OnEngineError'), // 引擎错误
  completed('OnMethodCompleted'), // 方法完成
  syncWorkspace('OnSyncWorkspace'), // 同步工作区
  workspaceChanged('OnWorkspaceChanged'), // 工作区文件发生改变
  homeButtonClicked('OnHomeButtonClicked'), // 返回主页
  exportButtonClicked('OnExportButtonClicked'), // 导出
  gotPresets('GotPresets'), // 获取到的预设列表
  gotExportResult('GotExportResult'), // 获取到当前用户的导出记录
  importImage('ImportImages'), // 导入图片
  onThumbnailConversion('OnThumbnailConversion'), // 转换缩略图函数执行完毕的回调，不一定成功
  onImportWorkspacesReceived('OnImportWorkspacesReceived'), //创建工程成功的回调
  getExportTokenAsync('getExportToken'), // 获取导出Token
  getDynamicEncryptionParams('getDynamicEncryptionParams'), // 获取动态加密参数
  getEncryptedLutParams('getEncryptedLut'), // 获取加密LUT
  onExportItemCompleted('OnExportItemCompleted'), // 导出一项完成
  onExportItemUpdated('OnExportItemUpdated'), // 导出项更新
  onExportBatchItemsUpdated('OnExportBatchItemsUpdated'), // 导出批量项更新
  onReloadExportItems('OnReloadExportItems'), // 重新加载导出记录
  onExportItemFailed('OnExportItemFailed'), // 导出一项失败
  onExportTaskFailed('OnExportTaskFailed'), // 导出任务失败
  getCacheDiskSpaceAsync('getCacheDiskSpace'), // 获取缓存磁盘空间
  currentDeviceInformation('CurrentDeviceInformation'), // 当前设备信息
  openExportListDialog('OpenExportListDialog'), // 打开导出列表弹窗
  getProjectGuideStepInfo('GetProjectGuideStepInfo'), // 获取新手引导信息
  onProjectGuideStepInfo('OnProjectGuideStepInfo'), // 新手引导步骤完成状态
  onRequestImportImages('OnRequestImportImages'), // 打开图片选择器
  onDeviceInfoDetail('OnDeviceInfoDetail'), // 设备信息详情
  getLocalKey('getLocalKey'), // 获取自定义表配置的本地密钥，用于小样加密
  setN8Select('SetN8Select'), // 设置N8选版结果
  setN8SelectFilterOn('SetN8SelectFilterOn'), // 设置选版过滤器开关
  ;
  // 其他消息...

  final String value;
  const UnityMessage(this.value);

  // 获取所有值的列表
  static List<String> get stringValues =>
      UnityMessage.values.map((e) => e.value).toList();

  // 通过值查找枚举
  static UnityMessage? fromValue(String value) =>
      UnityMessage.values.firstWhere((e) => e.value == value);
}
