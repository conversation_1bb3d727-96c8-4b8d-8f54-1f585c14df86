import 'dart:ffi';

import 'package:ffi/ffi.dart';

// 错误码枚举
class ImageProcessorErrorCode {
  static const int success = 0;
  static const int errorInitFailed = -1;
  static const int errorNotInitialized = -2;
  static const int errorInvalidParam = -3;
  static const int errorFileNotFound = -4;
  static const int errorUnsupportedFormat = -5;
  static const int errorDecodeFailed = -6;
  static const int errorEncodeFailed = -7;
  static const int errorResizeFailed = -8;
  static const int errorUnknown = -99;
}

// 图像处理选项结构体
final class ImageProcessorOptions extends Struct {
  @Int32()
  external int targetWidth;

  @Int32()
  external int targetHeight;

  @Int32()
  external int maintainAspect;

  @Int32()
  external int quality;

  external Pointer<Utf8> outputFormat;

  @Int32()
  external int interpolation;

  @Int32()
  external int correctOrientation;
}

// 图像信息类
class ImageInfo {
  final int width;
  final int height;
  final int channels;

  const ImageInfo({
    required this.width,
    required this.height,
    required this.channels,
  });

  @override
  String toString() => 'ImageInfo(${width}x$height, $channels channels)';
}
