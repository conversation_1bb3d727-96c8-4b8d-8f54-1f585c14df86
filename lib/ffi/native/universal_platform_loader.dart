import 'dart:ffi';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 通用的跨平台动态库加载器
/// 支持Windows(Win7/Win10)、macOS、Linux等平台
class UniversalPlatformLoader {
  /// 加载指定的动态库
  /// [libraryName] 库的基础名称，如 'PGSalientMatters'
  /// [subDirectory] 子目录，如 'salient_matters'
  /// [osSpecificPath] 特定操作系统的路径，如 'win7', 'win10'
  static DynamicLibrary loadLibrary(
    String libraryName, {
    String? subDirectory,
    String? osSpecificPath,
  }) {
    final fileName = _getLibraryFileName(libraryName);
    final platformDir = _getPlatformDirectory(osSpecificPath);

    // 构建搜索路径列表
    final searchPaths = _buildSearchPaths(
      fileName,
      subDirectory: subDirectory,
      platformDir: platformDir,
    );

    // 按优先级尝试加载
    for (final path in searchPaths) {
      try {
        if (path.startsWith('./') || path.startsWith('.\\')) {
          return DynamicLibrary.open(path);
        }

        if (File(path).existsSync()) {
          return DynamicLibrary.open(path);
        }
      } catch (e) {
        PGLog.d('尝试加载库失败: $path - ${e.toString()}');
        continue;
      }
    }

    // 最后尝试系统路径
    try {
      return DynamicLibrary.open(fileName);
    } catch (e) {
      throw StateError(
        '无法找到动态库 $fileName。已尝试的路径：\n${searchPaths.join('\n')}',
      );
    }
  }

  /// 获取动态库文件名
  static String _getLibraryFileName(String libraryName) {
    if (Platform.isWindows) {
      return '${libraryName}Lib.dll';
    } else if (Platform.isLinux) {
      return 'lib$libraryName.so';
    } else if (Platform.isMacOS) {
      return 'lib$libraryName.dylib';
    } else {
      throw UnsupportedError(
        'Unsupported platform: ${Platform.operatingSystem}',
      );
    }
  }

  /// 获取平台目录
  static String _getPlatformDirectory([String? osSpecificPath]) {
    if (Platform.isWindows) {
      if (osSpecificPath != null) {
        return 'windows/$osSpecificPath';
      }
      // 自动检测Windows版本
      return 'windows/${_detectWindowsVersion()}';
    } else if (Platform.isLinux) {
      return 'linux';
    } else if (Platform.isMacOS) {
      return 'macos';
    } else {
      throw UnsupportedError(
        'Unsupported platform: ${Platform.operatingSystem}',
      );
    }
  }

  /// 检测Windows版本
  static String _detectWindowsVersion() {
    try {
      final version = Platform.operatingSystemVersion.toLowerCase();
      // 简单的版本检测逻辑
      if (version.contains('windows 10') || version.contains('windows 11')) {
        return 'win10';
      } else {
        return 'win7';
      }
    } catch (e) {
      PGLog.d('检测Windows版本失败，使用默认win10: $e');
      return 'win10';
    }
  }

  /// 构建搜索路径列表
  static List<String> _buildSearchPaths(
    String fileName, {
    String? subDirectory,
    required String platformDir,
  }) {
    final paths = <String>[];
    // 1. 优先检查可执行文件同目录（CMake复制的版本）
    try {
      final executableDir = File(Platform.resolvedExecutable).parent.path;
      paths.add('$executableDir/$fileName');
    } catch (e) {
      PGLog.d('获取可执行文件目录失败: $e');
    }

    // 2. 然后检查应用包中的平台特定路径
    final basePath =
        subDirectory != null ? 'lib/native/$subDirectory' : 'lib/native';
    paths.add('$basePath/$platformDir/$fileName');

    // 3. 当前工作目录（Windows）
    if (Platform.isWindows) {
      paths.add('./$fileName');
    }

    return paths;
  }

  /// 验证库是否可以成功加载
  static bool canLoadLibrary(
    String libraryName, {
    String? subDirectory,
    String? osSpecificPath,
    String? testFunctionName,
  }) {
    try {
      final lib = loadLibrary(
        libraryName,
        subDirectory: subDirectory,
        osSpecificPath: osSpecificPath,
      );

      // 如果提供了测试函数名，尝试查找该函数
      if (testFunctionName != null) {
        lib.lookup(testFunctionName);
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  /// 获取库信息
  static Map<String, dynamic> getLibraryInfo(
    String libraryName, {
    String? subDirectory,
    String? osSpecificPath,
  }) {
    final fileName = _getLibraryFileName(libraryName);
    final platformDir = _getPlatformDirectory(osSpecificPath);

    return {
      'libraryName': libraryName,
      'fileName': fileName,
      'platform': Platform.operatingSystem,
      'platformDirectory': platformDir,
      'searchPaths': _buildSearchPaths(
        fileName,
        subDirectory: subDirectory,
        platformDir: platformDir,
      ),
      'canLoad': canLoadLibrary(
        libraryName,
        subDirectory: subDirectory,
        osSpecificPath: osSpecificPath,
        testFunctionName: 'SalientMatters_IsInitialized',
      ),
    };
  }

  /// 获取RawDecoder bundle路径
  static String? getRawDecoderBundlePath() {
    final searchPaths = <String>[];

    // 1. 优先检查可执行文件同目录的bundle
    try {
      final executableDir = File(Platform.resolvedExecutable).parent.path;
      searchPaths.add('$executableDir/bundle');
    } catch (e) {
      PGLog.d('获取可执行文件目录失败: $e');
    }

    // 2. 检查应用包中的平台特定路径
    searchPaths.add('lib/native/raw_decoder/$_platformDirectory/bundle');

    // 按优先级尝试查找
    for (final bundlePath in searchPaths) {
      try {
        if (Directory(bundlePath).existsSync()) {
          PGLog.d('找到bundle路径: $bundlePath');
          return bundlePath;
        }
      } catch (e) {
        PGLog.d('检查bundle路径失败: $bundlePath - $e');
      }
    }

    PGLog.d('无法找到bundle，已尝试路径: ${searchPaths.join(', ')}');
    return null;
  }

  /// 获取平台特定的库目录
  static String get _platformDirectory {
    if (Platform.isWindows) {
      return 'windows';
    } else if (Platform.isLinux) {
      return 'linux';
    } else if (Platform.isMacOS) {
      return 'macos';
    } else {
      throw UnsupportedError(
          'Unsupported platform: ${Platform.operatingSystem}');
    }
  }
}
