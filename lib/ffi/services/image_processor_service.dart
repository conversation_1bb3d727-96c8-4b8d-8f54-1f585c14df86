// 图像处理器的Dart封装API
// 提供更友好的Flutter接口

import 'dart:ffi';
import 'dart:io';

import 'package:ffi/ffi.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/ffi/models/image_processor_model.dart';
import 'package:turing_art/ffi/native/image_processor_bindings.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 图像处理器选项
class ImageProcessorConfig {
  final int targetWidth;
  final int targetHeight;
  final bool maintainAspect;
  final int quality;
  final String outputFormat;
  final ImageInterpolation interpolation;
  final bool correctOrientation;

  const ImageProcessorConfig({
    this.targetWidth = 0,
    this.targetHeight = 0,
    this.maintainAspect = true,
    this.quality = 85,
    this.outputFormat = 'jpg',
    this.interpolation = ImageInterpolation.linear,
    this.correctOrientation = true,
  });

  /// 获取默认配置
  static const ImageProcessorConfig defaultConfig = ImageProcessorConfig();

  /// 专门用于缩略图生成的优化配置
  static const ImageProcessorConfig thumbnailConfig = ImageProcessorConfig(
    targetWidth: ImageConstants.aigcThumbnailSize,
    targetHeight: ImageConstants.aigcThumbnailSize,
    maintainAspect: true,
    quality: 75,
    outputFormat: 'jpg',
    interpolation: ImageInterpolation.linear,
    correctOrientation: true,
  );

  /// 高质量处理配置
  static const ImageProcessorConfig highQualityConfig = ImageProcessorConfig(
    targetWidth: 0,
    targetHeight: 0,
    maintainAspect: true,
    quality: 95,
    outputFormat: 'jpg',
    interpolation: ImageInterpolation.cubic,
    correctOrientation: true,
  );

  /// 快速处理配置
  static const ImageProcessorConfig fastConfig = ImageProcessorConfig(
    targetWidth: 0,
    targetHeight: 0,
    maintainAspect: true,
    quality: 60,
    outputFormat: 'jpg',
    interpolation: ImageInterpolation.nearest,
    correctOrientation: true,
  );

  /// 转换为原生结构体
  Pointer<ImageProcessorOptions> toNative() {
    final options = malloc<ImageProcessorOptions>();
    options.ref.targetWidth = targetWidth;
    options.ref.targetHeight = targetHeight;
    options.ref.maintainAspect = maintainAspect ? 1 : 0;
    options.ref.quality = quality;
    options.ref.outputFormat = outputFormat.toNativeUtf8();
    options.ref.interpolation = interpolation.index;
    options.ref.correctOrientation = correctOrientation ? 1 : 0;
    return options;
  }

  /// 释放原生结构体内存
  static void freeNative(Pointer<ImageProcessorOptions> options) {
    if (options != nullptr) {
      if (options.ref.outputFormat != nullptr) {
        malloc.free(options.ref.outputFormat);
      }
      malloc.free(options);
    }
  }

  /// 创建适合特定尺寸限制的配置
  ImageProcessorConfig withMaxDimension(int maxDim) {
    return ImageProcessorConfig(
      targetWidth: maxDim,
      targetHeight: maxDim,
      maintainAspect: maintainAspect,
      quality: quality,
      outputFormat: outputFormat,
      interpolation: interpolation,
      correctOrientation: correctOrientation,
    );
  }

  /// 创建适合特定质量的配置
  ImageProcessorConfig withQuality(int newQuality) {
    return ImageProcessorConfig(
      targetWidth: targetWidth,
      targetHeight: targetHeight,
      maintainAspect: maintainAspect,
      quality: newQuality,
      outputFormat: outputFormat,
      interpolation: interpolation,
      correctOrientation: correctOrientation,
    );
  }

  /// 创建适合特定格式的配置
  ImageProcessorConfig withFormat(String format) {
    return ImageProcessorConfig(
      targetWidth: targetWidth,
      targetHeight: targetHeight,
      maintainAspect: maintainAspect,
      quality: quality,
      outputFormat: format,
      interpolation: interpolation,
      correctOrientation: correctOrientation,
    );
  }

  /// 创建适合特定方向矫正设置的配置
  ImageProcessorConfig withOrientationCorrection(bool correct) {
    return ImageProcessorConfig(
      targetWidth: targetWidth,
      targetHeight: targetHeight,
      maintainAspect: maintainAspect,
      quality: quality,
      outputFormat: outputFormat,
      interpolation: interpolation,
      correctOrientation: correct,
    );
  }
}

/// 图像插值方法枚举
enum ImageInterpolation {
  nearest, // 最近邻插值（最快）
  linear, // 双线性插值（中等质量和速度）
  cubic, // 双三次插值（最高质量，较慢）
}

/// 图像处理器异常
class ImageProcessorException implements Exception {
  final int errorCode;
  final String message;

  const ImageProcessorException(this.errorCode, this.message);

  @override
  String toString() => 'ImageProcessorException($errorCode): $message';

  /// 根据错误码获取错误描述
  static String getErrorMessage(int errorCode) {
    switch (errorCode) {
      case ImageProcessorErrorCode.success:
        return '成功';
      case ImageProcessorErrorCode.errorInitFailed:
        return '初始化失败';
      case ImageProcessorErrorCode.errorNotInitialized:
        return '处理器未初始化';
      case ImageProcessorErrorCode.errorInvalidParam:
        return '参数无效';
      case ImageProcessorErrorCode.errorFileNotFound:
        return '文件未找到';
      case ImageProcessorErrorCode.errorUnsupportedFormat:
        return '不支持的文件格式';
      case ImageProcessorErrorCode.errorDecodeFailed:
        return '解码失败';
      case ImageProcessorErrorCode.errorEncodeFailed:
        return '编码失败';
      case ImageProcessorErrorCode.errorResizeFailed:
        return '缩放失败';
      case ImageProcessorErrorCode.errorUnknown:
        return '未知错误';
      default:
        return '未知错误代码: $errorCode';
    }
  }
}

/// 图像处理器
class ImageProcessorService {
  static bool _initialized = false;

  /// 初始化处理器
  static Future<void> initialize() async {
    if (_initialized) {
      return;
    }

    final result = ImageProcessorBindings.init();
    if (result != ImageProcessorErrorCode.success) {
      throw ImageProcessorException(
        result,
        ImageProcessorException.getErrorMessage(result),
      );
    }

    _initialized = true;
  }

  /// 检查是否已初始化
  static bool get isInitialized => ImageProcessorBindings.isInitialized();

  /// 处理单个图像文件
  ///
  /// [inputFile] 输入图像文件路径
  /// [outputFile] 输出图像文件路径
  /// [config] 处理配置，null表示使用默认配置
  static Future<void> processFile(
    String inputFile,
    String outputFile, {
    ImageProcessorConfig? config,
  }) async {
    if (!isInitialized) {
      throw const ImageProcessorException(
        ImageProcessorErrorCode.errorNotInitialized,
        '处理器未初始化，请先调用initialize()',
      );
    }

    // 检查输入文件是否存在
    if (!File(inputFile).existsSync()) {
      throw ImageProcessorException(
        ImageProcessorErrorCode.errorFileNotFound,
        '输入文件不存在: $inputFile',
      );
    }

    Pointer<ImageProcessorOptions>? options;
    try {
      if (config != null) {
        options = config.toNative();
      }

      final result = ImageProcessorBindings.decode(
        inputFile,
        outputFile,
        options,
        1024, // 错误消息缓冲区大小
      );

      if (result != ImageProcessorErrorCode.success) {
        throw ImageProcessorException(
          result,
          ImageProcessorException.getErrorMessage(result),
        );
      }
    } finally {
      if (options != null) {
        ImageProcessorConfig.freeNative(options);
      }
    }
  }

  /// 智能处理图像文件（自动选择最优配置）
  ///
  /// [inputFile] 输入图像文件路径
  /// [outputFile] 输出图像文件路径
  /// [maxDimension] 最大尺寸限制，0表示不限制
  /// [forThumbnail] 是否用于缩略图生成
  static Future<void> processFileAuto(
    String inputFile,
    String outputFile, {
    int maxDimension = ImageConstants.aigcPreviewSize,
    bool forThumbnail = true,
  }) async {
    // 获取图像信息以便智能选择配置
    final imageInfo = ImageProcessorBindings.getImageInfo(inputFile);

    if (imageInfo == null) {
      throw const ImageProcessorException(
        ImageProcessorErrorCode.errorDecodeFailed,
        '无法获取图像信息',
      );
    }

    // 智能选择配置
    ImageProcessorConfig config;
    final fileSize = File(inputFile).lengthSync();
    final maxImageDim =
        imageInfo.width > imageInfo.height ? imageInfo.width : imageInfo.height;

    if (forThumbnail) {
      // 缩略图模式：优先考虑速度
      if (maxImageDim <= maxDimension) {
        // 图片已经够小，直接拷贝
        await File(inputFile).copy(outputFile);
        return;
      } else if (fileSize > 5 * 1024 * 1024) {
        // 大文件使用极速模式
        config = ImageProcessorConfig.fastConfig.withMaxDimension(maxDimension);
      } else {
        // 标准缩略图配置
        config =
            ImageProcessorConfig.thumbnailConfig.withMaxDimension(maxDimension);
      }
    } else {
      // 非缩略图模式：优先考虑质量
      if (fileSize > 10 * 1024 * 1024) {
        // 超大文件使用平衡配置
        config = ImageProcessorConfig.defaultConfig;
      } else {
        // 高质量配置
        config = ImageProcessorConfig.highQualityConfig;
      }
    }

    await processFile(inputFile, outputFile, config: config);
  }

  /// 批量处理图像文件
  ///
  /// [inputFiles] 输入图像文件路径列表
  /// [outputDir] 输出目录
  /// [config] 处理配置
  /// [onProgress] 进度回调 (当前索引, 总数)
  static Future<List<String>> processBatch(
    List<String> inputFiles,
    String outputDir, {
    ImageProcessorConfig? config,
    Function(int current, int total)? onProgress,
  }) async {
    final results = <String>[];

    // 确保输出目录存在
    final outputDirectory = Directory(outputDir);
    if (!outputDirectory.existsSync()) {
      outputDirectory.createSync(recursive: true);
    }

    for (int i = 0; i < inputFiles.length; i++) {
      final inputFile = inputFiles[i];
      final fileName = File(inputFile).uri.pathSegments.last;
      final nameWithoutExt = fileName.split('.').first;
      final outputFormat = config?.outputFormat ?? 'jpg';
      final outputFile = '$outputDir/$nameWithoutExt.$outputFormat';

      try {
        await processFile(inputFile, outputFile, config: config);
        results.add(outputFile);
      } catch (e) {
        // 继续处理下一个文件，但记录错误
        PGLog.d('处理文件失败 $inputFile: $e');
      }

      onProgress?.call(i + 1, inputFiles.length);
    }

    return results;
  }

  /// 获取图像信息
  ///
  /// [filePath] 图像文件路径
  /// 返回图像尺寸和通道信息，失败返回null
  static ImageInfo? getImageInfo(String filePath) {
    if (!isInitialized) {
      throw const ImageProcessorException(
        ImageProcessorErrorCode.errorNotInitialized,
        '处理器未初始化，请先调用initialize()',
      );
    }

    return ImageProcessorBindings.getImageInfo(filePath);
  }

  /// 检查文件是否为支持的图像格式
  ///
  /// [filePath] 图像文件路径
  static bool isSupportedFormat(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png'].contains(extension);
  }

  /// 获取默认处理选项
  static ImageProcessorConfig getDefaultConfig() {
    final options = malloc<ImageProcessorOptions>();
    try {
      ImageProcessorBindings.getDefaultOptions(options);

      return ImageProcessorConfig(
        targetWidth: options.ref.targetWidth,
        targetHeight: options.ref.targetHeight,
        maintainAspect: options.ref.maintainAspect == 1,
        quality: options.ref.quality,
        outputFormat: options.ref.outputFormat.toDartString(),
        interpolation: ImageInterpolation.values[options.ref.interpolation],
        correctOrientation: options.ref.correctOrientation == 1,
      );
    } finally {
      malloc.free(options);
    }
  }

  /// 获取库信息
  static Map<String, dynamic> getLibraryInfo() {
    return ImageProcessorBindings.getLibraryInfo();
  }

  /// 释放处理器资源
  static void dispose() {
    if (_initialized) {
      ImageProcessorBindings.free();
      _initialized = false;
    }
  }
}
