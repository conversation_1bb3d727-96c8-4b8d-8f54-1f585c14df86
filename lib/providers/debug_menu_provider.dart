import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pg_desktop_multi_window/desktop_multi_window.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:turing_art/utils/pg_log.dart';
import 'package:turing_art/utils/windows_process_terminator.dart';

class DebugMenuProvider extends ChangeNotifier {
  final SharedPreferences _prefs;
  bool _isVisible = false;
  String _currentEnv = 'dev';
  String _previousEnv = 'dev';
  bool _mockNetwork = false;
  String _proxyIp = "";
  String _proxyPort = "";

  DebugMenuProvider(this._prefs) {
    _loadSettings();
  }

  // Getters
  bool get isVisible => _isVisible;
  String get currentEnv => _currentEnv;
  bool get mockNetwork => _mockNetwork;
  String get previousEnv => _previousEnv;

  String get proxyIp => _proxyIp;
  String get proxyPort => _proxyPort;

  // 加载保存的设置
  Future<void> _loadSettings() async {
    _currentEnv = _prefs.getString('debug_env') ?? 'dev';
    _previousEnv = _currentEnv;
    _mockNetwork = _prefs.getBool('debug_mock_network') ?? false;
    _proxyIp = _prefs.getString("proxy_ip") ?? "";
    _proxyPort = _prefs.getString("proxy_port") ?? "";
  }

  // 切换菜单可见性
  void toggleVisibility() {
    _isVisible = !_isVisible;
    notifyListeners();
  }

  // 设置环境并重启
  Future<void> setEnvironment(String env, {bool restart = false}) async {
    if (_currentEnv == env) {
      return;
    }
    _previousEnv = _currentEnv;
    _currentEnv = env;
    await _prefs.setString('debug_env', env);
    await _prefs.remove("user_id");
    // uid和eid是触发重新登录的关键信息
    await _prefs.remove("employee_id");
    await _prefs.remove("user_token");
    await _prefs.remove("token_expire");
    await _prefs.remove("store");
    await _prefs.remove("creator");
    notifyListeners();

    if (restart) {
      await restartApp();
    }
  }

  void setProxyConfig(String ip, String port) {
    _proxyIp = ip;
    _proxyPort = port;
    _prefs.setString("proxy_ip", ip);
    _prefs.setString("proxy_port", port);
    notifyListeners();
  }

  // 重启应用
  Future<void> restartApp() async {
    exitApp();
  }

  // 强制终止应用程序
  Future<void> exitApp() async {
    if (Platform.isWindows) {
      // 在Windows上使用原生方法终止进程
      try {
        // 1. 先关闭所有子窗口
        DesktopMultiWindow.getAllSubWindowIds().then((windowIds) {
          for (final id in windowIds) {
            WindowController.fromWindowId(id).close();
          }

          // 2. 使用Windows原生方法终止进程
          bool terminated = WindowsProcessTerminator.terminateCurrentProcess();

          // 3. 如果原生方法失败，尝试其他方法
          if (!terminated) {
            SystemNavigator.pop(animated: true);

            // 最后尝试强制退出
            Future.delayed(const Duration(milliseconds: 500), () {
              exit(0);
            });
          }
        });
      } catch (e) {
        PGLog.d('退出应用时出错: $e');
        // 出错时使用强制退出
        exit(0);
      }
    } else {
      // 其他平台使用标准退出
      SystemNavigator.pop();
    }
  }
}
