import 'dart:io';

import 'package:turing_art/datalayer/domain/models/workspace/workspace_file.dart';

enum MediaResourceType {
  mask, // 蒙版 resource/universal_mask
  minIcon, // 小尺寸图标 256 thumb/ps_min_icon
  midIcon, // 中尺寸图标 512 thumb/ps_mid_icon

  previewResource, // 预览图 thumb/ps_preview
  largeResource, // 大尺寸图 thumb/ps_large
  originalResource, // 原始图
}

abstract class MediaRepository {
  // 批量创建某工作区内的媒体文件
  Future<List<WorkspaceFile>> generateWorkspaceFiles(
    String projectId,
    List<File> files,
  );

  // 同步媒体资源文件
  // 假设该文件已经存在则不覆盖，否则创建
  Future<File?> addOrUpdateFileResource(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
    File resourceFile,
  );

  // 删除媒体资源文件
  Future<bool> deleteFileResource(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
  );

  // 获取媒体资源文件
  File? getFileResource(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
  );

  Future<File?> getFileResourceAsync(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
  );

  // 获取媒体资源文件路径(只获取文件路径)
  File getResourceFilePath(
    String projectId,
    String fileId,
    MediaResourceType resourceType,
  );
}
