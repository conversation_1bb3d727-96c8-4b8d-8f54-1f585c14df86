import 'package:turing_art/datalayer/domain/models/export/export_models.dart';
import 'package:turing_art/datalayer/service/api/api_client.dart';
import 'package:turing_art/datalayer/service/api/header_tool.dart';
import 'package:turing_art/datalayer/service/api/request_header.dart';
import 'package:turing_art/datalayer/service/database/dao/export_token_dao.dart';
import 'package:turing_art/datalayer/service/database/database.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'export_exception.dart';
import 'export_repository.dart';

/// 照片导出仓库实现类，负责处理照片导出相关的业务逻辑
///
/// 主要功能：
/// - 创建导出Token
/// - 导出成功上报
/// - 调整照片参数
class ExportRepositoryImpl implements ExportRepository {
  final ApiClient _apiClient;
  final DataBase _db;

  /// 创建 [ExportRepositoryImpl] 实例
  ///
  /// [apiClient] API 客户端，用于网络请求
  /// [db] 数据库实例，用于本地数据存储
  ExportRepositoryImpl({
    required ApiClient apiClient,
    required DataBase db,
  })  : _apiClient = apiClient,
        _db = db;

  /// 获取导出Token，如果数据库中有有效的Token则直接返回，否则创建新的Token
  ///
  /// [userId] 用户ID
  /// [imagePHash] 图片哈希值
  /// [imageName] 图片名称
  /// [projectName] 项目名称
  /// [projectId] 项目ID
  /// [hostname] 主机名
  /// [exportType] 导出类型，默认为retouch
  ///
  /// 返回导出Token，失败抛出 ExportException
  @override
  Future<ExportToken> getExportToken(
    String userId,
    String imagePHash,
    String imageName,
    String projectName,
    String projectId,
    String hostname, {
    ExportType exportType = ExportType.retouch,
  }) async {
    try {
      // 清理过期的Token
      await _db.deleteExpiredExportTokens();

      // 从数据库中查询Token
      final tokenEntity =
          await _db.getExportToken(userId, imagePHash, exportType.name);

      // 如果数据库中有有效的Token，则直接返回
      if (tokenEntity != null) {
        PGLog.d(
            '使用数据库缓存的Token: ${tokenEntity.tokenId} 用户: $userId 图片: $imagePHash');
        return ExportToken(
          tokenId: tokenEntity.tokenId,
          key: tokenEntity.key,
          expireAt: tokenEntity.expireAt,
        );
      }

      // Token不存在，需要创建新Token
      PGLog.d('数据库中无Token，创建新Token 用户: $userId 图片: $imagePHash');
      final exportImage = ExportImage(
        imageName: imageName,
        imagePHash: imagePHash,
      );

      final response = await createExportToken(
        userId,
        [exportImage],
        projectName,
        projectId,
        hostname,
        exportType: exportType,
      );

      if (response == null || response.tokens.isEmpty) {
        throw ExportException('无法为图片创建Token: $imagePHash');
      }

      // 找到对应图片的Token
      final token = response.tokens.first;

      // 保存Token到数据库
      await _db.saveExportToken(
        userId,
        imagePHash,
        imageName,
        token.tokenId,
        token.key,
        token.expireAt,
        exportType.name,
      );

      return token;
    } catch (e, stack) {
      _handleException(e, stack, '获取导出Token异常');
      // 这行代码实际上不会执行，因为上面的方法会抛出异常
      throw ExportException('获取导出Token失败');
    }
  }

  /// 创建导出Token
  ///
  /// [userId] 用户ID
  /// [images] 要导出的图片列表
  /// [projectName] 项目名称
  /// [projectId] 项目ID
  /// [hostname] 主机名
  /// [exportType] 导出类型，默认为retouch
  ///
  /// 返回导出Token信息，失败抛出 ExportException
  @override
  Future<ExportInitializeResponse?> createExportToken(
    String userId,
    List<ExportImage> images,
    String projectName,
    String projectId,
    String hostname, {
    ExportType exportType = ExportType.retouch,
  }) async {
    try {
      final request = ExportInitializeRequest(
        images: images,
        projectName: projectName,
        projectId: projectId,
        hostname: hostname,
        exportType: exportType,
      );

      final headers = RequestHeader.getSignedHeaders(
        '/v1/export/initialize',
        HttpMethod.post,
        request.toJson(),
        forBody: true,
      );

      final response = await _apiClient.post<dynamic>(
        '/v1/export/initialize',
        headers: headers,
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final data = ExportInitializeResponse.fromJson(
            response.data as Map<String, dynamic>);

        // 保存Token到数据库
        final imageTokenPairs = <Map<String, dynamic>>[];

        for (int i = 0; i < images.length && i < data.tokens.length; i++) {
          final image = images[i];
          final token = data.tokens[i];

          imageTokenPairs.add({
            'userId': userId,
            'imagePHash': image.imagePHash,
            'imageName': image.imageName,
            'tokenId': token.tokenId,
            'key': token.key,
            'expireAt': token.expireAt,
            'exportType': exportType.name,
          });
        }

        await _db.saveExportTokens(imageTokenPairs);

        return data;
      } else {
        String errorMessage = response.error ?? '创建导出Token失败';
        int? errorCode = response.statusCode != -1 ? response.statusCode : null;
        if (response.data["code"] == 20001 || response.data["code"] == 20002) {
          errorMessage = '你已被主账号禁用';
          errorCode = response.data["code"];
        }
        throw ExportException(errorMessage, code: errorCode);
      }
    } catch (e, stack) {
      _handleException(e, stack, '创建导出Token异常');
      return null; // 这行代码实际上不会执行，因为上面的方法会抛出异常
    }
  }

  /// 导出成功上报
  ///
  /// [images] 导出成功的图片列表
  ///
  /// 成功返回void，失败抛出 ExportException
  @override
  Future<void> reportExportSuccess(
    List<ExportCompleteImage> images,
    String projectName,
    String projectId,
    String hostname,
  ) async {
    try {
      final request = ExportCompleteRequest(
        images: images,
        projectName: projectName,
        projectId: projectId,
        hostname: hostname,
      );

      final headers = RequestHeader.getSignedHeaders(
        '/v1/export/complete',
        HttpMethod.post,
        request.toJson(),
        forBody: true,
      );

      final response = await _apiClient.post<dynamic>(
        '/v1/export/complete',
        headers: headers,
        data: request.toJson(),
      );

      if (!response.success) {
        final errorMessage = response.error ?? '导出成功上报失败';
        final errorCode =
            response.statusCode != -1 ? response.statusCode : null;
        throw ExportException(errorMessage, code: errorCode);
      }

      // 成功无需解析返回数据
      PGLog.d('导出成功上报成功');
    } catch (e, stack) {
      _handleException(e, stack, '导出成功上报异常');
    }
  }

  /// 调整照片参数
  ///
  /// [tokenId] 导出Token ID
  /// [imagePHash] 图片哈希值
  /// [fields] 参数字段列表
  ///
  /// 返回调整后的参数，失败抛出 ExportException
  @override
  Future<GenParamsResponse?> adjustPhotoParams(
    String tokenId,
    String imagePHash,
    List<ParamField> fields,
  ) async {
    try {
      final request = GenParamsRequest(
        tokenId: tokenId,
        imagePHash: imagePHash,
        fields: fields,
      );

      final headers = RequestHeader.getSignedHeaders(
        '/v1/export/gen-params',
        HttpMethod.post,
        request.toJson(),
        forBody: true,
      );

      final response = await _apiClient.post<dynamic>(
        '/v1/export/gen-params',
        headers: headers,
        data: request.toJson(),
      );

      if (response.success && response.data != null) {
        final data =
            GenParamsResponse.fromJson(response.data as Map<String, dynamic>);
        return data;
      } else {
        final errorMessage = response.error ?? '调整照片参数失败';
        final errorCode =
            response.statusCode != -1 ? response.statusCode : null;
        throw ExportException(errorMessage, code: errorCode);
      }
    } catch (e, stack) {
      _handleException(e, stack, '调整照片参数异常');
      return null; // 这行代码实际上不会执行，因为上面的方法会抛出异常
    }
  }

  /// 清除指定图片的Token缓存
  ///
  /// [userId] 用户ID
  /// [imagePHash] 图片哈希值
  /// [exportType] 导出类型
  @override
  void clearTokenCache(
      String userId, String imagePHash, String exportType) async {
    await _db.deleteExportToken(userId, imagePHash, exportType);
  }

  /// 清除用户所有Token缓存
  ///
  /// [userId] 用户ID
  @override
  void clearUserTokenCache(String userId) async {
    await _db.deleteUserExportTokens(userId);
  }

  /// 清除所有Token缓存
  @override
  void clearAllTokenCache() async {
    // 删除所有Token - 不使用forEach循环，直接删除所有数据
    await _db.delete(_db.exportTokenEntity).go();
  }

  /// 处理异常，将其转换为 ExportException
  ///
  /// [e] 捕获的异常
  /// [stack] 堆栈信息
  /// [logPrefix] 日志前缀
  void _handleException(dynamic e, StackTrace stack, String logPrefix) {
    PGLog.e('$logPrefix: $e\n$stack');
    // 如果已经是 ExportException，则直接抛出
    if (e is ExportException) {
      throw e;
    }
    // 其他异常转换为 ExportException
    throw ExportException(e.toString());
  }
}
