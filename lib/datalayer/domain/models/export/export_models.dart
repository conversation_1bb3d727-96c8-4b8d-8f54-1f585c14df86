/// 导出类型枚举
enum ExportType {
  retouch,
  sample,
}

/// 导出图片信息
class ExportImage {
  final String imageName;
  final String imagePHash;

  ExportImage({
    required this.imageName,
    required this.imagePHash,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['imageName'] = imageName;
    data['imagePHash'] = imagePHash;
    return data;
  }

  factory ExportImage.fromJson(Map<String, dynamic> json) {
    return ExportImage(
      imageName: json['imageName'] as String,
      imagePHash: json['imagePHash'] as String,
    );
  }
}

/// 导出Token信息
class ExportToken {
  final String tokenId;
  final String key;
  final int expireAt;

  ExportToken({
    required this.tokenId,
    required this.key,
    required this.expireAt,
  });

  factory ExportToken.fromJson(Map<String, dynamic> json) {
    return ExportToken(
      tokenId: json['tokenId'] as String,
      key: json['key'] as String,
      expireAt: json['expireAt'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['tokenId'] = tokenId;
    data['key'] = key;
    data['expireAt'] = expireAt;
    return data;
  }
}

/// 创建导出Token请求
class ExportInitializeRequest {
  final List<ExportImage> images;
  final String projectName;
  final String projectId;
  final String hostname;
  final ExportType exportType;

  ExportInitializeRequest({
    required this.images,
    required this.projectName,
    required this.projectId,
    required this.hostname,
    this.exportType = ExportType.retouch, // 默认值为retouch
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['images'] = images.map((e) => e.toJson()).toList();
    data['projectName'] = projectName;
    data['projectId'] = projectId;
    data['hostname'] = hostname;
    data['exportType'] = exportType.name; // 将枚举转换为字符串
    return data;
  }

  factory ExportInitializeRequest.fromJson(Map<String, dynamic> json) {
    return ExportInitializeRequest(
      images: (json['images'] as List)
          .map((e) => ExportImage.fromJson(e as Map<String, dynamic>))
          .toList(),
      projectName: json['projectName'] as String,
      projectId: json['projectId'] as String,
      hostname: json['hostname'] as String,
      exportType: ExportType.values.firstWhere(
        (e) => e.name == (json['exportType'] as String? ?? 'retouch'),
        orElse: () => ExportType.retouch,
      ),
    );
  }
}

/// 创建导出Token响应
class ExportInitializeResponse {
  final List<ExportToken> tokens;
  final int cost;

  ExportInitializeResponse({
    required this.tokens,
    required this.cost,
  });

  factory ExportInitializeResponse.fromJson(Map<String, dynamic> json) {
    return ExportInitializeResponse(
      tokens: (json['tokens'] as List)
          .map((e) => ExportToken.fromJson(e as Map<String, dynamic>))
          .toList(),
      cost: json['cost'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['tokens'] = tokens.map((e) => e.toJson()).toList();
    data['cost'] = cost;
    return data;
  }
}

/// 导出成功上报图片信息
class ExportCompleteImage {
  final String imagePHash;
  final String tokenId;

  ExportCompleteImage({
    required this.imagePHash,
    required this.tokenId,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['imagePHash'] = imagePHash;
    data['tokenId'] = tokenId;
    return data;
  }

  factory ExportCompleteImage.fromJson(Map<String, dynamic> json) {
    return ExportCompleteImage(
      imagePHash: json['imagePHash'] as String,
      tokenId: json['tokenId'] as String,
    );
  }
}

/// 导出成功上报请求
class ExportCompleteRequest {
  final List<ExportCompleteImage> images;
  final String projectName;
  final String projectId;
  final String hostname;

  ExportCompleteRequest({
    required this.images,
    required this.projectName,
    required this.projectId,
    required this.hostname,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['images'] = images.map((e) => e.toJson()).toList();
    data['projectName'] = projectName;
    data['projectId'] = projectId;
    data['hostname'] = hostname;
    return data;
  }

  factory ExportCompleteRequest.fromJson(Map<String, dynamic> json) {
    return ExportCompleteRequest(
      images: (json['images'] as List)
          .map((e) => ExportCompleteImage.fromJson(e as Map<String, dynamic>))
          .toList(),
      projectName: json['projectName'] as String,
      projectId: json['projectId'] as String,
      hostname: json['hostname'] as String,
    );
  }
}

/// 导出成功上报响应
class ExportCompleteResponse {
  final String time;
  final int timestamp;

  ExportCompleteResponse({
    required this.time,
    required this.timestamp,
  });

  factory ExportCompleteResponse.fromJson(Map<String, dynamic> json) {
    return ExportCompleteResponse(
      time: json['time'] as String,
      timestamp: json['timestamp'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['time'] = time;
    data['timestamp'] = timestamp;
    return data;
  }
}

/// 参数字段
class ParamField {
  final String field;
  final String value;

  ParamField({
    required this.field,
    required this.value,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['field'] = field;
    data['value'] = value;
    return data;
  }

  factory ParamField.fromJson(Map<String, dynamic> json) {
    return ParamField(
      field: json['field'] as String,
      value: json['value'] as String,
    );
  }
}

/// 调整照片参数请求
class GenParamsRequest {
  final String tokenId;
  final String imagePHash;
  final List<ParamField> fields;

  GenParamsRequest({
    required this.tokenId,
    required this.imagePHash,
    required this.fields,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['tokenId'] = tokenId;
    data['imagePHash'] = imagePHash;
    data['fields'] = fields.map((e) => e.toJson()).toList();
    return data;
  }

  factory GenParamsRequest.fromJson(Map<String, dynamic> json) {
    return GenParamsRequest(
      tokenId: json['tokenId'] as String,
      imagePHash: json['imagePHash'] as String,
      fields: (json['fields'] as List)
          .map((e) => ParamField.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// 调整照片参数响应
class GenParamsResponse {
  final List<ParamField> fields;

  GenParamsResponse({
    required this.fields,
  });

  factory GenParamsResponse.fromJson(Map<String, dynamic> json) {
    return GenParamsResponse(
      fields: (json['fields'] as List)
          .map((e) => ParamField.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['fields'] = fields.map((e) => e.toJson()).toList();
    return data;
  }
}
