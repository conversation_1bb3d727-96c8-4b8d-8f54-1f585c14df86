// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'export_record.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExportRecord _$ExportRecordFromJson(Map<String, dynamic> json) {
  return _ExportRecord.fromJson(json);
}

/// @nodoc
mixin _$ExportRecord {
  String get guid => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get showName => throw _privateConstructorUsedError;
  List<String> get exportPaths => throw _privateConstructorUsedError;
  List<ExportFile> get exportFiles => throw _privateConstructorUsedError;
  int get exportState => throw _privateConstructorUsedError;
  int get createTime => throw _privateConstructorUsedError;
  String get operateTime => throw _privateConstructorUsedError;
  int get successNum => throw _privateConstructorUsedError;
  int? get errorNum => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;
  bool get isSample => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportRecordCopyWith<ExportRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportRecordCopyWith<$Res> {
  factory $ExportRecordCopyWith(
          ExportRecord value, $Res Function(ExportRecord) then) =
      _$ExportRecordCopyWithImpl<$Res, ExportRecord>;
  @useResult
  $Res call(
      {String guid,
      String? name,
      String? showName,
      List<String> exportPaths,
      List<ExportFile> exportFiles,
      int exportState,
      int createTime,
      String operateTime,
      int successNum,
      int? errorNum,
      String? errorMessage,
      bool isSample});
}

/// @nodoc
class _$ExportRecordCopyWithImpl<$Res, $Val extends ExportRecord>
    implements $ExportRecordCopyWith<$Res> {
  _$ExportRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guid = null,
    Object? name = freezed,
    Object? showName = freezed,
    Object? exportPaths = null,
    Object? exportFiles = null,
    Object? exportState = null,
    Object? createTime = null,
    Object? operateTime = null,
    Object? successNum = null,
    Object? errorNum = freezed,
    Object? errorMessage = freezed,
    Object? isSample = null,
  }) {
    return _then(_value.copyWith(
      guid: null == guid
          ? _value.guid
          : guid // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      showName: freezed == showName
          ? _value.showName
          : showName // ignore: cast_nullable_to_non_nullable
              as String?,
      exportPaths: null == exportPaths
          ? _value.exportPaths
          : exportPaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      exportFiles: null == exportFiles
          ? _value.exportFiles
          : exportFiles // ignore: cast_nullable_to_non_nullable
              as List<ExportFile>,
      exportState: null == exportState
          ? _value.exportState
          : exportState // ignore: cast_nullable_to_non_nullable
              as int,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as int,
      operateTime: null == operateTime
          ? _value.operateTime
          : operateTime // ignore: cast_nullable_to_non_nullable
              as String,
      successNum: null == successNum
          ? _value.successNum
          : successNum // ignore: cast_nullable_to_non_nullable
              as int,
      errorNum: freezed == errorNum
          ? _value.errorNum
          : errorNum // ignore: cast_nullable_to_non_nullable
              as int?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isSample: null == isSample
          ? _value.isSample
          : isSample // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExportRecordImplCopyWith<$Res>
    implements $ExportRecordCopyWith<$Res> {
  factory _$$ExportRecordImplCopyWith(
          _$ExportRecordImpl value, $Res Function(_$ExportRecordImpl) then) =
      __$$ExportRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String guid,
      String? name,
      String? showName,
      List<String> exportPaths,
      List<ExportFile> exportFiles,
      int exportState,
      int createTime,
      String operateTime,
      int successNum,
      int? errorNum,
      String? errorMessage,
      bool isSample});
}

/// @nodoc
class __$$ExportRecordImplCopyWithImpl<$Res>
    extends _$ExportRecordCopyWithImpl<$Res, _$ExportRecordImpl>
    implements _$$ExportRecordImplCopyWith<$Res> {
  __$$ExportRecordImplCopyWithImpl(
      _$ExportRecordImpl _value, $Res Function(_$ExportRecordImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? guid = null,
    Object? name = freezed,
    Object? showName = freezed,
    Object? exportPaths = null,
    Object? exportFiles = null,
    Object? exportState = null,
    Object? createTime = null,
    Object? operateTime = null,
    Object? successNum = null,
    Object? errorNum = freezed,
    Object? errorMessage = freezed,
    Object? isSample = null,
  }) {
    return _then(_$ExportRecordImpl(
      guid: null == guid
          ? _value.guid
          : guid // ignore: cast_nullable_to_non_nullable
              as String,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      showName: freezed == showName
          ? _value.showName
          : showName // ignore: cast_nullable_to_non_nullable
              as String?,
      exportPaths: null == exportPaths
          ? _value._exportPaths
          : exportPaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      exportFiles: null == exportFiles
          ? _value._exportFiles
          : exportFiles // ignore: cast_nullable_to_non_nullable
              as List<ExportFile>,
      exportState: null == exportState
          ? _value.exportState
          : exportState // ignore: cast_nullable_to_non_nullable
              as int,
      createTime: null == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as int,
      operateTime: null == operateTime
          ? _value.operateTime
          : operateTime // ignore: cast_nullable_to_non_nullable
              as String,
      successNum: null == successNum
          ? _value.successNum
          : successNum // ignore: cast_nullable_to_non_nullable
              as int,
      errorNum: freezed == errorNum
          ? _value.errorNum
          : errorNum // ignore: cast_nullable_to_non_nullable
              as int?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isSample: null == isSample
          ? _value.isSample
          : isSample // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportRecordImpl extends _ExportRecord {
  const _$ExportRecordImpl(
      {required this.guid,
      this.name,
      this.showName,
      required final List<String> exportPaths,
      required final List<ExportFile> exportFiles,
      required this.exportState,
      required this.createTime,
      required this.operateTime,
      required this.successNum,
      this.errorNum,
      this.errorMessage,
      this.isSample = false})
      : _exportPaths = exportPaths,
        _exportFiles = exportFiles,
        super._();

  factory _$ExportRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportRecordImplFromJson(json);

  @override
  final String guid;
  @override
  final String? name;
  @override
  final String? showName;
  final List<String> _exportPaths;
  @override
  List<String> get exportPaths {
    if (_exportPaths is EqualUnmodifiableListView) return _exportPaths;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exportPaths);
  }

  final List<ExportFile> _exportFiles;
  @override
  List<ExportFile> get exportFiles {
    if (_exportFiles is EqualUnmodifiableListView) return _exportFiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exportFiles);
  }

  @override
  final int exportState;
  @override
  final int createTime;
  @override
  final String operateTime;
  @override
  final int successNum;
  @override
  final int? errorNum;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final bool isSample;

  @override
  String toString() {
    return 'ExportRecord(guid: $guid, name: $name, showName: $showName, exportPaths: $exportPaths, exportFiles: $exportFiles, exportState: $exportState, createTime: $createTime, operateTime: $operateTime, successNum: $successNum, errorNum: $errorNum, errorMessage: $errorMessage, isSample: $isSample)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportRecordImpl &&
            (identical(other.guid, guid) || other.guid == guid) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.showName, showName) ||
                other.showName == showName) &&
            const DeepCollectionEquality()
                .equals(other._exportPaths, _exportPaths) &&
            const DeepCollectionEquality()
                .equals(other._exportFiles, _exportFiles) &&
            (identical(other.exportState, exportState) ||
                other.exportState == exportState) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.operateTime, operateTime) ||
                other.operateTime == operateTime) &&
            (identical(other.successNum, successNum) ||
                other.successNum == successNum) &&
            (identical(other.errorNum, errorNum) ||
                other.errorNum == errorNum) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isSample, isSample) ||
                other.isSample == isSample));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      guid,
      name,
      showName,
      const DeepCollectionEquality().hash(_exportPaths),
      const DeepCollectionEquality().hash(_exportFiles),
      exportState,
      createTime,
      operateTime,
      successNum,
      errorNum,
      errorMessage,
      isSample);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportRecordImplCopyWith<_$ExportRecordImpl> get copyWith =>
      __$$ExportRecordImplCopyWithImpl<_$ExportRecordImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportRecordImplToJson(
      this,
    );
  }
}

abstract class _ExportRecord extends ExportRecord {
  const factory _ExportRecord(
      {required final String guid,
      final String? name,
      final String? showName,
      required final List<String> exportPaths,
      required final List<ExportFile> exportFiles,
      required final int exportState,
      required final int createTime,
      required final String operateTime,
      required final int successNum,
      final int? errorNum,
      final String? errorMessage,
      final bool isSample}) = _$ExportRecordImpl;
  const _ExportRecord._() : super._();

  factory _ExportRecord.fromJson(Map<String, dynamic> json) =
      _$ExportRecordImpl.fromJson;

  @override
  String get guid;
  @override
  String? get name;
  @override
  String? get showName;
  @override
  List<String> get exportPaths;
  @override
  List<ExportFile> get exportFiles;
  @override
  int get exportState;
  @override
  int get createTime;
  @override
  String get operateTime;
  @override
  int get successNum;
  @override
  int? get errorNum;
  @override
  String? get errorMessage;
  @override
  bool get isSample;
  @override
  @JsonKey(ignore: true)
  _$$ExportRecordImplCopyWith<_$ExportRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ExportFile _$ExportFileFromJson(Map<String, dynamic> json) {
  return _ExportFile.fromJson(json);
}

/// @nodoc
mixin _$ExportFile {
  ExportFileParam get fileParam => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportFileCopyWith<ExportFile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportFileCopyWith<$Res> {
  factory $ExportFileCopyWith(
          ExportFile value, $Res Function(ExportFile) then) =
      _$ExportFileCopyWithImpl<$Res, ExportFile>;
  @useResult
  $Res call({ExportFileParam fileParam});

  $ExportFileParamCopyWith<$Res> get fileParam;
}

/// @nodoc
class _$ExportFileCopyWithImpl<$Res, $Val extends ExportFile>
    implements $ExportFileCopyWith<$Res> {
  _$ExportFileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileParam = null,
  }) {
    return _then(_value.copyWith(
      fileParam: null == fileParam
          ? _value.fileParam
          : fileParam // ignore: cast_nullable_to_non_nullable
              as ExportFileParam,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $ExportFileParamCopyWith<$Res> get fileParam {
    return $ExportFileParamCopyWith<$Res>(_value.fileParam, (value) {
      return _then(_value.copyWith(fileParam: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ExportFileImplCopyWith<$Res>
    implements $ExportFileCopyWith<$Res> {
  factory _$$ExportFileImplCopyWith(
          _$ExportFileImpl value, $Res Function(_$ExportFileImpl) then) =
      __$$ExportFileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({ExportFileParam fileParam});

  @override
  $ExportFileParamCopyWith<$Res> get fileParam;
}

/// @nodoc
class __$$ExportFileImplCopyWithImpl<$Res>
    extends _$ExportFileCopyWithImpl<$Res, _$ExportFileImpl>
    implements _$$ExportFileImplCopyWith<$Res> {
  __$$ExportFileImplCopyWithImpl(
      _$ExportFileImpl _value, $Res Function(_$ExportFileImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileParam = null,
  }) {
    return _then(_$ExportFileImpl(
      fileParam: null == fileParam
          ? _value.fileParam
          : fileParam // ignore: cast_nullable_to_non_nullable
              as ExportFileParam,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportFileImpl extends _ExportFile {
  const _$ExportFileImpl({required this.fileParam}) : super._();

  factory _$ExportFileImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportFileImplFromJson(json);

  @override
  final ExportFileParam fileParam;

  @override
  String toString() {
    return 'ExportFile(fileParam: $fileParam)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportFileImpl &&
            (identical(other.fileParam, fileParam) ||
                other.fileParam == fileParam));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, fileParam);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportFileImplCopyWith<_$ExportFileImpl> get copyWith =>
      __$$ExportFileImplCopyWithImpl<_$ExportFileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportFileImplToJson(
      this,
    );
  }
}

abstract class _ExportFile extends ExportFile {
  const factory _ExportFile({required final ExportFileParam fileParam}) =
      _$ExportFileImpl;
  const _ExportFile._() : super._();

  factory _ExportFile.fromJson(Map<String, dynamic> json) =
      _$ExportFileImpl.fromJson;

  @override
  ExportFileParam get fileParam;
  @override
  @JsonKey(ignore: true)
  _$$ExportFileImplCopyWith<_$ExportFileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ExportFileParam _$ExportFileParamFromJson(Map<String, dynamic> json) {
  return _ExportFileParam.fromJson(json);
}

/// @nodoc
mixin _$ExportFileParam {
  String get fileId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExportFileParamCopyWith<ExportFileParam> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExportFileParamCopyWith<$Res> {
  factory $ExportFileParamCopyWith(
          ExportFileParam value, $Res Function(ExportFileParam) then) =
      _$ExportFileParamCopyWithImpl<$Res, ExportFileParam>;
  @useResult
  $Res call({String fileId});
}

/// @nodoc
class _$ExportFileParamCopyWithImpl<$Res, $Val extends ExportFileParam>
    implements $ExportFileParamCopyWith<$Res> {
  _$ExportFileParamCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileId = null,
  }) {
    return _then(_value.copyWith(
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExportFileParamImplCopyWith<$Res>
    implements $ExportFileParamCopyWith<$Res> {
  factory _$$ExportFileParamImplCopyWith(_$ExportFileParamImpl value,
          $Res Function(_$ExportFileParamImpl) then) =
      __$$ExportFileParamImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String fileId});
}

/// @nodoc
class __$$ExportFileParamImplCopyWithImpl<$Res>
    extends _$ExportFileParamCopyWithImpl<$Res, _$ExportFileParamImpl>
    implements _$$ExportFileParamImplCopyWith<$Res> {
  __$$ExportFileParamImplCopyWithImpl(
      _$ExportFileParamImpl _value, $Res Function(_$ExportFileParamImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? fileId = null,
  }) {
    return _then(_$ExportFileParamImpl(
      fileId: null == fileId
          ? _value.fileId
          : fileId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExportFileParamImpl extends _ExportFileParam {
  const _$ExportFileParamImpl({required this.fileId}) : super._();

  factory _$ExportFileParamImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExportFileParamImplFromJson(json);

  @override
  final String fileId;

  @override
  String toString() {
    return 'ExportFileParam(fileId: $fileId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportFileParamImpl &&
            (identical(other.fileId, fileId) || other.fileId == fileId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, fileId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportFileParamImplCopyWith<_$ExportFileParamImpl> get copyWith =>
      __$$ExportFileParamImplCopyWithImpl<_$ExportFileParamImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExportFileParamImplToJson(
      this,
    );
  }
}

abstract class _ExportFileParam extends ExportFileParam {
  const factory _ExportFileParam({required final String fileId}) =
      _$ExportFileParamImpl;
  const _ExportFileParam._() : super._();

  factory _ExportFileParam.fromJson(Map<String, dynamic> json) =
      _$ExportFileParamImpl.fromJson;

  @override
  String get fileId;
  @override
  @JsonKey(ignore: true)
  _$$ExportFileParamImplCopyWith<_$ExportFileParamImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
