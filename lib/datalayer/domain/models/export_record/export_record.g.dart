// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'export_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExportRecordImpl _$$ExportRecordImplFromJson(Map<String, dynamic> json) =>
    _$ExportRecordImpl(
      guid: json['guid'] as String,
      name: json['name'] as String?,
      showName: json['showName'] as String?,
      exportPaths: (json['exportPaths'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      exportFiles: (json['exportFiles'] as List<dynamic>)
          .map((e) => ExportFile.fromJson(e as Map<String, dynamic>))
          .toList(),
      exportState: (json['exportState'] as num).toInt(),
      createTime: (json['createTime'] as num).toInt(),
      operateTime: json['operateTime'] as String,
      successNum: (json['successNum'] as num).toInt(),
      errorNum: (json['errorNum'] as num?)?.toInt(),
      errorMessage: json['errorMessage'] as String?,
      isSample: json['isSample'] as bool? ?? false,
    );

Map<String, dynamic> _$$ExportRecordImplToJson(_$ExportRecordImpl instance) =>
    <String, dynamic>{
      'guid': instance.guid,
      'name': instance.name,
      'showName': instance.showName,
      'exportPaths': instance.exportPaths,
      'exportFiles': instance.exportFiles,
      'exportState': instance.exportState,
      'createTime': instance.createTime,
      'operateTime': instance.operateTime,
      'successNum': instance.successNum,
      'errorNum': instance.errorNum,
      'errorMessage': instance.errorMessage,
      'isSample': instance.isSample,
    };

_$ExportFileImpl _$$ExportFileImplFromJson(Map<String, dynamic> json) =>
    _$ExportFileImpl(
      fileParam:
          ExportFileParam.fromJson(json['fileParam'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$ExportFileImplToJson(_$ExportFileImpl instance) =>
    <String, dynamic>{
      'fileParam': instance.fileParam,
    };

_$ExportFileParamImpl _$$ExportFileParamImplFromJson(
        Map<String, dynamic> json) =>
    _$ExportFileParamImpl(
      fileId: json['fileId'] as String,
    );

Map<String, dynamic> _$$ExportFileParamImplToJson(
        _$ExportFileParamImpl instance) =>
    <String, dynamic>{
      'fileId': instance.fileId,
    };
