// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'external_message.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExternalMessage _$ExternalMessageFromJson(Map<String, dynamic> json) {
  return _ExternalMessage.fromJson(json);
}

/// @nodoc
mixin _$ExternalMessage {
  ExternalMessageType get type => throw _privateConstructorUsedError;
  Map<String, dynamic> get data => throw _privateConstructorUsedError;
  String? get requestId => throw _privateConstructorUsedError;
  int get timestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExternalMessageCopyWith<ExternalMessage> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExternalMessageCopyWith<$Res> {
  factory $ExternalMessageCopyWith(
          ExternalMessage value, $Res Function(ExternalMessage) then) =
      _$ExternalMessageCopyWithImpl<$Res, ExternalMessage>;
  @useResult
  $Res call(
      {ExternalMessageType type,
      Map<String, dynamic> data,
      String? requestId,
      int timestamp});
}

/// @nodoc
class _$ExternalMessageCopyWithImpl<$Res, $Val extends ExternalMessage>
    implements $ExternalMessageCopyWith<$Res> {
  _$ExternalMessageCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? data = null,
    Object? requestId = freezed,
    Object? timestamp = null,
  }) {
    return _then(_value.copyWith(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ExternalMessageType,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      requestId: freezed == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ExternalMessageImplCopyWith<$Res>
    implements $ExternalMessageCopyWith<$Res> {
  factory _$$ExternalMessageImplCopyWith(_$ExternalMessageImpl value,
          $Res Function(_$ExternalMessageImpl) then) =
      __$$ExternalMessageImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {ExternalMessageType type,
      Map<String, dynamic> data,
      String? requestId,
      int timestamp});
}

/// @nodoc
class __$$ExternalMessageImplCopyWithImpl<$Res>
    extends _$ExternalMessageCopyWithImpl<$Res, _$ExternalMessageImpl>
    implements _$$ExternalMessageImplCopyWith<$Res> {
  __$$ExternalMessageImplCopyWithImpl(
      _$ExternalMessageImpl _value, $Res Function(_$ExternalMessageImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = null,
    Object? data = null,
    Object? requestId = freezed,
    Object? timestamp = null,
  }) {
    return _then(_$ExternalMessageImpl(
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as ExternalMessageType,
      data: null == data
          ? _value._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      requestId: freezed == requestId
          ? _value.requestId
          : requestId // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ExternalMessageImpl extends _ExternalMessage {
  const _$ExternalMessageImpl(
      {required this.type,
      required final Map<String, dynamic> data,
      this.requestId,
      this.timestamp = 0})
      : _data = data,
        super._();

  factory _$ExternalMessageImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExternalMessageImplFromJson(json);

  @override
  final ExternalMessageType type;
  final Map<String, dynamic> _data;
  @override
  Map<String, dynamic> get data {
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_data);
  }

  @override
  final String? requestId;
  @override
  @JsonKey()
  final int timestamp;

  @override
  String toString() {
    return 'ExternalMessage(type: $type, data: $data, requestId: $requestId, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExternalMessageImpl &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.requestId, requestId) ||
                other.requestId == requestId) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type,
      const DeepCollectionEquality().hash(_data), requestId, timestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExternalMessageImplCopyWith<_$ExternalMessageImpl> get copyWith =>
      __$$ExternalMessageImplCopyWithImpl<_$ExternalMessageImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExternalMessageImplToJson(
      this,
    );
  }
}

abstract class _ExternalMessage extends ExternalMessage {
  const factory _ExternalMessage(
      {required final ExternalMessageType type,
      required final Map<String, dynamic> data,
      final String? requestId,
      final int timestamp}) = _$ExternalMessageImpl;
  const _ExternalMessage._() : super._();

  factory _ExternalMessage.fromJson(Map<String, dynamic> json) =
      _$ExternalMessageImpl.fromJson;

  @override
  ExternalMessageType get type;
  @override
  Map<String, dynamic> get data;
  @override
  String? get requestId;
  @override
  int get timestamp;
  @override
  @JsonKey(ignore: true)
  _$$ExternalMessageImplCopyWith<_$ExternalMessageImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FileItem _$FileItemFromJson(Map<String, dynamic> json) {
  return _FileItem.fromJson(json);
}

/// @nodoc
mixin _$FileItem {
  String get originalPath => throw _privateConstructorUsedError;
  String? get historyId => throw _privateConstructorUsedError;
  bool get isSelected => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FileItemCopyWith<FileItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FileItemCopyWith<$Res> {
  factory $FileItemCopyWith(FileItem value, $Res Function(FileItem) then) =
      _$FileItemCopyWithImpl<$Res, FileItem>;
  @useResult
  $Res call({String originalPath, String? historyId, bool isSelected});
}

/// @nodoc
class _$FileItemCopyWithImpl<$Res, $Val extends FileItem>
    implements $FileItemCopyWith<$Res> {
  _$FileItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? originalPath = null,
    Object? historyId = freezed,
    Object? isSelected = null,
  }) {
    return _then(_value.copyWith(
      originalPath: null == originalPath
          ? _value.originalPath
          : originalPath // ignore: cast_nullable_to_non_nullable
              as String,
      historyId: freezed == historyId
          ? _value.historyId
          : historyId // ignore: cast_nullable_to_non_nullable
              as String?,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FileItemImplCopyWith<$Res>
    implements $FileItemCopyWith<$Res> {
  factory _$$FileItemImplCopyWith(
          _$FileItemImpl value, $Res Function(_$FileItemImpl) then) =
      __$$FileItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String originalPath, String? historyId, bool isSelected});
}

/// @nodoc
class __$$FileItemImplCopyWithImpl<$Res>
    extends _$FileItemCopyWithImpl<$Res, _$FileItemImpl>
    implements _$$FileItemImplCopyWith<$Res> {
  __$$FileItemImplCopyWithImpl(
      _$FileItemImpl _value, $Res Function(_$FileItemImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? originalPath = null,
    Object? historyId = freezed,
    Object? isSelected = null,
  }) {
    return _then(_$FileItemImpl(
      originalPath: null == originalPath
          ? _value.originalPath
          : originalPath // ignore: cast_nullable_to_non_nullable
              as String,
      historyId: freezed == historyId
          ? _value.historyId
          : historyId // ignore: cast_nullable_to_non_nullable
              as String?,
      isSelected: null == isSelected
          ? _value.isSelected
          : isSelected // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FileItemImpl extends _FileItem {
  const _$FileItemImpl(
      {required this.originalPath, this.historyId, this.isSelected = false})
      : super._();

  factory _$FileItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$FileItemImplFromJson(json);

  @override
  final String originalPath;
  @override
  final String? historyId;
  @override
  @JsonKey()
  final bool isSelected;

  @override
  String toString() {
    return 'FileItem(originalPath: $originalPath, historyId: $historyId, isSelected: $isSelected)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileItemImpl &&
            (identical(other.originalPath, originalPath) ||
                other.originalPath == originalPath) &&
            (identical(other.historyId, historyId) ||
                other.historyId == historyId) &&
            (identical(other.isSelected, isSelected) ||
                other.isSelected == isSelected));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, originalPath, historyId, isSelected);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FileItemImplCopyWith<_$FileItemImpl> get copyWith =>
      __$$FileItemImplCopyWithImpl<_$FileItemImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FileItemImplToJson(
      this,
    );
  }
}

abstract class _FileItem extends FileItem {
  const factory _FileItem(
      {required final String originalPath,
      final String? historyId,
      final bool isSelected}) = _$FileItemImpl;
  const _FileItem._() : super._();

  factory _FileItem.fromJson(Map<String, dynamic> json) =
      _$FileItemImpl.fromJson;

  @override
  String get originalPath;
  @override
  String? get historyId;
  @override
  bool get isSelected;
  @override
  @JsonKey(ignore: true)
  _$$FileItemImplCopyWith<_$FileItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ImportProjectData _$ImportProjectDataFromJson(Map<String, dynamic> json) {
  return _ImportProjectData.fromJson(json);
}

/// @nodoc
mixin _$ImportProjectData {
  String? get projectId => throw _privateConstructorUsedError;
  String? get projectName => throw _privateConstructorUsedError; // 文件列表字段
  List<FileItem> get fileList => throw _privateConstructorUsedError;
  bool get autoNavigate => throw _privateConstructorUsedError;
  bool get newProject => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ImportProjectDataCopyWith<ImportProjectData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ImportProjectDataCopyWith<$Res> {
  factory $ImportProjectDataCopyWith(
          ImportProjectData value, $Res Function(ImportProjectData) then) =
      _$ImportProjectDataCopyWithImpl<$Res, ImportProjectData>;
  @useResult
  $Res call(
      {String? projectId,
      String? projectName,
      List<FileItem> fileList,
      bool autoNavigate,
      bool newProject});
}

/// @nodoc
class _$ImportProjectDataCopyWithImpl<$Res, $Val extends ImportProjectData>
    implements $ImportProjectDataCopyWith<$Res> {
  _$ImportProjectDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? projectName = freezed,
    Object? fileList = null,
    Object? autoNavigate = null,
    Object? newProject = null,
  }) {
    return _then(_value.copyWith(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      projectName: freezed == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String?,
      fileList: null == fileList
          ? _value.fileList
          : fileList // ignore: cast_nullable_to_non_nullable
              as List<FileItem>,
      autoNavigate: null == autoNavigate
          ? _value.autoNavigate
          : autoNavigate // ignore: cast_nullable_to_non_nullable
              as bool,
      newProject: null == newProject
          ? _value.newProject
          : newProject // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ImportProjectDataImplCopyWith<$Res>
    implements $ImportProjectDataCopyWith<$Res> {
  factory _$$ImportProjectDataImplCopyWith(_$ImportProjectDataImpl value,
          $Res Function(_$ImportProjectDataImpl) then) =
      __$$ImportProjectDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? projectId,
      String? projectName,
      List<FileItem> fileList,
      bool autoNavigate,
      bool newProject});
}

/// @nodoc
class __$$ImportProjectDataImplCopyWithImpl<$Res>
    extends _$ImportProjectDataCopyWithImpl<$Res, _$ImportProjectDataImpl>
    implements _$$ImportProjectDataImplCopyWith<$Res> {
  __$$ImportProjectDataImplCopyWithImpl(_$ImportProjectDataImpl _value,
      $Res Function(_$ImportProjectDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = freezed,
    Object? projectName = freezed,
    Object? fileList = null,
    Object? autoNavigate = null,
    Object? newProject = null,
  }) {
    return _then(_$ImportProjectDataImpl(
      projectId: freezed == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String?,
      projectName: freezed == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String?,
      fileList: null == fileList
          ? _value._fileList
          : fileList // ignore: cast_nullable_to_non_nullable
              as List<FileItem>,
      autoNavigate: null == autoNavigate
          ? _value.autoNavigate
          : autoNavigate // ignore: cast_nullable_to_non_nullable
              as bool,
      newProject: null == newProject
          ? _value.newProject
          : newProject // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ImportProjectDataImpl extends _ImportProjectData {
  const _$ImportProjectDataImpl(
      {this.projectId,
      this.projectName,
      required final List<FileItem> fileList,
      this.autoNavigate = false,
      this.newProject = false})
      : _fileList = fileList,
        super._();

  factory _$ImportProjectDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ImportProjectDataImplFromJson(json);

  @override
  final String? projectId;
  @override
  final String? projectName;
// 文件列表字段
  final List<FileItem> _fileList;
// 文件列表字段
  @override
  List<FileItem> get fileList {
    if (_fileList is EqualUnmodifiableListView) return _fileList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fileList);
  }

  @override
  @JsonKey()
  final bool autoNavigate;
  @override
  @JsonKey()
  final bool newProject;

  @override
  String toString() {
    return 'ImportProjectData(projectId: $projectId, projectName: $projectName, fileList: $fileList, autoNavigate: $autoNavigate, newProject: $newProject)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ImportProjectDataImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId) &&
            (identical(other.projectName, projectName) ||
                other.projectName == projectName) &&
            const DeepCollectionEquality().equals(other._fileList, _fileList) &&
            (identical(other.autoNavigate, autoNavigate) ||
                other.autoNavigate == autoNavigate) &&
            (identical(other.newProject, newProject) ||
                other.newProject == newProject));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, projectId, projectName,
      const DeepCollectionEquality().hash(_fileList), autoNavigate, newProject);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ImportProjectDataImplCopyWith<_$ImportProjectDataImpl> get copyWith =>
      __$$ImportProjectDataImplCopyWithImpl<_$ImportProjectDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ImportProjectDataImplToJson(
      this,
    );
  }
}

abstract class _ImportProjectData extends ImportProjectData {
  const factory _ImportProjectData(
      {final String? projectId,
      final String? projectName,
      required final List<FileItem> fileList,
      final bool autoNavigate,
      final bool newProject}) = _$ImportProjectDataImpl;
  const _ImportProjectData._() : super._();

  factory _ImportProjectData.fromJson(Map<String, dynamic> json) =
      _$ImportProjectDataImpl.fromJson;

  @override
  String? get projectId;
  @override
  String? get projectName;
  @override // 文件列表字段
  List<FileItem> get fileList;
  @override
  bool get autoNavigate;
  @override
  bool get newProject;
  @override
  @JsonKey(ignore: true)
  _$$ImportProjectDataImplCopyWith<_$ImportProjectDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SmartData _$SmartDataFromJson(Map<String, dynamic> json) {
  return _SmartData.fromJson(json);
}

/// @nodoc
mixin _$SmartData {
  List<String> get imagePaths => throw _privateConstructorUsedError;
  String? get projectName => throw _privateConstructorUsedError;
  bool get autoNavigate => throw _privateConstructorUsedError;
  bool get newProject => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SmartDataCopyWith<SmartData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SmartDataCopyWith<$Res> {
  factory $SmartDataCopyWith(SmartData value, $Res Function(SmartData) then) =
      _$SmartDataCopyWithImpl<$Res, SmartData>;
  @useResult
  $Res call(
      {List<String> imagePaths,
      String? projectName,
      bool autoNavigate,
      bool newProject});
}

/// @nodoc
class _$SmartDataCopyWithImpl<$Res, $Val extends SmartData>
    implements $SmartDataCopyWith<$Res> {
  _$SmartDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imagePaths = null,
    Object? projectName = freezed,
    Object? autoNavigate = null,
    Object? newProject = null,
  }) {
    return _then(_value.copyWith(
      imagePaths: null == imagePaths
          ? _value.imagePaths
          : imagePaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      projectName: freezed == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String?,
      autoNavigate: null == autoNavigate
          ? _value.autoNavigate
          : autoNavigate // ignore: cast_nullable_to_non_nullable
              as bool,
      newProject: null == newProject
          ? _value.newProject
          : newProject // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SmartDataImplCopyWith<$Res>
    implements $SmartDataCopyWith<$Res> {
  factory _$$SmartDataImplCopyWith(
          _$SmartDataImpl value, $Res Function(_$SmartDataImpl) then) =
      __$$SmartDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<String> imagePaths,
      String? projectName,
      bool autoNavigate,
      bool newProject});
}

/// @nodoc
class __$$SmartDataImplCopyWithImpl<$Res>
    extends _$SmartDataCopyWithImpl<$Res, _$SmartDataImpl>
    implements _$$SmartDataImplCopyWith<$Res> {
  __$$SmartDataImplCopyWithImpl(
      _$SmartDataImpl _value, $Res Function(_$SmartDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? imagePaths = null,
    Object? projectName = freezed,
    Object? autoNavigate = null,
    Object? newProject = null,
  }) {
    return _then(_$SmartDataImpl(
      imagePaths: null == imagePaths
          ? _value._imagePaths
          : imagePaths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      projectName: freezed == projectName
          ? _value.projectName
          : projectName // ignore: cast_nullable_to_non_nullable
              as String?,
      autoNavigate: null == autoNavigate
          ? _value.autoNavigate
          : autoNavigate // ignore: cast_nullable_to_non_nullable
              as bool,
      newProject: null == newProject
          ? _value.newProject
          : newProject // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SmartDataImpl extends _SmartData {
  const _$SmartDataImpl(
      {required final List<String> imagePaths,
      this.projectName,
      this.autoNavigate = false,
      this.newProject = false})
      : _imagePaths = imagePaths,
        super._();

  factory _$SmartDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$SmartDataImplFromJson(json);

  final List<String> _imagePaths;
  @override
  List<String> get imagePaths {
    if (_imagePaths is EqualUnmodifiableListView) return _imagePaths;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_imagePaths);
  }

  @override
  final String? projectName;
  @override
  @JsonKey()
  final bool autoNavigate;
  @override
  @JsonKey()
  final bool newProject;

  @override
  String toString() {
    return 'SmartData(imagePaths: $imagePaths, projectName: $projectName, autoNavigate: $autoNavigate, newProject: $newProject)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SmartDataImpl &&
            const DeepCollectionEquality()
                .equals(other._imagePaths, _imagePaths) &&
            (identical(other.projectName, projectName) ||
                other.projectName == projectName) &&
            (identical(other.autoNavigate, autoNavigate) ||
                other.autoNavigate == autoNavigate) &&
            (identical(other.newProject, newProject) ||
                other.newProject == newProject));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_imagePaths),
      projectName,
      autoNavigate,
      newProject);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SmartDataImplCopyWith<_$SmartDataImpl> get copyWith =>
      __$$SmartDataImplCopyWithImpl<_$SmartDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SmartDataImplToJson(
      this,
    );
  }
}

abstract class _SmartData extends SmartData {
  const factory _SmartData(
      {required final List<String> imagePaths,
      final String? projectName,
      final bool autoNavigate,
      final bool newProject}) = _$SmartDataImpl;
  const _SmartData._() : super._();

  factory _SmartData.fromJson(Map<String, dynamic> json) =
      _$SmartDataImpl.fromJson;

  @override
  List<String> get imagePaths;
  @override
  String? get projectName;
  @override
  bool get autoNavigate;
  @override
  bool get newProject;
  @override
  @JsonKey(ignore: true)
  _$$SmartDataImplCopyWith<_$SmartDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OpenProjectData _$OpenProjectDataFromJson(Map<String, dynamic> json) {
  return _OpenProjectData.fromJson(json);
}

/// @nodoc
mixin _$OpenProjectData {
  String get projectId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $OpenProjectDataCopyWith<OpenProjectData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OpenProjectDataCopyWith<$Res> {
  factory $OpenProjectDataCopyWith(
          OpenProjectData value, $Res Function(OpenProjectData) then) =
      _$OpenProjectDataCopyWithImpl<$Res, OpenProjectData>;
  @useResult
  $Res call({String projectId});
}

/// @nodoc
class _$OpenProjectDataCopyWithImpl<$Res, $Val extends OpenProjectData>
    implements $OpenProjectDataCopyWith<$Res> {
  _$OpenProjectDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
  }) {
    return _then(_value.copyWith(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OpenProjectDataImplCopyWith<$Res>
    implements $OpenProjectDataCopyWith<$Res> {
  factory _$$OpenProjectDataImplCopyWith(_$OpenProjectDataImpl value,
          $Res Function(_$OpenProjectDataImpl) then) =
      __$$OpenProjectDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String projectId});
}

/// @nodoc
class __$$OpenProjectDataImplCopyWithImpl<$Res>
    extends _$OpenProjectDataCopyWithImpl<$Res, _$OpenProjectDataImpl>
    implements _$$OpenProjectDataImplCopyWith<$Res> {
  __$$OpenProjectDataImplCopyWithImpl(
      _$OpenProjectDataImpl _value, $Res Function(_$OpenProjectDataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? projectId = null,
  }) {
    return _then(_$OpenProjectDataImpl(
      projectId: null == projectId
          ? _value.projectId
          : projectId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OpenProjectDataImpl extends _OpenProjectData {
  const _$OpenProjectDataImpl({required this.projectId}) : super._();

  factory _$OpenProjectDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$OpenProjectDataImplFromJson(json);

  @override
  final String projectId;

  @override
  String toString() {
    return 'OpenProjectData(projectId: $projectId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OpenProjectDataImpl &&
            (identical(other.projectId, projectId) ||
                other.projectId == projectId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, projectId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$OpenProjectDataImplCopyWith<_$OpenProjectDataImpl> get copyWith =>
      __$$OpenProjectDataImplCopyWithImpl<_$OpenProjectDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OpenProjectDataImplToJson(
      this,
    );
  }
}

abstract class _OpenProjectData extends OpenProjectData {
  const factory _OpenProjectData({required final String projectId}) =
      _$OpenProjectDataImpl;
  const _OpenProjectData._() : super._();

  factory _OpenProjectData.fromJson(Map<String, dynamic> json) =
      _$OpenProjectDataImpl.fromJson;

  @override
  String get projectId;
  @override
  @JsonKey(ignore: true)
  _$$OpenProjectDataImplCopyWith<_$OpenProjectDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
