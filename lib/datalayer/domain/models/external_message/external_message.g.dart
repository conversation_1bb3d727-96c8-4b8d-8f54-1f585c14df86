// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'external_message.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ExternalMessageImpl _$$ExternalMessageImplFromJson(
        Map<String, dynamic> json) =>
    _$ExternalMessageImpl(
      type: $enumDecode(_$ExternalMessageTypeEnumMap, json['type']),
      data: json['data'] as Map<String, dynamic>,
      requestId: json['requestId'] as String?,
      timestamp: (json['timestamp'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$ExternalMessageImplToJson(
        _$ExternalMessageImpl instance) =>
    <String, dynamic>{
      'type': _$ExternalMessageTypeEnumMap[instance.type]!,
      'data': instance.data,
      'requestId': instance.requestId,
      'timestamp': instance.timestamp,
    };

const _$ExternalMessageTypeEnumMap = {
  ExternalMessageType.importProject: 'import_project',
  ExternalMessageType.smart: 'smart',
  ExternalMessageType.openProject: 'open_project',
  ExternalMessageType.exportProject: 'export_project',
  ExternalMessageType.unknown: 'unknown',
};

_$FileItemImpl _$$FileItemImplFromJson(Map<String, dynamic> json) =>
    _$FileItemImpl(
      originalPath: json['originalPath'] as String,
      historyId: json['historyId'] as String?,
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$$FileItemImplToJson(_$FileItemImpl instance) =>
    <String, dynamic>{
      'originalPath': instance.originalPath,
      'historyId': instance.historyId,
      'isSelected': instance.isSelected,
    };

_$ImportProjectDataImpl _$$ImportProjectDataImplFromJson(
        Map<String, dynamic> json) =>
    _$ImportProjectDataImpl(
      projectId: json['projectId'] as String?,
      projectName: json['projectName'] as String?,
      fileList: (json['fileList'] as List<dynamic>)
          .map((e) => FileItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      autoNavigate: json['autoNavigate'] as bool? ?? false,
      newProject: json['newProject'] as bool? ?? false,
    );

Map<String, dynamic> _$$ImportProjectDataImplToJson(
        _$ImportProjectDataImpl instance) =>
    <String, dynamic>{
      'projectId': instance.projectId,
      'projectName': instance.projectName,
      'fileList': instance.fileList,
      'autoNavigate': instance.autoNavigate,
      'newProject': instance.newProject,
    };

_$SmartDataImpl _$$SmartDataImplFromJson(Map<String, dynamic> json) =>
    _$SmartDataImpl(
      imagePaths: (json['imagePaths'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      projectName: json['projectName'] as String?,
      autoNavigate: json['autoNavigate'] as bool? ?? false,
      newProject: json['newProject'] as bool? ?? false,
    );

Map<String, dynamic> _$$SmartDataImplToJson(_$SmartDataImpl instance) =>
    <String, dynamic>{
      'imagePaths': instance.imagePaths,
      'projectName': instance.projectName,
      'autoNavigate': instance.autoNavigate,
      'newProject': instance.newProject,
    };

_$OpenProjectDataImpl _$$OpenProjectDataImplFromJson(
        Map<String, dynamic> json) =>
    _$OpenProjectDataImpl(
      projectId: json['projectId'] as String,
    );

Map<String, dynamic> _$$OpenProjectDataImplToJson(
        _$OpenProjectDataImpl instance) =>
    <String, dynamic>{
      'projectId': instance.projectId,
    };
