import 'package:flutter/foundation.dart';
import 'package:turing_art/datalayer/service/aigc_processors/processors/aigc_image_processor.dart';
import 'package:turing_art/datalayer/service/task_queue_system/task_processor_registry.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'aigc_interactive_mask_processor.dart';
import 'aigc_mask_processor.dart';
import 'aigc_raw_conversion_processor.dart';
import 'aigc_thumbnail_processor.dart';

/// AIGC任务处理器工厂
/// 负责初始化和注册所有AIGC处理器到通用注册表
class AigcProcessorFactory {
  /// 初始化并注册所有AIGC处理器到通用注册表
  static void initialize() {
    // 创建处理器实例并注册到通用注册表
    final processors = [
      AigcThumbnailProcessor(),
      AigcMaskProcessor(),
      AigcInteractiveMaskProcessor(),
      AigcImageProcessor(),
      AigcRawConversionProcessor(),
    ];

    for (final processor in processors) {
      TaskProcessorRegistry.register(processor);
    }

    PGLog.d('AIGC处理器工厂初始化完成，注册了 ${processors.length} 个处理器');
    TaskProcessorRegistry.debugInfo.forEach((key, value) {
      PGLog.d('处理器: $key, 类型: $value');
    });
  }
}
