import 'dart:io';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/config/ai_sdk_config.dart';
import 'package:turing_art/constants/image_constants.dart';
import 'package:turing_art/datalayer/repository/media_repository_impl.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/datalayer/service/task_queue_system/generic_task_processor.dart';
import 'package:turing_art/ffi/services/image_processor_service.dart';
import 'package:turing_art/ffi/services/raw_decoder_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// 缩略图输出路径集合
class ThumbnailPaths {
  final String highQualityPath; // ${ImageConstants.aigcHighQualitySize}px高清大图路径
  final String previewPath; // ${ImageConstants.previewSize}px预览图路径
  final String thumbnailPath; // 256px缩略图路径

  const ThumbnailPaths({
    required this.highQualityPath,
    required this.previewPath,
    required this.thumbnailPath,
  });

  Map<String, String> get outputPaths => {
        'highQualityPath': highQualityPath,
        'previewPath': previewPath,
        'thumbnailPath': thumbnailPath,
      };
}

/// AIGC缩略图生成任务处理器
class AigcThumbnailProcessor extends GenericTaskProcessor<AigcTaskMessage> {
  @override
  String get processorKey => 'thumbnail';

  @override
  Future<void> process(
    AigcTaskMessage message,
    SendPort sendToMain,
    String workerId,
  ) async {
    final startTime = DateTime.now();

    try {
      final fileName = path.basename(message.payload.inputPath);
      PGLog.d('🖼️ 工作器 $workerId 开始处理图片文件: $fileName');

      // 发送开始进度
      sendProgress(message, '开始处理图片文件', 0.0, sendToMain);

      // 检查输入文件是否存在
      if (!File(message.payload.inputPath).existsSync()) {
        throw Exception('输入文件不存在: ${message.payload.inputPath}');
      }

      // 检测文件类型并选择处理方式
      final output = await _processImageFile(message, sendToMain);

      // 发送成功结果
      sendSuccess(
          message,
          AigcThumbnailTaskResult(
            highQualityPath: output.highQualityPath,
            previewPath: output.previewPath,
            thumbnailPath: output.thumbnailPath,
          ),
          startTime,
          sendToMain);
    } catch (e) {
      sendError(message, e.toString(), startTime, sendToMain);
    }
  }

  /// 根据文件类型处理图片文件
  Future<ThumbnailPaths> _processImageFile(
    AigcTaskMessage message,
    SendPort sendToMain,
  ) async {
    final ext = message.payload.inputPath.split('.').last.toLowerCase();
    // 判断是否为普通图片文件（JPG/JPEG/PNG）
    if (ImageConstants.normalImageExtensions.contains(ext)) {
      return await _processNormalImage(message, sendToMain);
    } else {
      // 使用Raw解码器处理RAW文件
      return await _processRawFile(message, sendToMain);
    }
  }

  /// 生成多尺寸输出路径
  ThumbnailPaths _prepareMultiSizeOutputPaths(String outputPath) {
    final outputDir = path.dirname(outputPath);

    final highQualityPath =
        path.join(outputDir, MediaResourceConstants.getPsLargeFileName());
    final previewPath =
        path.join(outputDir, MediaResourceConstants.getPsPreviewFileName());
    final thumbnailPath =
        path.join(outputDir, MediaResourceConstants.getPsMinIconFileName());

    // 创建输出目录
    final outputDirectory = Directory(outputDir);
    if (!outputDirectory.existsSync()) {
      outputDirectory.createSync(recursive: true);
    }

    return ThumbnailPaths(
        highQualityPath: highQualityPath,
        previewPath: previewPath,
        thumbnailPath: thumbnailPath);
  }

  /// 验证输出文件是否成功生成
  void _validateOutputFile(String outputPath) {
    if (!File(outputPath).existsSync()) {
      throw Exception('输出文件生成失败: $outputPath');
    }
  }

  /// 基于预览图生成256px缩略图
  Future<void> _generateThumbnailFromPreview(
    String previewPath,
    String thumbnailPath,
    SendPort sendToMain,
    AigcTaskMessage message,
  ) async {
    sendProgress(message, '基于预览图生成缩略图(256px)', 0.7, sendToMain);

    try {
      // 使用原生图像处理器从预览图生成缩略图
      const thumbnailConfig = ImageProcessorConfig(
        targetWidth: ImageConstants.aigcThumbnailSize,
        targetHeight: ImageConstants.aigcThumbnailSize,
        maintainAspect: true,
        quality: 95,
        outputFormat: 'jpg',
        interpolation: ImageInterpolation.cubic,
      );

      await ImageProcessorService.processFile(
        previewPath,
        thumbnailPath,
        config: thumbnailConfig,
      );

      sendProgress(message, '缩略图生成完成', 0.9, sendToMain);
    } on ImageProcessorException catch (e) {
      throw Exception('缩略图生成失败: ${e.message} (错误码: ${e.errorCode})');
    } catch (e) {
      throw Exception('缩略图生成失败: $e');
    }
  }

  /// 处理普通图片文件（JPG/JPEG/PNG）- 使用原生图像处理器生成三尺寸图片
  Future<ThumbnailPaths> _processNormalImage(
    AigcTaskMessage message,
    SendPort sendToMain,
  ) async {
    sendProgress(message, '初始化图像处理器', 0.1, sendToMain);

    // 确保图像处理器已初始化
    await _ensureImageProcessorInitialized(sendToMain, message);

    // 生成多尺寸输出路径
    final outputPaths =
        _prepareMultiSizeOutputPaths(message.payload.outputPath);

    sendProgress(message, '开始处理图片', 0.2, sendToMain);

    try {
      // 第一步：生成${ImageConstants.aigcHighQualitySize}px高清大图
      sendProgress(message, '生成高清大图(${ImageConstants.aigcHighQualitySize}px)',
          0.25, sendToMain);
      const highQualityConfig = ImageProcessorConfig(
        targetWidth: ImageConstants.aigcHighQualitySize,
        targetHeight: ImageConstants.aigcHighQualitySize,
        maintainAspect: true,
        quality: 95,
        outputFormat: 'jpg',
        interpolation: ImageInterpolation.cubic,
      );

      await ImageProcessorService.processFile(
        message.payload.inputPath,
        outputPaths.highQualityPath,
        config: highQualityConfig,
      );

      sendProgress(message, '高清大图生成完成', 0.4, sendToMain);

      // 第二步：生成${ImageConstants.aigcPreviewSize}px预览图
      sendProgress(message, '生成预览图(${ImageConstants.aigcPreviewSize}px)', 0.45,
          sendToMain);
      const previewConfig = ImageProcessorConfig(
        targetWidth: ImageConstants.aigcPreviewSize,
        targetHeight: ImageConstants.aigcPreviewSize,
        maintainAspect: true,
        quality: 95,
        outputFormat: 'jpg',
        interpolation: ImageInterpolation.cubic,
      );

      await ImageProcessorService.processFile(
        outputPaths.highQualityPath,
        outputPaths.previewPath,
        config: previewConfig,
      );

      sendProgress(message, '预览图生成完成', 0.7, sendToMain);

      // 第三步：基于预览图生成256px缩略图
      await _generateThumbnailFromPreview(
        outputPaths.previewPath,
        outputPaths.thumbnailPath,
        sendToMain,
        message,
      );

      // 验证输出文件是否生成
      _validateOutputFile(outputPaths.highQualityPath);
      _validateOutputFile(outputPaths.previewPath);
      _validateOutputFile(outputPaths.thumbnailPath);

      sendProgress(message, '三尺寸缩略图生成完成', 1.0, sendToMain);

      // 返回所有尺寸路径
      return outputPaths;
    } on ImageProcessorException catch (e) {
      throw Exception('解码失败: ${e.message} (错误码: ${e.errorCode})');
    } catch (e) {
      throw Exception('文件处理失败: $e');
    }
  }

  /// 确保图像处理器已初始化
  Future<void> _ensureImageProcessorInitialized(
    SendPort sendToMain,
    AigcTaskMessage message,
  ) async {
    if (!ImageProcessorService.isInitialized) {
      sendProgress(message, '初始化图像处理器', 0.1, sendToMain);

      try {
        await ImageProcessorService.initialize();
        sendProgress(message, '图像处理器初始化完成', 0.15, sendToMain);
      } catch (e) {
        PGLog.d('图像处理器初始化失败，将使用Flutter回退: $e');
        // 不抛出异常，让调用方决定是否回退
      }
    } else {
      sendProgress(message, '图像处理器已就绪', 0.1, sendToMain);
    }
  }

  /// 确保Raw解码器已初始化
  Future<void> _ensureRawDecoderInitialized(
    SendPort sendToMain,
    AigcTaskMessage message,
  ) async {
    if (!RawDecoderService.isInitialized) {
      sendProgress(message, '初始化RAW解码器', 0.1, sendToMain);

      try {
        await RawDecoderService.initialize(
          key: AISDKDevConfig.activeConfig['sdkKey'],
        );
        sendProgress(message, 'RAW解码器初始化完成', 0.2, sendToMain);
      } catch (e) {
        throw Exception('RAW解码器初始化失败: $e');
      }
    } else {
      sendProgress(message, 'RAW解码器已就绪', 0.1, sendToMain);
    }
  }

  /// 使用Raw解码器处理文件 - 生成三尺寸图片
  Future<ThumbnailPaths> _processRawFile(
    AigcTaskMessage message,
    SendPort sendToMain,
  ) async {
    // 确保Raw解码器已初始化
    await _ensureRawDecoderInitialized(sendToMain, message);
    await _ensureImageProcessorInitialized(sendToMain, message);

    // 生成多尺寸输出路径
    final outputPaths =
        _prepareMultiSizeOutputPaths(message.payload.outputPath);

    sendProgress(message, '开始RAW解码', 0.2, sendToMain);

    try {
      // 第一步：生成${ImageConstants.aigcHighQualitySize}px RAW高清大图
      sendProgress(
          message,
          '生成RAW高清大图(${ImageConstants.aigcHighQualitySize}px)',
          0.25,
          sendToMain);
      const highQualityConfig = RawDecoderConfig(
        bitDepth: 8, // 8位深度适合显示
        maxDim: ImageConstants.aigcHighQualitySize, // 限制最大尺寸
        thumbnail: false, // 高质量模式
        denoise: false, // 启用降噪
        clarity: false, // 不启用锐化，保持处理速度
        exposure: 0.0, // 无曝光补偿
        outputFormat: 'jpg', // JPEG格式，文件更小
      );

      await RawDecoderService.processFile(
        message.payload.inputPath,
        outputPaths.highQualityPath,
        config: highQualityConfig,
      );

      sendProgress(message, 'RAW高清大图生成完成', 0.4, sendToMain);

      // 第二步：生成${ImageConstants.aigcPreviewSize}px RAW预览图
      sendProgress(message, '生成RAW预览图(${ImageConstants.aigcPreviewSize}px)',
          0.45, sendToMain);
      const previewConfig = ImageProcessorConfig(
        targetWidth: ImageConstants.aigcPreviewSize,
        targetHeight: ImageConstants.aigcPreviewSize,
        maintainAspect: true,
        quality: 95,
        outputFormat: 'jpg',
        interpolation: ImageInterpolation.cubic,
      );

      await ImageProcessorService.processFile(
        outputPaths.highQualityPath,
        outputPaths.previewPath,
        config: previewConfig,
      );

      sendProgress(message, 'RAW预览图生成完成', 0.7, sendToMain);

      // 第三步：基于RAW预览图生成256px缩略图
      await _generateThumbnailFromPreview(
        outputPaths.previewPath,
        outputPaths.thumbnailPath,
        sendToMain,
        message,
      );

      // 验证输出文件是否生成
      _validateOutputFile(outputPaths.highQualityPath);
      _validateOutputFile(outputPaths.previewPath);
      _validateOutputFile(outputPaths.thumbnailPath);

      sendProgress(message, 'RAW三尺寸缩略图生成完成', 1.0, sendToMain);

      // 返回所有尺寸路径
      return outputPaths;
    } on RawDecoderException catch (e) {
      throw Exception('RAW解码失败: ${e.message} (错误码: ${e.errorCode})');
    } catch (e) {
      throw Exception('文件处理失败: $e');
    }
  }
}
