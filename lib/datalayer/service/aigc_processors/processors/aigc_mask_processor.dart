import 'dart:io';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/datalayer/service/task_queue_system/generic_task_processor.dart';
import 'package:turing_art/ffi/models/salient_matters_model.dart';
import 'package:turing_art/ffi/services/salient_matters_service.dart';
import 'package:turing_art/utils/image_save_helper.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC遮罩生成任务处理器
class AigcMaskProcessor extends GenericTaskProcessor<AigcTaskMessage> {
  @override
  String get processorKey => 'mask';

  @override
  Future<void> process(
    AigcTaskMessage message,
    SendPort sendToMain,
    String workerId,
  ) async {
    final startTime = DateTime.now();

    try {
      PGLog.d(
          '🎭 工作器 $workerId 开始生成遮罩: ${path.basename(message.payload.inputPath)}');

      // 发送开始进度
      sendProgress(message, '开始生成遮罩', 0.0, sendToMain);

      // 创建遮罩文件
      final outputPath = await _createMask(
        message.payload.inputPath,
        customOutputPath: message.payload.outputPath,
      );

      // 发送完成进度
      sendProgress(message, '生成遮罩', 1.0, sendToMain);

      // 发送成功结果
      sendSuccess(message, AigcMaskTaskResult(outputPath: outputPath),
          startTime, sendToMain);
    } catch (e) {
      sendError(message, e.toString(), startTime, sendToMain);
    } finally {
      // 释放SalientMatters服务资源
      // SalientMattersService.dispose();
    }
  }

  /// 创建遮罩文件
  Future<String> _createMask(String inputPath,
      {String? customOutputPath}) async {
    try {
      // 处理输出路径
      String outputDir;

      if (customOutputPath != null && customOutputPath.isNotEmpty) {
        outputDir = path.dirname(customOutputPath);
      } else {
        throw Exception('输出路径不能为空');
      }

      // 创建输出目录
      final outputDirectory = Directory(outputDir);
      if (!outputDirectory.existsSync()) {
        outputDirectory.createSync(recursive: true);
      }

      PGLog.d('开始对图片进行主体抠图: $inputPath');

      // 1. 初始化SalientMatters服务
      final initResult = await SalientMattersService.initializeFromConfig();
      if (!initResult.isSuccess) {
        throw Exception('SalientMatters初始化失败: ${initResult.errorMessage}');
      }

      // 2. 从文件加载图像数据（BGR格式）
      final imageData = await ImageSaveHelper.loadImageFromFile(inputPath);
      if (imageData == null) {
        throw Exception('无法加载输入图像: $inputPath');
      }

      PGLog.d(
          '成功加载图像: ${imageData.width}x${imageData.height}, 通道数: ${imageData.channels}');

      // 3. 验证图像数据
      if (!SalientMattersService.validateImageData(imageData)) {
        throw Exception('图像数据验证失败');
      }

      // 4. 执行主体抠图
      PGLog.d('开始执行主体抠图...');
      final mattingResult = await SalientMattersService.quickMatting(imageData);

      if (!mattingResult.isSuccess) {
        throw Exception('主体抠图失败: ${mattingResult.errorMessage}');
      }

      if (mattingResult.imageData == null) {
        throw Exception('抠图结果为空');
      }

      // 将结果数据转换为ImageData对象（结果是4通道遮罩）
      final maskImageData = ImageData(
        data: mattingResult.imageData!,
        width: imageData.width,
        height: imageData.height,
        channels: 4,
      );

      PGLog.d('主体抠图完成，遮罩尺寸: ${maskImageData.width}x${maskImageData.height}');

      // 5. 保存遮罩结果为PNG文件
      final savedPath =
          await ImageSaveHelper.saveAsPng(maskImageData, customOutputPath);

      if (savedPath == null) {
        throw Exception('保存遮罩文件失败');
      }

      PGLog.d('遮罩文件已保存到: $savedPath');
      return savedPath;
    } catch (e) {
      PGLog.e('创建遮罩失败: $e');
      throw Exception('创建遮罩失败: $e');
    }
  }
}
