import 'dart:io';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_message.dart';
import 'package:turing_art/datalayer/service/aigc_processors/models/aigc_task.dart';
import 'package:turing_art/datalayer/service/task_queue_system/generic_task_processor.dart';
import 'package:turing_art/ffi/services/raw_conversion_service.dart';
import 'package:turing_art/utils/pg_log.dart';

/// AIGC智能调色任务处理器
class AigcRawConversionProcessor extends GenericTaskProcessor<AigcTaskMessage> {
  @override
  String get processorKey => 'rawConversion';

  @override
  Future<void> process(
    AigcTaskMessage message,
    SendPort sendToMain,
    String workerId,
  ) async {
    final startTime = DateTime.now();

    try {
      final fileName = path.basename(message.payload.inputPath);
      PGLog.d('🎨 工作器 $workerId 开始智能调色处理: $fileName');

      // 发送开始进度
      sendProgress(message, '开始智能调色处理', 0.0, sendToMain);

      // 检查输入文件是否存在
      if (!File(message.payload.inputPath).existsSync()) {
        throw Exception('输入文件不存在: ${message.payload.inputPath}');
      }

      // 检查文件格式是否支持
      if (!RawConversionService.isSupportedFormat(message.payload.inputPath)) {
        throw Exception(
            '不支持的文件格式: ${path.extension(message.payload.inputPath)}');
      }

      // 处理智能调色
      final outputPath = await _processRawConversion(
        message.payload.inputPath,
        message.payload.outputPath,
      );

      // 发送完成进度
      sendProgress(message, '智能调色处理完成', 1.0, sendToMain);

      // 发送成功结果
      sendSuccess(
        message,
        AigcRawConversionTaskResult(
          outputPath: outputPath,
        ),
        startTime,
        sendToMain,
      );
    } catch (e) {
      sendError(message, e.toString(), startTime, sendToMain);
    }
  }

  /// 执行智能调色处理
  Future<String> _processRawConversion(
    String inputPath,
    String outputPath,
  ) async {
    try {
      final initResult = await RawConversionService.initializeFromConfig();
      if (!initResult.isSuccess) {
        throw Exception('RawConversion服务初始化失败: ${initResult.errorMessage}');
      }

      // 创建输出目录
      final outputFile = File(outputPath);
      final outputDir = outputFile.parent;
      if (!outputDir.existsSync()) {
        await outputDir.create(recursive: true);
      }

      // 调用RawConversionService处理文件
      // 使用默认配置，如配置文档要求
      final result = await RawConversionService.processFile(
        inputPath,
        outputPath,
        config: RawConversionService.getDefaultConfig(),
        adjustCustom: true,
      );

      // 检查处理结果
      if (!result.isSuccess) {
        throw Exception('智能调色处理失败: ${result.errorMessage}');
      }

      // 验证输出文件是否生成
      if (!File(outputPath).existsSync()) {
        throw Exception('输出文件生成失败: $outputPath');
      }

      PGLog.d('✅ 智能调色处理完成: $outputPath');
      return outputPath;
    } catch (e) {
      throw Exception('智能调色处理失败: $e');
    }
  }
}
