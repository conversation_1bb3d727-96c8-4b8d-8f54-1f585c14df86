import 'package:drift/drift.dart';

import '../database.dart';

extension ExportTokenDao on DataBase {
  // 查询所有Token
  Future<List<ExportTokenEntityData>> getAllExportTokens() async {
    final tokens = await select(exportTokenEntity).get();
    return tokens;
  }

  // 根据用户ID、图片哈希值和导出类型获取Token
  Future<ExportTokenEntityData?> getExportToken(
      String userId, String imagePHash, String exportType) async {
    final result = await (select(exportTokenEntity)
          ..where((t) =>
              t.userId.equals(userId) &
              t.imagePHash.equals(imagePHash) &
              t.exportType.equals(exportType)))
        .getSingleOrNull();
    return result;
  }

  // 获取所有未过期的Token
  Future<List<ExportTokenEntityData>> getValidExportTokens() async {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000; // 当前时间（秒）
    final tokens = await (select(exportTokenEntity)
          ..where((t) => t.expireAt.isBiggerThan(Variable(now))))
        .get();
    return tokens;
  }

  // 删除Token
  Future<int> deleteExportToken(
      String userId, String imagePHash, String exportType) {
    return (delete(exportTokenEntity)
          ..where((t) =>
              t.userId.equals(userId) &
              t.imagePHash.equals(imagePHash) &
              t.exportType.equals(exportType)))
        .go();
  }

  // 删除所有过期的Token
  Future<int> deleteExpiredExportTokens() {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000; // 当前时间（秒）
    return (delete(exportTokenEntity)
          ..where((t) => t.expireAt.isSmallerOrEqual(Variable(now))))
        .go();
  }

  // 保存或更新Token
  Future<void> upsertExportToken(ExportTokenEntityCompanion entity) async {
    await into(exportTokenEntity).insertOnConflictUpdate(entity);
  }

  // 从API响应中保存Token
  Future<void> saveExportToken(
    String userId,
    String imagePHash,
    String imageName,
    String tokenId,
    String key,
    int expireAt,
    String exportType,
  ) async {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000; // 当前时间（秒）
    final entity = ExportTokenEntityCompanion(
      userId: Value(userId),
      imagePHash: Value(imagePHash),
      tokenId: Value(tokenId),
      key: Value(key),
      expireAt: Value(expireAt),
      imageName: Value(imageName),
      createTime: Value(now),
      exportType: Value(exportType),
    );
    await upsertExportToken(entity);
  }

  // 批量保存Token
  Future<void> saveExportTokens(
    List<Map<String, dynamic>> imageTokenPairs,
  ) async {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000; // 当前时间（秒）

    for (final pair in imageTokenPairs) {
      final entity = ExportTokenEntityCompanion(
        userId: Value(pair['userId'] as String),
        imagePHash: Value(pair['imagePHash'] as String),
        tokenId: Value(pair['tokenId'] as String),
        key: Value(pair['key'] as String),
        expireAt: Value(pair['expireAt'] as int),
        imageName: Value(pair['imageName'] as String),
        createTime: Value(now),
        exportType: Value(pair['exportType'] as String),
      );

      await upsertExportToken(entity);
    }
  }

  // 删除用户的所有Token
  Future<int> deleteUserExportTokens(String userId) {
    return (delete(exportTokenEntity)..where((t) => t.userId.equals(userId)))
        .go();
  }
}
