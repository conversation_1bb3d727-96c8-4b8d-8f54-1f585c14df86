// 定义导出Token表
import 'package:drift/drift.dart';

class ExportTokenEntity extends Table {
  TextColumn get imagePHash => text()(); // 图片哈希值
  TextColumn get userId => text()(); // 用户ID，新增字段
  TextColumn get tokenId => text()(); // Token ID
  TextColumn get key => text()(); // Token密钥
  IntColumn get expireAt => integer()(); // Token过期时间（Unix时间戳，秒）
  TextColumn get imageName => text().nullable()(); // 图片名称（可选）
  IntColumn get createTime => integer()(); // Token创建时间（Unix时间戳，秒）
  TextColumn get exportType => text()(); // 导出类型

  // 修改唯一索引为userId和imagePHash的组合
  @override
  Set<Column> get primaryKey => {userId, imagePHash, exportType};
}
