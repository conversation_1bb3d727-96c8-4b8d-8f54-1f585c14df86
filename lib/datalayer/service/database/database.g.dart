// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $ProjectEntityTable extends ProjectEntity
    with TableInfo<$ProjectEntityTable, ProjectEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ProjectEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _nameMeta = const VerificationMeta('name');
  @override
  late final GeneratedColumn<String> name = GeneratedColumn<String>(
      'name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _projectIdMeta =
      const VerificationMeta('projectId');
  @override
  late final GeneratedColumn<String> projectId = GeneratedColumn<String>(
      'project_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _versionMeta =
      const VerificationMeta('version');
  @override
  late final GeneratedColumn<String> version = GeneratedColumn<String>(
      'version', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _authorMeta = const VerificationMeta('author');
  @override
  late final GeneratedColumn<String> author = GeneratedColumn<String>(
      'author', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _coverImagesMeta =
      const VerificationMeta('coverImages');
  @override
  late final GeneratedColumn<String> coverImages = GeneratedColumn<String>(
      'cover_images', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdDateMeta =
      const VerificationMeta('createdDate');
  @override
  late final GeneratedColumn<DateTime> createdDate = GeneratedColumn<DateTime>(
      'created_date', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _updateDateMeta =
      const VerificationMeta('updateDate');
  @override
  late final GeneratedColumn<DateTime> updateDate = GeneratedColumn<DateTime>(
      'update_date', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _projectTypeMeta =
      const VerificationMeta('projectType');
  @override
  late final GeneratedColumn<int> projectType = GeneratedColumn<int>(
      'project_type', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _outputFolderMeta =
      const VerificationMeta('outputFolder');
  @override
  late final GeneratedColumn<String> outputFolder = GeneratedColumn<String>(
      'output_folder', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant(''));
  static const VerificationMeta _exportFileTypeMeta =
      const VerificationMeta('exportFileType');
  @override
  late final GeneratedColumn<int> exportFileType = GeneratedColumn<int>(
      'export_file_type', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _qualityMeta =
      const VerificationMeta('quality');
  @override
  late final GeneratedColumn<int> quality = GeneratedColumn<int>(
      'quality', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(100));
  static const VerificationMeta _isReplaceMeta =
      const VerificationMeta('isReplace');
  @override
  late final GeneratedColumn<bool> isReplace = GeneratedColumn<bool>(
      'is_replace', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_replace" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _transferSRGBMeta =
      const VerificationMeta('transferSRGB');
  @override
  late final GeneratedColumn<bool> transferSRGB = GeneratedColumn<bool>(
      'transfer_s_r_g_b', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("transfer_s_r_g_b" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _workspaceVersionMeta =
      const VerificationMeta('workspaceVersion');
  @override
  late final GeneratedColumn<int> workspaceVersion = GeneratedColumn<int>(
      'workspace_version', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(1));
  @override
  List<GeneratedColumn> get $columns => [
        name,
        projectId,
        version,
        author,
        coverImages,
        description,
        createdDate,
        updateDate,
        projectType,
        outputFolder,
        exportFileType,
        quality,
        isReplace,
        transferSRGB,
        workspaceVersion
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'project_entity';
  @override
  VerificationContext validateIntegrity(Insertable<ProjectEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('name')) {
      context.handle(
          _nameMeta, name.isAcceptableOrUnknown(data['name']!, _nameMeta));
    } else if (isInserting) {
      context.missing(_nameMeta);
    }
    if (data.containsKey('project_id')) {
      context.handle(_projectIdMeta,
          projectId.isAcceptableOrUnknown(data['project_id']!, _projectIdMeta));
    } else if (isInserting) {
      context.missing(_projectIdMeta);
    }
    if (data.containsKey('version')) {
      context.handle(_versionMeta,
          version.isAcceptableOrUnknown(data['version']!, _versionMeta));
    } else if (isInserting) {
      context.missing(_versionMeta);
    }
    if (data.containsKey('author')) {
      context.handle(_authorMeta,
          author.isAcceptableOrUnknown(data['author']!, _authorMeta));
    } else if (isInserting) {
      context.missing(_authorMeta);
    }
    if (data.containsKey('cover_images')) {
      context.handle(
          _coverImagesMeta,
          coverImages.isAcceptableOrUnknown(
              data['cover_images']!, _coverImagesMeta));
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('created_date')) {
      context.handle(
          _createdDateMeta,
          createdDate.isAcceptableOrUnknown(
              data['created_date']!, _createdDateMeta));
    }
    if (data.containsKey('update_date')) {
      context.handle(
          _updateDateMeta,
          updateDate.isAcceptableOrUnknown(
              data['update_date']!, _updateDateMeta));
    }
    if (data.containsKey('project_type')) {
      context.handle(
          _projectTypeMeta,
          projectType.isAcceptableOrUnknown(
              data['project_type']!, _projectTypeMeta));
    }
    if (data.containsKey('output_folder')) {
      context.handle(
          _outputFolderMeta,
          outputFolder.isAcceptableOrUnknown(
              data['output_folder']!, _outputFolderMeta));
    }
    if (data.containsKey('export_file_type')) {
      context.handle(
          _exportFileTypeMeta,
          exportFileType.isAcceptableOrUnknown(
              data['export_file_type']!, _exportFileTypeMeta));
    }
    if (data.containsKey('quality')) {
      context.handle(_qualityMeta,
          quality.isAcceptableOrUnknown(data['quality']!, _qualityMeta));
    }
    if (data.containsKey('is_replace')) {
      context.handle(_isReplaceMeta,
          isReplace.isAcceptableOrUnknown(data['is_replace']!, _isReplaceMeta));
    }
    if (data.containsKey('transfer_s_r_g_b')) {
      context.handle(
          _transferSRGBMeta,
          transferSRGB.isAcceptableOrUnknown(
              data['transfer_s_r_g_b']!, _transferSRGBMeta));
    }
    if (data.containsKey('workspace_version')) {
      context.handle(
          _workspaceVersionMeta,
          workspaceVersion.isAcceptableOrUnknown(
              data['workspace_version']!, _workspaceVersionMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {projectId};
  @override
  ProjectEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ProjectEntityData(
      name: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}name'])!,
      projectId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}project_id'])!,
      version: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}version'])!,
      author: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}author'])!,
      coverImages: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}cover_images']),
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      createdDate: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_date']),
      updateDate: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}update_date']),
      projectType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}project_type'])!,
      outputFolder: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}output_folder'])!,
      exportFileType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}export_file_type'])!,
      quality: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}quality'])!,
      isReplace: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_replace'])!,
      transferSRGB: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}transfer_s_r_g_b'])!,
      workspaceVersion: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}workspace_version']),
    );
  }

  @override
  $ProjectEntityTable createAlias(String alias) {
    return $ProjectEntityTable(attachedDatabase, alias);
  }
}

class ProjectEntityData extends DataClass
    implements Insertable<ProjectEntityData> {
  final String name;
  final String projectId;
  final String version;
  final String author;
  final String? coverImages;
  final String? description;
  final DateTime? createdDate;
  final DateTime? updateDate;
  final int projectType;
  final String outputFolder;
  final int exportFileType;
  final int quality;
  final bool isReplace;
  final bool transferSRGB;
  final int? workspaceVersion;
  const ProjectEntityData(
      {required this.name,
      required this.projectId,
      required this.version,
      required this.author,
      this.coverImages,
      this.description,
      this.createdDate,
      this.updateDate,
      required this.projectType,
      required this.outputFolder,
      required this.exportFileType,
      required this.quality,
      required this.isReplace,
      required this.transferSRGB,
      this.workspaceVersion});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['name'] = Variable<String>(name);
    map['project_id'] = Variable<String>(projectId);
    map['version'] = Variable<String>(version);
    map['author'] = Variable<String>(author);
    if (!nullToAbsent || coverImages != null) {
      map['cover_images'] = Variable<String>(coverImages);
    }
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    if (!nullToAbsent || createdDate != null) {
      map['created_date'] = Variable<DateTime>(createdDate);
    }
    if (!nullToAbsent || updateDate != null) {
      map['update_date'] = Variable<DateTime>(updateDate);
    }
    map['project_type'] = Variable<int>(projectType);
    map['output_folder'] = Variable<String>(outputFolder);
    map['export_file_type'] = Variable<int>(exportFileType);
    map['quality'] = Variable<int>(quality);
    map['is_replace'] = Variable<bool>(isReplace);
    map['transfer_s_r_g_b'] = Variable<bool>(transferSRGB);
    if (!nullToAbsent || workspaceVersion != null) {
      map['workspace_version'] = Variable<int>(workspaceVersion);
    }
    return map;
  }

  ProjectEntityCompanion toCompanion(bool nullToAbsent) {
    return ProjectEntityCompanion(
      name: Value(name),
      projectId: Value(projectId),
      version: Value(version),
      author: Value(author),
      coverImages: coverImages == null && nullToAbsent
          ? const Value.absent()
          : Value(coverImages),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      createdDate: createdDate == null && nullToAbsent
          ? const Value.absent()
          : Value(createdDate),
      updateDate: updateDate == null && nullToAbsent
          ? const Value.absent()
          : Value(updateDate),
      projectType: Value(projectType),
      outputFolder: Value(outputFolder),
      exportFileType: Value(exportFileType),
      quality: Value(quality),
      isReplace: Value(isReplace),
      transferSRGB: Value(transferSRGB),
      workspaceVersion: workspaceVersion == null && nullToAbsent
          ? const Value.absent()
          : Value(workspaceVersion),
    );
  }

  factory ProjectEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ProjectEntityData(
      name: serializer.fromJson<String>(json['name']),
      projectId: serializer.fromJson<String>(json['projectId']),
      version: serializer.fromJson<String>(json['version']),
      author: serializer.fromJson<String>(json['author']),
      coverImages: serializer.fromJson<String?>(json['coverImages']),
      description: serializer.fromJson<String?>(json['description']),
      createdDate: serializer.fromJson<DateTime?>(json['createdDate']),
      updateDate: serializer.fromJson<DateTime?>(json['updateDate']),
      projectType: serializer.fromJson<int>(json['projectType']),
      outputFolder: serializer.fromJson<String>(json['outputFolder']),
      exportFileType: serializer.fromJson<int>(json['exportFileType']),
      quality: serializer.fromJson<int>(json['quality']),
      isReplace: serializer.fromJson<bool>(json['isReplace']),
      transferSRGB: serializer.fromJson<bool>(json['transferSRGB']),
      workspaceVersion: serializer.fromJson<int?>(json['workspaceVersion']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'name': serializer.toJson<String>(name),
      'projectId': serializer.toJson<String>(projectId),
      'version': serializer.toJson<String>(version),
      'author': serializer.toJson<String>(author),
      'coverImages': serializer.toJson<String?>(coverImages),
      'description': serializer.toJson<String?>(description),
      'createdDate': serializer.toJson<DateTime?>(createdDate),
      'updateDate': serializer.toJson<DateTime?>(updateDate),
      'projectType': serializer.toJson<int>(projectType),
      'outputFolder': serializer.toJson<String>(outputFolder),
      'exportFileType': serializer.toJson<int>(exportFileType),
      'quality': serializer.toJson<int>(quality),
      'isReplace': serializer.toJson<bool>(isReplace),
      'transferSRGB': serializer.toJson<bool>(transferSRGB),
      'workspaceVersion': serializer.toJson<int?>(workspaceVersion),
    };
  }

  ProjectEntityData copyWith(
          {String? name,
          String? projectId,
          String? version,
          String? author,
          Value<String?> coverImages = const Value.absent(),
          Value<String?> description = const Value.absent(),
          Value<DateTime?> createdDate = const Value.absent(),
          Value<DateTime?> updateDate = const Value.absent(),
          int? projectType,
          String? outputFolder,
          int? exportFileType,
          int? quality,
          bool? isReplace,
          bool? transferSRGB,
          Value<int?> workspaceVersion = const Value.absent()}) =>
      ProjectEntityData(
        name: name ?? this.name,
        projectId: projectId ?? this.projectId,
        version: version ?? this.version,
        author: author ?? this.author,
        coverImages: coverImages.present ? coverImages.value : this.coverImages,
        description: description.present ? description.value : this.description,
        createdDate: createdDate.present ? createdDate.value : this.createdDate,
        updateDate: updateDate.present ? updateDate.value : this.updateDate,
        projectType: projectType ?? this.projectType,
        outputFolder: outputFolder ?? this.outputFolder,
        exportFileType: exportFileType ?? this.exportFileType,
        quality: quality ?? this.quality,
        isReplace: isReplace ?? this.isReplace,
        transferSRGB: transferSRGB ?? this.transferSRGB,
        workspaceVersion: workspaceVersion.present
            ? workspaceVersion.value
            : this.workspaceVersion,
      );
  ProjectEntityData copyWithCompanion(ProjectEntityCompanion data) {
    return ProjectEntityData(
      name: data.name.present ? data.name.value : this.name,
      projectId: data.projectId.present ? data.projectId.value : this.projectId,
      version: data.version.present ? data.version.value : this.version,
      author: data.author.present ? data.author.value : this.author,
      coverImages:
          data.coverImages.present ? data.coverImages.value : this.coverImages,
      description:
          data.description.present ? data.description.value : this.description,
      createdDate:
          data.createdDate.present ? data.createdDate.value : this.createdDate,
      updateDate:
          data.updateDate.present ? data.updateDate.value : this.updateDate,
      projectType:
          data.projectType.present ? data.projectType.value : this.projectType,
      outputFolder: data.outputFolder.present
          ? data.outputFolder.value
          : this.outputFolder,
      exportFileType: data.exportFileType.present
          ? data.exportFileType.value
          : this.exportFileType,
      quality: data.quality.present ? data.quality.value : this.quality,
      isReplace: data.isReplace.present ? data.isReplace.value : this.isReplace,
      transferSRGB: data.transferSRGB.present
          ? data.transferSRGB.value
          : this.transferSRGB,
      workspaceVersion: data.workspaceVersion.present
          ? data.workspaceVersion.value
          : this.workspaceVersion,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ProjectEntityData(')
          ..write('name: $name, ')
          ..write('projectId: $projectId, ')
          ..write('version: $version, ')
          ..write('author: $author, ')
          ..write('coverImages: $coverImages, ')
          ..write('description: $description, ')
          ..write('createdDate: $createdDate, ')
          ..write('updateDate: $updateDate, ')
          ..write('projectType: $projectType, ')
          ..write('outputFolder: $outputFolder, ')
          ..write('exportFileType: $exportFileType, ')
          ..write('quality: $quality, ')
          ..write('isReplace: $isReplace, ')
          ..write('transferSRGB: $transferSRGB, ')
          ..write('workspaceVersion: $workspaceVersion')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      name,
      projectId,
      version,
      author,
      coverImages,
      description,
      createdDate,
      updateDate,
      projectType,
      outputFolder,
      exportFileType,
      quality,
      isReplace,
      transferSRGB,
      workspaceVersion);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ProjectEntityData &&
          other.name == this.name &&
          other.projectId == this.projectId &&
          other.version == this.version &&
          other.author == this.author &&
          other.coverImages == this.coverImages &&
          other.description == this.description &&
          other.createdDate == this.createdDate &&
          other.updateDate == this.updateDate &&
          other.projectType == this.projectType &&
          other.outputFolder == this.outputFolder &&
          other.exportFileType == this.exportFileType &&
          other.quality == this.quality &&
          other.isReplace == this.isReplace &&
          other.transferSRGB == this.transferSRGB &&
          other.workspaceVersion == this.workspaceVersion);
}

class ProjectEntityCompanion extends UpdateCompanion<ProjectEntityData> {
  final Value<String> name;
  final Value<String> projectId;
  final Value<String> version;
  final Value<String> author;
  final Value<String?> coverImages;
  final Value<String?> description;
  final Value<DateTime?> createdDate;
  final Value<DateTime?> updateDate;
  final Value<int> projectType;
  final Value<String> outputFolder;
  final Value<int> exportFileType;
  final Value<int> quality;
  final Value<bool> isReplace;
  final Value<bool> transferSRGB;
  final Value<int?> workspaceVersion;
  final Value<int> rowid;
  const ProjectEntityCompanion({
    this.name = const Value.absent(),
    this.projectId = const Value.absent(),
    this.version = const Value.absent(),
    this.author = const Value.absent(),
    this.coverImages = const Value.absent(),
    this.description = const Value.absent(),
    this.createdDate = const Value.absent(),
    this.updateDate = const Value.absent(),
    this.projectType = const Value.absent(),
    this.outputFolder = const Value.absent(),
    this.exportFileType = const Value.absent(),
    this.quality = const Value.absent(),
    this.isReplace = const Value.absent(),
    this.transferSRGB = const Value.absent(),
    this.workspaceVersion = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ProjectEntityCompanion.insert({
    required String name,
    required String projectId,
    required String version,
    required String author,
    this.coverImages = const Value.absent(),
    this.description = const Value.absent(),
    this.createdDate = const Value.absent(),
    this.updateDate = const Value.absent(),
    this.projectType = const Value.absent(),
    this.outputFolder = const Value.absent(),
    this.exportFileType = const Value.absent(),
    this.quality = const Value.absent(),
    this.isReplace = const Value.absent(),
    this.transferSRGB = const Value.absent(),
    this.workspaceVersion = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : name = Value(name),
        projectId = Value(projectId),
        version = Value(version),
        author = Value(author);
  static Insertable<ProjectEntityData> custom({
    Expression<String>? name,
    Expression<String>? projectId,
    Expression<String>? version,
    Expression<String>? author,
    Expression<String>? coverImages,
    Expression<String>? description,
    Expression<DateTime>? createdDate,
    Expression<DateTime>? updateDate,
    Expression<int>? projectType,
    Expression<String>? outputFolder,
    Expression<int>? exportFileType,
    Expression<int>? quality,
    Expression<bool>? isReplace,
    Expression<bool>? transferSRGB,
    Expression<int>? workspaceVersion,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (name != null) 'name': name,
      if (projectId != null) 'project_id': projectId,
      if (version != null) 'version': version,
      if (author != null) 'author': author,
      if (coverImages != null) 'cover_images': coverImages,
      if (description != null) 'description': description,
      if (createdDate != null) 'created_date': createdDate,
      if (updateDate != null) 'update_date': updateDate,
      if (projectType != null) 'project_type': projectType,
      if (outputFolder != null) 'output_folder': outputFolder,
      if (exportFileType != null) 'export_file_type': exportFileType,
      if (quality != null) 'quality': quality,
      if (isReplace != null) 'is_replace': isReplace,
      if (transferSRGB != null) 'transfer_s_r_g_b': transferSRGB,
      if (workspaceVersion != null) 'workspace_version': workspaceVersion,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ProjectEntityCompanion copyWith(
      {Value<String>? name,
      Value<String>? projectId,
      Value<String>? version,
      Value<String>? author,
      Value<String?>? coverImages,
      Value<String?>? description,
      Value<DateTime?>? createdDate,
      Value<DateTime?>? updateDate,
      Value<int>? projectType,
      Value<String>? outputFolder,
      Value<int>? exportFileType,
      Value<int>? quality,
      Value<bool>? isReplace,
      Value<bool>? transferSRGB,
      Value<int?>? workspaceVersion,
      Value<int>? rowid}) {
    return ProjectEntityCompanion(
      name: name ?? this.name,
      projectId: projectId ?? this.projectId,
      version: version ?? this.version,
      author: author ?? this.author,
      coverImages: coverImages ?? this.coverImages,
      description: description ?? this.description,
      createdDate: createdDate ?? this.createdDate,
      updateDate: updateDate ?? this.updateDate,
      projectType: projectType ?? this.projectType,
      outputFolder: outputFolder ?? this.outputFolder,
      exportFileType: exportFileType ?? this.exportFileType,
      quality: quality ?? this.quality,
      isReplace: isReplace ?? this.isReplace,
      transferSRGB: transferSRGB ?? this.transferSRGB,
      workspaceVersion: workspaceVersion ?? this.workspaceVersion,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (name.present) {
      map['name'] = Variable<String>(name.value);
    }
    if (projectId.present) {
      map['project_id'] = Variable<String>(projectId.value);
    }
    if (version.present) {
      map['version'] = Variable<String>(version.value);
    }
    if (author.present) {
      map['author'] = Variable<String>(author.value);
    }
    if (coverImages.present) {
      map['cover_images'] = Variable<String>(coverImages.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (createdDate.present) {
      map['created_date'] = Variable<DateTime>(createdDate.value);
    }
    if (updateDate.present) {
      map['update_date'] = Variable<DateTime>(updateDate.value);
    }
    if (projectType.present) {
      map['project_type'] = Variable<int>(projectType.value);
    }
    if (outputFolder.present) {
      map['output_folder'] = Variable<String>(outputFolder.value);
    }
    if (exportFileType.present) {
      map['export_file_type'] = Variable<int>(exportFileType.value);
    }
    if (quality.present) {
      map['quality'] = Variable<int>(quality.value);
    }
    if (isReplace.present) {
      map['is_replace'] = Variable<bool>(isReplace.value);
    }
    if (transferSRGB.present) {
      map['transfer_s_r_g_b'] = Variable<bool>(transferSRGB.value);
    }
    if (workspaceVersion.present) {
      map['workspace_version'] = Variable<int>(workspaceVersion.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ProjectEntityCompanion(')
          ..write('name: $name, ')
          ..write('projectId: $projectId, ')
          ..write('version: $version, ')
          ..write('author: $author, ')
          ..write('coverImages: $coverImages, ')
          ..write('description: $description, ')
          ..write('createdDate: $createdDate, ')
          ..write('updateDate: $updateDate, ')
          ..write('projectType: $projectType, ')
          ..write('outputFolder: $outputFolder, ')
          ..write('exportFileType: $exportFileType, ')
          ..write('quality: $quality, ')
          ..write('isReplace: $isReplace, ')
          ..write('transferSRGB: $transferSRGB, ')
          ..write('workspaceVersion: $workspaceVersion, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $UserEntityTable extends UserEntity
    with TableInfo<$UserEntityTable, UserEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UserEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _usernameMeta =
      const VerificationMeta('username');
  @override
  late final GeneratedColumn<String> username = GeneratedColumn<String>(
      'username', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _uidMeta = const VerificationMeta('uid');
  @override
  late final GeneratedColumn<String> uid = GeneratedColumn<String>(
      'uid', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _phoneNumberMeta =
      const VerificationMeta('phoneNumber');
  @override
  late final GeneratedColumn<String> phoneNumber = GeneratedColumn<String>(
      'phone_number', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _tokenMeta = const VerificationMeta('token');
  @override
  late final GeneratedColumn<String> token = GeneratedColumn<String>(
      'token', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _tokenExpiredDateMeta =
      const VerificationMeta('tokenExpiredDate');
  @override
  late final GeneratedColumn<int> tokenExpiredDate = GeneratedColumn<int>(
      'token_expired_date', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _tokenEndMeta =
      const VerificationMeta('tokenEnd');
  @override
  late final GeneratedColumn<String> tokenEnd = GeneratedColumn<String>(
      'token_end', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _firstLoginMeta =
      const VerificationMeta('firstLogin');
  @override
  late final GeneratedColumn<int> firstLogin = GeneratedColumn<int>(
      'first_login', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _lastLoginTimeMeta =
      const VerificationMeta('lastLoginTime');
  @override
  late final GeneratedColumn<String> lastLoginTime = GeneratedColumn<String>(
      'last_login_time', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant(''));
  static const VerificationMeta _regDateTimeMeta =
      const VerificationMeta('regDateTime');
  @override
  late final GeneratedColumn<String> regDateTime = GeneratedColumn<String>(
      'reg_date_time', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant(''));
  static const VerificationMeta _ccMeta = const VerificationMeta('cc');
  @override
  late final GeneratedColumn<int> cc = GeneratedColumn<int>(
      'cc', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(86));
  static const VerificationMeta _roleMeta = const VerificationMeta('role');
  @override
  late final GeneratedColumn<String> role = GeneratedColumn<String>(
      'role', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant('creator'));
  static const VerificationMeta _usedMeta = const VerificationMeta('used');
  @override
  late final GeneratedColumn<String> used = GeneratedColumn<String>(
      'used', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant('0'));
  static const VerificationMeta _enableMeta = const VerificationMeta('enable');
  @override
  late final GeneratedColumn<int> enable = GeneratedColumn<int>(
      'enable', aliasedName, false,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(1));
  static const VerificationMeta _lastLoginStoreIdMeta =
      const VerificationMeta('lastLoginStoreId');
  @override
  late final GeneratedColumn<String> lastLoginStoreId = GeneratedColumn<String>(
      'last_login_store_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant(''));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        username,
        uid,
        phoneNumber,
        token,
        tokenExpiredDate,
        tokenEnd,
        firstLogin,
        lastLoginTime,
        regDateTime,
        cc,
        role,
        used,
        enable,
        lastLoginStoreId
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'user_entity';
  @override
  VerificationContext validateIntegrity(Insertable<UserEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('username')) {
      context.handle(_usernameMeta,
          username.isAcceptableOrUnknown(data['username']!, _usernameMeta));
    } else if (isInserting) {
      context.missing(_usernameMeta);
    }
    if (data.containsKey('uid')) {
      context.handle(
          _uidMeta, uid.isAcceptableOrUnknown(data['uid']!, _uidMeta));
    } else if (isInserting) {
      context.missing(_uidMeta);
    }
    if (data.containsKey('phone_number')) {
      context.handle(
          _phoneNumberMeta,
          phoneNumber.isAcceptableOrUnknown(
              data['phone_number']!, _phoneNumberMeta));
    } else if (isInserting) {
      context.missing(_phoneNumberMeta);
    }
    if (data.containsKey('token')) {
      context.handle(
          _tokenMeta, token.isAcceptableOrUnknown(data['token']!, _tokenMeta));
    } else if (isInserting) {
      context.missing(_tokenMeta);
    }
    if (data.containsKey('token_expired_date')) {
      context.handle(
          _tokenExpiredDateMeta,
          tokenExpiredDate.isAcceptableOrUnknown(
              data['token_expired_date']!, _tokenExpiredDateMeta));
    } else if (isInserting) {
      context.missing(_tokenExpiredDateMeta);
    }
    if (data.containsKey('token_end')) {
      context.handle(_tokenEndMeta,
          tokenEnd.isAcceptableOrUnknown(data['token_end']!, _tokenEndMeta));
    } else if (isInserting) {
      context.missing(_tokenEndMeta);
    }
    if (data.containsKey('first_login')) {
      context.handle(
          _firstLoginMeta,
          firstLogin.isAcceptableOrUnknown(
              data['first_login']!, _firstLoginMeta));
    }
    if (data.containsKey('last_login_time')) {
      context.handle(
          _lastLoginTimeMeta,
          lastLoginTime.isAcceptableOrUnknown(
              data['last_login_time']!, _lastLoginTimeMeta));
    }
    if (data.containsKey('reg_date_time')) {
      context.handle(
          _regDateTimeMeta,
          regDateTime.isAcceptableOrUnknown(
              data['reg_date_time']!, _regDateTimeMeta));
    }
    if (data.containsKey('cc')) {
      context.handle(_ccMeta, cc.isAcceptableOrUnknown(data['cc']!, _ccMeta));
    }
    if (data.containsKey('role')) {
      context.handle(
          _roleMeta, role.isAcceptableOrUnknown(data['role']!, _roleMeta));
    }
    if (data.containsKey('used')) {
      context.handle(
          _usedMeta, used.isAcceptableOrUnknown(data['used']!, _usedMeta));
    }
    if (data.containsKey('enable')) {
      context.handle(_enableMeta,
          enable.isAcceptableOrUnknown(data['enable']!, _enableMeta));
    }
    if (data.containsKey('last_login_store_id')) {
      context.handle(
          _lastLoginStoreIdMeta,
          lastLoginStoreId.isAcceptableOrUnknown(
              data['last_login_store_id']!, _lastLoginStoreIdMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  UserEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return UserEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      username: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}username'])!,
      uid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uid'])!,
      phoneNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}phone_number'])!,
      token: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}token'])!,
      tokenExpiredDate: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}token_expired_date'])!,
      tokenEnd: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}token_end'])!,
      firstLogin: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}first_login'])!,
      lastLoginTime: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}last_login_time'])!,
      regDateTime: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}reg_date_time'])!,
      cc: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}cc'])!,
      role: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}role'])!,
      used: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}used'])!,
      enable: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}enable'])!,
      lastLoginStoreId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}last_login_store_id'])!,
    );
  }

  @override
  $UserEntityTable createAlias(String alias) {
    return $UserEntityTable(attachedDatabase, alias);
  }
}

class UserEntityData extends DataClass implements Insertable<UserEntityData> {
  final String id;
  final String username;
  final String uid;
  final String phoneNumber;
  final String token;
  final int tokenExpiredDate;
  final String tokenEnd;
  final int firstLogin;
  final String lastLoginTime;
  final String regDateTime;
  final int cc;
  final String role;
  final String used;
  final int enable;
  final String lastLoginStoreId;
  const UserEntityData(
      {required this.id,
      required this.username,
      required this.uid,
      required this.phoneNumber,
      required this.token,
      required this.tokenExpiredDate,
      required this.tokenEnd,
      required this.firstLogin,
      required this.lastLoginTime,
      required this.regDateTime,
      required this.cc,
      required this.role,
      required this.used,
      required this.enable,
      required this.lastLoginStoreId});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['username'] = Variable<String>(username);
    map['uid'] = Variable<String>(uid);
    map['phone_number'] = Variable<String>(phoneNumber);
    map['token'] = Variable<String>(token);
    map['token_expired_date'] = Variable<int>(tokenExpiredDate);
    map['token_end'] = Variable<String>(tokenEnd);
    map['first_login'] = Variable<int>(firstLogin);
    map['last_login_time'] = Variable<String>(lastLoginTime);
    map['reg_date_time'] = Variable<String>(regDateTime);
    map['cc'] = Variable<int>(cc);
    map['role'] = Variable<String>(role);
    map['used'] = Variable<String>(used);
    map['enable'] = Variable<int>(enable);
    map['last_login_store_id'] = Variable<String>(lastLoginStoreId);
    return map;
  }

  UserEntityCompanion toCompanion(bool nullToAbsent) {
    return UserEntityCompanion(
      id: Value(id),
      username: Value(username),
      uid: Value(uid),
      phoneNumber: Value(phoneNumber),
      token: Value(token),
      tokenExpiredDate: Value(tokenExpiredDate),
      tokenEnd: Value(tokenEnd),
      firstLogin: Value(firstLogin),
      lastLoginTime: Value(lastLoginTime),
      regDateTime: Value(regDateTime),
      cc: Value(cc),
      role: Value(role),
      used: Value(used),
      enable: Value(enable),
      lastLoginStoreId: Value(lastLoginStoreId),
    );
  }

  factory UserEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return UserEntityData(
      id: serializer.fromJson<String>(json['id']),
      username: serializer.fromJson<String>(json['username']),
      uid: serializer.fromJson<String>(json['uid']),
      phoneNumber: serializer.fromJson<String>(json['phoneNumber']),
      token: serializer.fromJson<String>(json['token']),
      tokenExpiredDate: serializer.fromJson<int>(json['tokenExpiredDate']),
      tokenEnd: serializer.fromJson<String>(json['tokenEnd']),
      firstLogin: serializer.fromJson<int>(json['firstLogin']),
      lastLoginTime: serializer.fromJson<String>(json['lastLoginTime']),
      regDateTime: serializer.fromJson<String>(json['regDateTime']),
      cc: serializer.fromJson<int>(json['cc']),
      role: serializer.fromJson<String>(json['role']),
      used: serializer.fromJson<String>(json['used']),
      enable: serializer.fromJson<int>(json['enable']),
      lastLoginStoreId: serializer.fromJson<String>(json['lastLoginStoreId']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'username': serializer.toJson<String>(username),
      'uid': serializer.toJson<String>(uid),
      'phoneNumber': serializer.toJson<String>(phoneNumber),
      'token': serializer.toJson<String>(token),
      'tokenExpiredDate': serializer.toJson<int>(tokenExpiredDate),
      'tokenEnd': serializer.toJson<String>(tokenEnd),
      'firstLogin': serializer.toJson<int>(firstLogin),
      'lastLoginTime': serializer.toJson<String>(lastLoginTime),
      'regDateTime': serializer.toJson<String>(regDateTime),
      'cc': serializer.toJson<int>(cc),
      'role': serializer.toJson<String>(role),
      'used': serializer.toJson<String>(used),
      'enable': serializer.toJson<int>(enable),
      'lastLoginStoreId': serializer.toJson<String>(lastLoginStoreId),
    };
  }

  UserEntityData copyWith(
          {String? id,
          String? username,
          String? uid,
          String? phoneNumber,
          String? token,
          int? tokenExpiredDate,
          String? tokenEnd,
          int? firstLogin,
          String? lastLoginTime,
          String? regDateTime,
          int? cc,
          String? role,
          String? used,
          int? enable,
          String? lastLoginStoreId}) =>
      UserEntityData(
        id: id ?? this.id,
        username: username ?? this.username,
        uid: uid ?? this.uid,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        token: token ?? this.token,
        tokenExpiredDate: tokenExpiredDate ?? this.tokenExpiredDate,
        tokenEnd: tokenEnd ?? this.tokenEnd,
        firstLogin: firstLogin ?? this.firstLogin,
        lastLoginTime: lastLoginTime ?? this.lastLoginTime,
        regDateTime: regDateTime ?? this.regDateTime,
        cc: cc ?? this.cc,
        role: role ?? this.role,
        used: used ?? this.used,
        enable: enable ?? this.enable,
        lastLoginStoreId: lastLoginStoreId ?? this.lastLoginStoreId,
      );
  UserEntityData copyWithCompanion(UserEntityCompanion data) {
    return UserEntityData(
      id: data.id.present ? data.id.value : this.id,
      username: data.username.present ? data.username.value : this.username,
      uid: data.uid.present ? data.uid.value : this.uid,
      phoneNumber:
          data.phoneNumber.present ? data.phoneNumber.value : this.phoneNumber,
      token: data.token.present ? data.token.value : this.token,
      tokenExpiredDate: data.tokenExpiredDate.present
          ? data.tokenExpiredDate.value
          : this.tokenExpiredDate,
      tokenEnd: data.tokenEnd.present ? data.tokenEnd.value : this.tokenEnd,
      firstLogin:
          data.firstLogin.present ? data.firstLogin.value : this.firstLogin,
      lastLoginTime: data.lastLoginTime.present
          ? data.lastLoginTime.value
          : this.lastLoginTime,
      regDateTime:
          data.regDateTime.present ? data.regDateTime.value : this.regDateTime,
      cc: data.cc.present ? data.cc.value : this.cc,
      role: data.role.present ? data.role.value : this.role,
      used: data.used.present ? data.used.value : this.used,
      enable: data.enable.present ? data.enable.value : this.enable,
      lastLoginStoreId: data.lastLoginStoreId.present
          ? data.lastLoginStoreId.value
          : this.lastLoginStoreId,
    );
  }

  @override
  String toString() {
    return (StringBuffer('UserEntityData(')
          ..write('id: $id, ')
          ..write('username: $username, ')
          ..write('uid: $uid, ')
          ..write('phoneNumber: $phoneNumber, ')
          ..write('token: $token, ')
          ..write('tokenExpiredDate: $tokenExpiredDate, ')
          ..write('tokenEnd: $tokenEnd, ')
          ..write('firstLogin: $firstLogin, ')
          ..write('lastLoginTime: $lastLoginTime, ')
          ..write('regDateTime: $regDateTime, ')
          ..write('cc: $cc, ')
          ..write('role: $role, ')
          ..write('used: $used, ')
          ..write('enable: $enable, ')
          ..write('lastLoginStoreId: $lastLoginStoreId')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      username,
      uid,
      phoneNumber,
      token,
      tokenExpiredDate,
      tokenEnd,
      firstLogin,
      lastLoginTime,
      regDateTime,
      cc,
      role,
      used,
      enable,
      lastLoginStoreId);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is UserEntityData &&
          other.id == this.id &&
          other.username == this.username &&
          other.uid == this.uid &&
          other.phoneNumber == this.phoneNumber &&
          other.token == this.token &&
          other.tokenExpiredDate == this.tokenExpiredDate &&
          other.tokenEnd == this.tokenEnd &&
          other.firstLogin == this.firstLogin &&
          other.lastLoginTime == this.lastLoginTime &&
          other.regDateTime == this.regDateTime &&
          other.cc == this.cc &&
          other.role == this.role &&
          other.used == this.used &&
          other.enable == this.enable &&
          other.lastLoginStoreId == this.lastLoginStoreId);
}

class UserEntityCompanion extends UpdateCompanion<UserEntityData> {
  final Value<String> id;
  final Value<String> username;
  final Value<String> uid;
  final Value<String> phoneNumber;
  final Value<String> token;
  final Value<int> tokenExpiredDate;
  final Value<String> tokenEnd;
  final Value<int> firstLogin;
  final Value<String> lastLoginTime;
  final Value<String> regDateTime;
  final Value<int> cc;
  final Value<String> role;
  final Value<String> used;
  final Value<int> enable;
  final Value<String> lastLoginStoreId;
  final Value<int> rowid;
  const UserEntityCompanion({
    this.id = const Value.absent(),
    this.username = const Value.absent(),
    this.uid = const Value.absent(),
    this.phoneNumber = const Value.absent(),
    this.token = const Value.absent(),
    this.tokenExpiredDate = const Value.absent(),
    this.tokenEnd = const Value.absent(),
    this.firstLogin = const Value.absent(),
    this.lastLoginTime = const Value.absent(),
    this.regDateTime = const Value.absent(),
    this.cc = const Value.absent(),
    this.role = const Value.absent(),
    this.used = const Value.absent(),
    this.enable = const Value.absent(),
    this.lastLoginStoreId = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  UserEntityCompanion.insert({
    required String id,
    required String username,
    required String uid,
    required String phoneNumber,
    required String token,
    required int tokenExpiredDate,
    required String tokenEnd,
    this.firstLogin = const Value.absent(),
    this.lastLoginTime = const Value.absent(),
    this.regDateTime = const Value.absent(),
    this.cc = const Value.absent(),
    this.role = const Value.absent(),
    this.used = const Value.absent(),
    this.enable = const Value.absent(),
    this.lastLoginStoreId = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        username = Value(username),
        uid = Value(uid),
        phoneNumber = Value(phoneNumber),
        token = Value(token),
        tokenExpiredDate = Value(tokenExpiredDate),
        tokenEnd = Value(tokenEnd);
  static Insertable<UserEntityData> custom({
    Expression<String>? id,
    Expression<String>? username,
    Expression<String>? uid,
    Expression<String>? phoneNumber,
    Expression<String>? token,
    Expression<int>? tokenExpiredDate,
    Expression<String>? tokenEnd,
    Expression<int>? firstLogin,
    Expression<String>? lastLoginTime,
    Expression<String>? regDateTime,
    Expression<int>? cc,
    Expression<String>? role,
    Expression<String>? used,
    Expression<int>? enable,
    Expression<String>? lastLoginStoreId,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (username != null) 'username': username,
      if (uid != null) 'uid': uid,
      if (phoneNumber != null) 'phone_number': phoneNumber,
      if (token != null) 'token': token,
      if (tokenExpiredDate != null) 'token_expired_date': tokenExpiredDate,
      if (tokenEnd != null) 'token_end': tokenEnd,
      if (firstLogin != null) 'first_login': firstLogin,
      if (lastLoginTime != null) 'last_login_time': lastLoginTime,
      if (regDateTime != null) 'reg_date_time': regDateTime,
      if (cc != null) 'cc': cc,
      if (role != null) 'role': role,
      if (used != null) 'used': used,
      if (enable != null) 'enable': enable,
      if (lastLoginStoreId != null) 'last_login_store_id': lastLoginStoreId,
      if (rowid != null) 'rowid': rowid,
    });
  }

  UserEntityCompanion copyWith(
      {Value<String>? id,
      Value<String>? username,
      Value<String>? uid,
      Value<String>? phoneNumber,
      Value<String>? token,
      Value<int>? tokenExpiredDate,
      Value<String>? tokenEnd,
      Value<int>? firstLogin,
      Value<String>? lastLoginTime,
      Value<String>? regDateTime,
      Value<int>? cc,
      Value<String>? role,
      Value<String>? used,
      Value<int>? enable,
      Value<String>? lastLoginStoreId,
      Value<int>? rowid}) {
    return UserEntityCompanion(
      id: id ?? this.id,
      username: username ?? this.username,
      uid: uid ?? this.uid,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      token: token ?? this.token,
      tokenExpiredDate: tokenExpiredDate ?? this.tokenExpiredDate,
      tokenEnd: tokenEnd ?? this.tokenEnd,
      firstLogin: firstLogin ?? this.firstLogin,
      lastLoginTime: lastLoginTime ?? this.lastLoginTime,
      regDateTime: regDateTime ?? this.regDateTime,
      cc: cc ?? this.cc,
      role: role ?? this.role,
      used: used ?? this.used,
      enable: enable ?? this.enable,
      lastLoginStoreId: lastLoginStoreId ?? this.lastLoginStoreId,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (username.present) {
      map['username'] = Variable<String>(username.value);
    }
    if (uid.present) {
      map['uid'] = Variable<String>(uid.value);
    }
    if (phoneNumber.present) {
      map['phone_number'] = Variable<String>(phoneNumber.value);
    }
    if (token.present) {
      map['token'] = Variable<String>(token.value);
    }
    if (tokenExpiredDate.present) {
      map['token_expired_date'] = Variable<int>(tokenExpiredDate.value);
    }
    if (tokenEnd.present) {
      map['token_end'] = Variable<String>(tokenEnd.value);
    }
    if (firstLogin.present) {
      map['first_login'] = Variable<int>(firstLogin.value);
    }
    if (lastLoginTime.present) {
      map['last_login_time'] = Variable<String>(lastLoginTime.value);
    }
    if (regDateTime.present) {
      map['reg_date_time'] = Variable<String>(regDateTime.value);
    }
    if (cc.present) {
      map['cc'] = Variable<int>(cc.value);
    }
    if (role.present) {
      map['role'] = Variable<String>(role.value);
    }
    if (used.present) {
      map['used'] = Variable<String>(used.value);
    }
    if (enable.present) {
      map['enable'] = Variable<int>(enable.value);
    }
    if (lastLoginStoreId.present) {
      map['last_login_store_id'] = Variable<String>(lastLoginStoreId.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UserEntityCompanion(')
          ..write('id: $id, ')
          ..write('username: $username, ')
          ..write('uid: $uid, ')
          ..write('phoneNumber: $phoneNumber, ')
          ..write('token: $token, ')
          ..write('tokenExpiredDate: $tokenExpiredDate, ')
          ..write('tokenEnd: $tokenEnd, ')
          ..write('firstLogin: $firstLogin, ')
          ..write('lastLoginTime: $lastLoginTime, ')
          ..write('regDateTime: $regDateTime, ')
          ..write('cc: $cc, ')
          ..write('role: $role, ')
          ..write('used: $used, ')
          ..write('enable: $enable, ')
          ..write('lastLoginStoreId: $lastLoginStoreId, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $WorkspaceEntityTable extends WorkspaceEntity
    with TableInfo<$WorkspaceEntityTable, WorkspaceEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $WorkspaceEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _workspaceIdMeta =
      const VerificationMeta('workspaceId');
  @override
  late final GeneratedColumn<String> workspaceId = GeneratedColumn<String>(
      'workspace_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _workspaceNameMeta =
      const VerificationMeta('workspaceName');
  @override
  late final GeneratedColumn<String> workspaceName = GeneratedColumn<String>(
      'workspace_name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _currentFileIdMeta =
      const VerificationMeta('currentFileId');
  @override
  late final GeneratedColumn<String> currentFileId = GeneratedColumn<String>(
      'current_file_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  @override
  late final GeneratedColumn<int> createTime = GeneratedColumn<int>(
      'create_time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _lastEditTimeMeta =
      const VerificationMeta('lastEditTime');
  @override
  late final GeneratedColumn<int> lastEditTime = GeneratedColumn<int>(
      'last_edit_time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _projectIdMeta =
      const VerificationMeta('projectId');
  @override
  late final GeneratedColumn<String> projectId = GeneratedColumn<String>(
      'project_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES project_entity (project_id)'));
  static const VerificationMeta _filterValueMeta =
      const VerificationMeta('filterValue');
  @override
  late final GeneratedColumn<String> filterValue = GeneratedColumn<String>(
      'filter_value', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant('{}'));
  static const VerificationMeta _sortValueMeta =
      const VerificationMeta('sortValue');
  @override
  late final GeneratedColumn<String> sortValue = GeneratedColumn<String>(
      'sort_value', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant('{}'));
  @override
  List<GeneratedColumn> get $columns => [
        workspaceId,
        workspaceName,
        currentFileId,
        createTime,
        lastEditTime,
        projectId,
        filterValue,
        sortValue
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'workspace_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<WorkspaceEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('workspace_id')) {
      context.handle(
          _workspaceIdMeta,
          workspaceId.isAcceptableOrUnknown(
              data['workspace_id']!, _workspaceIdMeta));
    } else if (isInserting) {
      context.missing(_workspaceIdMeta);
    }
    if (data.containsKey('workspace_name')) {
      context.handle(
          _workspaceNameMeta,
          workspaceName.isAcceptableOrUnknown(
              data['workspace_name']!, _workspaceNameMeta));
    } else if (isInserting) {
      context.missing(_workspaceNameMeta);
    }
    if (data.containsKey('current_file_id')) {
      context.handle(
          _currentFileIdMeta,
          currentFileId.isAcceptableOrUnknown(
              data['current_file_id']!, _currentFileIdMeta));
    } else if (isInserting) {
      context.missing(_currentFileIdMeta);
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    } else if (isInserting) {
      context.missing(_createTimeMeta);
    }
    if (data.containsKey('last_edit_time')) {
      context.handle(
          _lastEditTimeMeta,
          lastEditTime.isAcceptableOrUnknown(
              data['last_edit_time']!, _lastEditTimeMeta));
    } else if (isInserting) {
      context.missing(_lastEditTimeMeta);
    }
    if (data.containsKey('project_id')) {
      context.handle(_projectIdMeta,
          projectId.isAcceptableOrUnknown(data['project_id']!, _projectIdMeta));
    } else if (isInserting) {
      context.missing(_projectIdMeta);
    }
    if (data.containsKey('filter_value')) {
      context.handle(
          _filterValueMeta,
          filterValue.isAcceptableOrUnknown(
              data['filter_value']!, _filterValueMeta));
    }
    if (data.containsKey('sort_value')) {
      context.handle(_sortValueMeta,
          sortValue.isAcceptableOrUnknown(data['sort_value']!, _sortValueMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {workspaceId};
  @override
  WorkspaceEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return WorkspaceEntityData(
      workspaceId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}workspace_id'])!,
      workspaceName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}workspace_name'])!,
      currentFileId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}current_file_id'])!,
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}create_time'])!,
      lastEditTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}last_edit_time'])!,
      projectId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}project_id'])!,
      filterValue: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}filter_value']),
      sortValue: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}sort_value']),
    );
  }

  @override
  $WorkspaceEntityTable createAlias(String alias) {
    return $WorkspaceEntityTable(attachedDatabase, alias);
  }
}

class WorkspaceEntityData extends DataClass
    implements Insertable<WorkspaceEntityData> {
  final String workspaceId;
  final String workspaceName;
  final String currentFileId;
  final int createTime;
  final int lastEditTime;
  final String projectId;
  final String? filterValue;
  final String? sortValue;
  const WorkspaceEntityData(
      {required this.workspaceId,
      required this.workspaceName,
      required this.currentFileId,
      required this.createTime,
      required this.lastEditTime,
      required this.projectId,
      this.filterValue,
      this.sortValue});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['workspace_id'] = Variable<String>(workspaceId);
    map['workspace_name'] = Variable<String>(workspaceName);
    map['current_file_id'] = Variable<String>(currentFileId);
    map['create_time'] = Variable<int>(createTime);
    map['last_edit_time'] = Variable<int>(lastEditTime);
    map['project_id'] = Variable<String>(projectId);
    if (!nullToAbsent || filterValue != null) {
      map['filter_value'] = Variable<String>(filterValue);
    }
    if (!nullToAbsent || sortValue != null) {
      map['sort_value'] = Variable<String>(sortValue);
    }
    return map;
  }

  WorkspaceEntityCompanion toCompanion(bool nullToAbsent) {
    return WorkspaceEntityCompanion(
      workspaceId: Value(workspaceId),
      workspaceName: Value(workspaceName),
      currentFileId: Value(currentFileId),
      createTime: Value(createTime),
      lastEditTime: Value(lastEditTime),
      projectId: Value(projectId),
      filterValue: filterValue == null && nullToAbsent
          ? const Value.absent()
          : Value(filterValue),
      sortValue: sortValue == null && nullToAbsent
          ? const Value.absent()
          : Value(sortValue),
    );
  }

  factory WorkspaceEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return WorkspaceEntityData(
      workspaceId: serializer.fromJson<String>(json['workspaceId']),
      workspaceName: serializer.fromJson<String>(json['workspaceName']),
      currentFileId: serializer.fromJson<String>(json['currentFileId']),
      createTime: serializer.fromJson<int>(json['createTime']),
      lastEditTime: serializer.fromJson<int>(json['lastEditTime']),
      projectId: serializer.fromJson<String>(json['projectId']),
      filterValue: serializer.fromJson<String?>(json['filterValue']),
      sortValue: serializer.fromJson<String?>(json['sortValue']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'workspaceId': serializer.toJson<String>(workspaceId),
      'workspaceName': serializer.toJson<String>(workspaceName),
      'currentFileId': serializer.toJson<String>(currentFileId),
      'createTime': serializer.toJson<int>(createTime),
      'lastEditTime': serializer.toJson<int>(lastEditTime),
      'projectId': serializer.toJson<String>(projectId),
      'filterValue': serializer.toJson<String?>(filterValue),
      'sortValue': serializer.toJson<String?>(sortValue),
    };
  }

  WorkspaceEntityData copyWith(
          {String? workspaceId,
          String? workspaceName,
          String? currentFileId,
          int? createTime,
          int? lastEditTime,
          String? projectId,
          Value<String?> filterValue = const Value.absent(),
          Value<String?> sortValue = const Value.absent()}) =>
      WorkspaceEntityData(
        workspaceId: workspaceId ?? this.workspaceId,
        workspaceName: workspaceName ?? this.workspaceName,
        currentFileId: currentFileId ?? this.currentFileId,
        createTime: createTime ?? this.createTime,
        lastEditTime: lastEditTime ?? this.lastEditTime,
        projectId: projectId ?? this.projectId,
        filterValue: filterValue.present ? filterValue.value : this.filterValue,
        sortValue: sortValue.present ? sortValue.value : this.sortValue,
      );
  WorkspaceEntityData copyWithCompanion(WorkspaceEntityCompanion data) {
    return WorkspaceEntityData(
      workspaceId:
          data.workspaceId.present ? data.workspaceId.value : this.workspaceId,
      workspaceName: data.workspaceName.present
          ? data.workspaceName.value
          : this.workspaceName,
      currentFileId: data.currentFileId.present
          ? data.currentFileId.value
          : this.currentFileId,
      createTime:
          data.createTime.present ? data.createTime.value : this.createTime,
      lastEditTime: data.lastEditTime.present
          ? data.lastEditTime.value
          : this.lastEditTime,
      projectId: data.projectId.present ? data.projectId.value : this.projectId,
      filterValue:
          data.filterValue.present ? data.filterValue.value : this.filterValue,
      sortValue: data.sortValue.present ? data.sortValue.value : this.sortValue,
    );
  }

  @override
  String toString() {
    return (StringBuffer('WorkspaceEntityData(')
          ..write('workspaceId: $workspaceId, ')
          ..write('workspaceName: $workspaceName, ')
          ..write('currentFileId: $currentFileId, ')
          ..write('createTime: $createTime, ')
          ..write('lastEditTime: $lastEditTime, ')
          ..write('projectId: $projectId, ')
          ..write('filterValue: $filterValue, ')
          ..write('sortValue: $sortValue')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(workspaceId, workspaceName, currentFileId,
      createTime, lastEditTime, projectId, filterValue, sortValue);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is WorkspaceEntityData &&
          other.workspaceId == this.workspaceId &&
          other.workspaceName == this.workspaceName &&
          other.currentFileId == this.currentFileId &&
          other.createTime == this.createTime &&
          other.lastEditTime == this.lastEditTime &&
          other.projectId == this.projectId &&
          other.filterValue == this.filterValue &&
          other.sortValue == this.sortValue);
}

class WorkspaceEntityCompanion extends UpdateCompanion<WorkspaceEntityData> {
  final Value<String> workspaceId;
  final Value<String> workspaceName;
  final Value<String> currentFileId;
  final Value<int> createTime;
  final Value<int> lastEditTime;
  final Value<String> projectId;
  final Value<String?> filterValue;
  final Value<String?> sortValue;
  final Value<int> rowid;
  const WorkspaceEntityCompanion({
    this.workspaceId = const Value.absent(),
    this.workspaceName = const Value.absent(),
    this.currentFileId = const Value.absent(),
    this.createTime = const Value.absent(),
    this.lastEditTime = const Value.absent(),
    this.projectId = const Value.absent(),
    this.filterValue = const Value.absent(),
    this.sortValue = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  WorkspaceEntityCompanion.insert({
    required String workspaceId,
    required String workspaceName,
    required String currentFileId,
    required int createTime,
    required int lastEditTime,
    required String projectId,
    this.filterValue = const Value.absent(),
    this.sortValue = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : workspaceId = Value(workspaceId),
        workspaceName = Value(workspaceName),
        currentFileId = Value(currentFileId),
        createTime = Value(createTime),
        lastEditTime = Value(lastEditTime),
        projectId = Value(projectId);
  static Insertable<WorkspaceEntityData> custom({
    Expression<String>? workspaceId,
    Expression<String>? workspaceName,
    Expression<String>? currentFileId,
    Expression<int>? createTime,
    Expression<int>? lastEditTime,
    Expression<String>? projectId,
    Expression<String>? filterValue,
    Expression<String>? sortValue,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (workspaceId != null) 'workspace_id': workspaceId,
      if (workspaceName != null) 'workspace_name': workspaceName,
      if (currentFileId != null) 'current_file_id': currentFileId,
      if (createTime != null) 'create_time': createTime,
      if (lastEditTime != null) 'last_edit_time': lastEditTime,
      if (projectId != null) 'project_id': projectId,
      if (filterValue != null) 'filter_value': filterValue,
      if (sortValue != null) 'sort_value': sortValue,
      if (rowid != null) 'rowid': rowid,
    });
  }

  WorkspaceEntityCompanion copyWith(
      {Value<String>? workspaceId,
      Value<String>? workspaceName,
      Value<String>? currentFileId,
      Value<int>? createTime,
      Value<int>? lastEditTime,
      Value<String>? projectId,
      Value<String?>? filterValue,
      Value<String?>? sortValue,
      Value<int>? rowid}) {
    return WorkspaceEntityCompanion(
      workspaceId: workspaceId ?? this.workspaceId,
      workspaceName: workspaceName ?? this.workspaceName,
      currentFileId: currentFileId ?? this.currentFileId,
      createTime: createTime ?? this.createTime,
      lastEditTime: lastEditTime ?? this.lastEditTime,
      projectId: projectId ?? this.projectId,
      filterValue: filterValue ?? this.filterValue,
      sortValue: sortValue ?? this.sortValue,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (workspaceId.present) {
      map['workspace_id'] = Variable<String>(workspaceId.value);
    }
    if (workspaceName.present) {
      map['workspace_name'] = Variable<String>(workspaceName.value);
    }
    if (currentFileId.present) {
      map['current_file_id'] = Variable<String>(currentFileId.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<int>(createTime.value);
    }
    if (lastEditTime.present) {
      map['last_edit_time'] = Variable<int>(lastEditTime.value);
    }
    if (projectId.present) {
      map['project_id'] = Variable<String>(projectId.value);
    }
    if (filterValue.present) {
      map['filter_value'] = Variable<String>(filterValue.value);
    }
    if (sortValue.present) {
      map['sort_value'] = Variable<String>(sortValue.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('WorkspaceEntityCompanion(')
          ..write('workspaceId: $workspaceId, ')
          ..write('workspaceName: $workspaceName, ')
          ..write('currentFileId: $currentFileId, ')
          ..write('createTime: $createTime, ')
          ..write('lastEditTime: $lastEditTime, ')
          ..write('projectId: $projectId, ')
          ..write('filterValue: $filterValue, ')
          ..write('sortValue: $sortValue, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $WorkspaceFileEntityTable extends WorkspaceFileEntity
    with TableInfo<$WorkspaceFileEntityTable, WorkspaceFileEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $WorkspaceFileEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _fileIdMeta = const VerificationMeta('fileId');
  @override
  late final GeneratedColumn<String> fileId = GeneratedColumn<String>(
      'file_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _editedMeta = const VerificationMeta('edited');
  @override
  late final GeneratedColumn<bool> edited = GeneratedColumn<bool>(
      'edited', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("edited" IN (0, 1))'));
  static const VerificationMeta _starsMeta = const VerificationMeta('stars');
  @override
  late final GeneratedColumn<int> stars = GeneratedColumn<int>(
      'stars', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _exportedMeta =
      const VerificationMeta('exported');
  @override
  late final GeneratedColumn<bool> exported = GeneratedColumn<bool>(
      'exported', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("exported" IN (0, 1))'));
  static const VerificationMeta _orgPathMeta =
      const VerificationMeta('orgPath');
  @override
  late final GeneratedColumn<String> orgPath = GeneratedColumn<String>(
      'org_path', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _exportTimeMeta =
      const VerificationMeta('exportTime');
  @override
  late final GeneratedColumn<int> exportTime = GeneratedColumn<int>(
      'export_time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _formatMeta = const VerificationMeta('format');
  @override
  late final GeneratedColumn<String> format = GeneratedColumn<String>(
      'format', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _brokenMeta = const VerificationMeta('broken');
  @override
  late final GeneratedColumn<bool> broken = GeneratedColumn<bool>(
      'broken', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("broken" IN (0, 1))'));
  static const VerificationMeta _lastEditTimeMeta =
      const VerificationMeta('lastEditTime');
  @override
  late final GeneratedColumn<int> lastEditTime = GeneratedColumn<int>(
      'last_edit_time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  @override
  late final GeneratedColumn<int> createTime = GeneratedColumn<int>(
      'create_time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _sizeMeta = const VerificationMeta('size');
  @override
  late final GeneratedColumn<int> size = GeneratedColumn<int>(
      'size', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _widthMeta = const VerificationMeta('width');
  @override
  late final GeneratedColumn<int> width = GeneratedColumn<int>(
      'width', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _heightMeta = const VerificationMeta('height');
  @override
  late final GeneratedColumn<int> height = GeneratedColumn<int>(
      'height', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _orientationMeta =
      const VerificationMeta('orientation');
  @override
  late final GeneratedColumn<int> orientation = GeneratedColumn<int>(
      'orientation', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _iccTypeMeta =
      const VerificationMeta('iccType');
  @override
  late final GeneratedColumn<int> iccType = GeneratedColumn<int>(
      'icc_type', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _isRawMeta = const VerificationMeta('isRaw');
  @override
  late final GeneratedColumn<bool> isRaw = GeneratedColumn<bool>(
      'is_raw', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_raw" IN (0, 1))'));
  static const VerificationMeta _rawPathMeta =
      const VerificationMeta('rawPath');
  @override
  late final GeneratedColumn<String> rawPath = GeneratedColumn<String>(
      'raw_path', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _convertedMeta =
      const VerificationMeta('converted');
  @override
  late final GeneratedColumn<bool> converted = GeneratedColumn<bool>(
      'converted', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: true,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("converted" IN (0, 1))'));
  static const VerificationMeta _workspaceIdMeta =
      const VerificationMeta('workspaceId');
  @override
  late final GeneratedColumn<String> workspaceId = GeneratedColumn<String>(
      'workspace_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'REFERENCES workspace_entity (workspace_id)'));
  static const VerificationMeta _fileNameMeta =
      const VerificationMeta('fileName');
  @override
  late final GeneratedColumn<String> fileName = GeneratedColumn<String>(
      'file_name', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant(''));
  static const VerificationMeta _iconizedMeta =
      const VerificationMeta('iconized');
  @override
  late final GeneratedColumn<bool> iconized = GeneratedColumn<bool>(
      'iconized', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("iconized" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _midIconizedMeta =
      const VerificationMeta('midIconized');
  @override
  late final GeneratedColumn<bool> midIconized = GeneratedColumn<bool>(
      'mid_iconized', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("mid_iconized" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _captureTimeMeta =
      const VerificationMeta('captureTime');
  @override
  late final GeneratedColumn<int> captureTime = GeneratedColumn<int>(
      'capture_time', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _isOverSizeMeta =
      const VerificationMeta('isOverSize');
  @override
  late final GeneratedColumn<bool> isOverSize = GeneratedColumn<bool>(
      'is_over_size', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_over_size" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _faceCountMeta =
      const VerificationMeta('faceCount');
  @override
  late final GeneratedColumn<int> faceCount = GeneratedColumn<int>(
      'face_count', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _binFormatMeta =
      const VerificationMeta('binFormat');
  @override
  late final GeneratedColumn<String> binFormat = GeneratedColumn<String>(
      'bin_format', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant(''));
  static const VerificationMeta _rawAutoExposeMeta =
      const VerificationMeta('rawAutoExpose');
  @override
  late final GeneratedColumn<bool> rawAutoExpose = GeneratedColumn<bool>(
      'raw_auto_expose', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("raw_auto_expose" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _rawAutoAdjustTypeMeta =
      const VerificationMeta('rawAutoAdjustType');
  @override
  late final GeneratedColumn<int> rawAutoAdjustType = GeneratedColumn<int>(
      'raw_auto_adjust_type', aliasedName, true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _isDeletedMeta =
      const VerificationMeta('isDeleted');
  @override
  late final GeneratedColumn<bool> isDeleted = GeneratedColumn<bool>(
      'is_deleted', aliasedName, true,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_deleted" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [
        fileId,
        edited,
        stars,
        exported,
        orgPath,
        exportTime,
        format,
        broken,
        lastEditTime,
        createTime,
        size,
        width,
        height,
        orientation,
        iccType,
        isRaw,
        rawPath,
        converted,
        workspaceId,
        fileName,
        iconized,
        midIconized,
        captureTime,
        isOverSize,
        faceCount,
        binFormat,
        rawAutoExpose,
        rawAutoAdjustType,
        isDeleted
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'workspace_file_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<WorkspaceFileEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('file_id')) {
      context.handle(_fileIdMeta,
          fileId.isAcceptableOrUnknown(data['file_id']!, _fileIdMeta));
    } else if (isInserting) {
      context.missing(_fileIdMeta);
    }
    if (data.containsKey('edited')) {
      context.handle(_editedMeta,
          edited.isAcceptableOrUnknown(data['edited']!, _editedMeta));
    } else if (isInserting) {
      context.missing(_editedMeta);
    }
    if (data.containsKey('stars')) {
      context.handle(
          _starsMeta, stars.isAcceptableOrUnknown(data['stars']!, _starsMeta));
    } else if (isInserting) {
      context.missing(_starsMeta);
    }
    if (data.containsKey('exported')) {
      context.handle(_exportedMeta,
          exported.isAcceptableOrUnknown(data['exported']!, _exportedMeta));
    } else if (isInserting) {
      context.missing(_exportedMeta);
    }
    if (data.containsKey('org_path')) {
      context.handle(_orgPathMeta,
          orgPath.isAcceptableOrUnknown(data['org_path']!, _orgPathMeta));
    } else if (isInserting) {
      context.missing(_orgPathMeta);
    }
    if (data.containsKey('export_time')) {
      context.handle(
          _exportTimeMeta,
          exportTime.isAcceptableOrUnknown(
              data['export_time']!, _exportTimeMeta));
    } else if (isInserting) {
      context.missing(_exportTimeMeta);
    }
    if (data.containsKey('format')) {
      context.handle(_formatMeta,
          format.isAcceptableOrUnknown(data['format']!, _formatMeta));
    } else if (isInserting) {
      context.missing(_formatMeta);
    }
    if (data.containsKey('broken')) {
      context.handle(_brokenMeta,
          broken.isAcceptableOrUnknown(data['broken']!, _brokenMeta));
    } else if (isInserting) {
      context.missing(_brokenMeta);
    }
    if (data.containsKey('last_edit_time')) {
      context.handle(
          _lastEditTimeMeta,
          lastEditTime.isAcceptableOrUnknown(
              data['last_edit_time']!, _lastEditTimeMeta));
    } else if (isInserting) {
      context.missing(_lastEditTimeMeta);
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    } else if (isInserting) {
      context.missing(_createTimeMeta);
    }
    if (data.containsKey('size')) {
      context.handle(
          _sizeMeta, size.isAcceptableOrUnknown(data['size']!, _sizeMeta));
    } else if (isInserting) {
      context.missing(_sizeMeta);
    }
    if (data.containsKey('width')) {
      context.handle(
          _widthMeta, width.isAcceptableOrUnknown(data['width']!, _widthMeta));
    } else if (isInserting) {
      context.missing(_widthMeta);
    }
    if (data.containsKey('height')) {
      context.handle(_heightMeta,
          height.isAcceptableOrUnknown(data['height']!, _heightMeta));
    } else if (isInserting) {
      context.missing(_heightMeta);
    }
    if (data.containsKey('orientation')) {
      context.handle(
          _orientationMeta,
          orientation.isAcceptableOrUnknown(
              data['orientation']!, _orientationMeta));
    } else if (isInserting) {
      context.missing(_orientationMeta);
    }
    if (data.containsKey('icc_type')) {
      context.handle(_iccTypeMeta,
          iccType.isAcceptableOrUnknown(data['icc_type']!, _iccTypeMeta));
    } else if (isInserting) {
      context.missing(_iccTypeMeta);
    }
    if (data.containsKey('is_raw')) {
      context.handle(
          _isRawMeta, isRaw.isAcceptableOrUnknown(data['is_raw']!, _isRawMeta));
    } else if (isInserting) {
      context.missing(_isRawMeta);
    }
    if (data.containsKey('raw_path')) {
      context.handle(_rawPathMeta,
          rawPath.isAcceptableOrUnknown(data['raw_path']!, _rawPathMeta));
    } else if (isInserting) {
      context.missing(_rawPathMeta);
    }
    if (data.containsKey('converted')) {
      context.handle(_convertedMeta,
          converted.isAcceptableOrUnknown(data['converted']!, _convertedMeta));
    } else if (isInserting) {
      context.missing(_convertedMeta);
    }
    if (data.containsKey('workspace_id')) {
      context.handle(
          _workspaceIdMeta,
          workspaceId.isAcceptableOrUnknown(
              data['workspace_id']!, _workspaceIdMeta));
    } else if (isInserting) {
      context.missing(_workspaceIdMeta);
    }
    if (data.containsKey('file_name')) {
      context.handle(_fileNameMeta,
          fileName.isAcceptableOrUnknown(data['file_name']!, _fileNameMeta));
    }
    if (data.containsKey('iconized')) {
      context.handle(_iconizedMeta,
          iconized.isAcceptableOrUnknown(data['iconized']!, _iconizedMeta));
    }
    if (data.containsKey('mid_iconized')) {
      context.handle(
          _midIconizedMeta,
          midIconized.isAcceptableOrUnknown(
              data['mid_iconized']!, _midIconizedMeta));
    }
    if (data.containsKey('capture_time')) {
      context.handle(
          _captureTimeMeta,
          captureTime.isAcceptableOrUnknown(
              data['capture_time']!, _captureTimeMeta));
    }
    if (data.containsKey('is_over_size')) {
      context.handle(
          _isOverSizeMeta,
          isOverSize.isAcceptableOrUnknown(
              data['is_over_size']!, _isOverSizeMeta));
    }
    if (data.containsKey('face_count')) {
      context.handle(_faceCountMeta,
          faceCount.isAcceptableOrUnknown(data['face_count']!, _faceCountMeta));
    }
    if (data.containsKey('bin_format')) {
      context.handle(_binFormatMeta,
          binFormat.isAcceptableOrUnknown(data['bin_format']!, _binFormatMeta));
    }
    if (data.containsKey('raw_auto_expose')) {
      context.handle(
          _rawAutoExposeMeta,
          rawAutoExpose.isAcceptableOrUnknown(
              data['raw_auto_expose']!, _rawAutoExposeMeta));
    }
    if (data.containsKey('raw_auto_adjust_type')) {
      context.handle(
          _rawAutoAdjustTypeMeta,
          rawAutoAdjustType.isAcceptableOrUnknown(
              data['raw_auto_adjust_type']!, _rawAutoAdjustTypeMeta));
    }
    if (data.containsKey('is_deleted')) {
      context.handle(_isDeletedMeta,
          isDeleted.isAcceptableOrUnknown(data['is_deleted']!, _isDeletedMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {fileId};
  @override
  WorkspaceFileEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return WorkspaceFileEntityData(
      fileId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_id'])!,
      edited: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}edited'])!,
      stars: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}stars'])!,
      exported: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}exported'])!,
      orgPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}org_path'])!,
      exportTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}export_time'])!,
      format: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}format'])!,
      broken: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}broken'])!,
      lastEditTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}last_edit_time'])!,
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}create_time'])!,
      size: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}size'])!,
      width: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}width'])!,
      height: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}height'])!,
      orientation: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}orientation'])!,
      iccType: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}icc_type'])!,
      isRaw: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_raw'])!,
      rawPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}raw_path'])!,
      converted: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}converted'])!,
      workspaceId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}workspace_id'])!,
      fileName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_name']),
      iconized: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}iconized']),
      midIconized: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}mid_iconized']),
      captureTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}capture_time']),
      isOverSize: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_over_size']),
      faceCount: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}face_count']),
      binFormat: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}bin_format']),
      rawAutoExpose: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}raw_auto_expose']),
      rawAutoAdjustType: attachedDatabase.typeMapping.read(
          DriftSqlType.int, data['${effectivePrefix}raw_auto_adjust_type']),
      isDeleted: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_deleted']),
    );
  }

  @override
  $WorkspaceFileEntityTable createAlias(String alias) {
    return $WorkspaceFileEntityTable(attachedDatabase, alias);
  }
}

class WorkspaceFileEntityData extends DataClass
    implements Insertable<WorkspaceFileEntityData> {
  final String fileId;
  final bool edited;
  final int stars;
  final bool exported;
  final String orgPath;
  final int exportTime;
  final String format;
  final bool broken;
  final int lastEditTime;
  final int createTime;
  final int size;
  final int width;
  final int height;
  final int orientation;
  final int iccType;
  final bool isRaw;
  final String rawPath;
  final bool converted;
  final String workspaceId;
  final String? fileName;
  final bool? iconized;
  final bool? midIconized;
  final int? captureTime;
  final bool? isOverSize;
  final int? faceCount;
  final String? binFormat;
  final bool? rawAutoExpose;
  final int? rawAutoAdjustType;
  final bool? isDeleted;
  const WorkspaceFileEntityData(
      {required this.fileId,
      required this.edited,
      required this.stars,
      required this.exported,
      required this.orgPath,
      required this.exportTime,
      required this.format,
      required this.broken,
      required this.lastEditTime,
      required this.createTime,
      required this.size,
      required this.width,
      required this.height,
      required this.orientation,
      required this.iccType,
      required this.isRaw,
      required this.rawPath,
      required this.converted,
      required this.workspaceId,
      this.fileName,
      this.iconized,
      this.midIconized,
      this.captureTime,
      this.isOverSize,
      this.faceCount,
      this.binFormat,
      this.rawAutoExpose,
      this.rawAutoAdjustType,
      this.isDeleted});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['file_id'] = Variable<String>(fileId);
    map['edited'] = Variable<bool>(edited);
    map['stars'] = Variable<int>(stars);
    map['exported'] = Variable<bool>(exported);
    map['org_path'] = Variable<String>(orgPath);
    map['export_time'] = Variable<int>(exportTime);
    map['format'] = Variable<String>(format);
    map['broken'] = Variable<bool>(broken);
    map['last_edit_time'] = Variable<int>(lastEditTime);
    map['create_time'] = Variable<int>(createTime);
    map['size'] = Variable<int>(size);
    map['width'] = Variable<int>(width);
    map['height'] = Variable<int>(height);
    map['orientation'] = Variable<int>(orientation);
    map['icc_type'] = Variable<int>(iccType);
    map['is_raw'] = Variable<bool>(isRaw);
    map['raw_path'] = Variable<String>(rawPath);
    map['converted'] = Variable<bool>(converted);
    map['workspace_id'] = Variable<String>(workspaceId);
    if (!nullToAbsent || fileName != null) {
      map['file_name'] = Variable<String>(fileName);
    }
    if (!nullToAbsent || iconized != null) {
      map['iconized'] = Variable<bool>(iconized);
    }
    if (!nullToAbsent || midIconized != null) {
      map['mid_iconized'] = Variable<bool>(midIconized);
    }
    if (!nullToAbsent || captureTime != null) {
      map['capture_time'] = Variable<int>(captureTime);
    }
    if (!nullToAbsent || isOverSize != null) {
      map['is_over_size'] = Variable<bool>(isOverSize);
    }
    if (!nullToAbsent || faceCount != null) {
      map['face_count'] = Variable<int>(faceCount);
    }
    if (!nullToAbsent || binFormat != null) {
      map['bin_format'] = Variable<String>(binFormat);
    }
    if (!nullToAbsent || rawAutoExpose != null) {
      map['raw_auto_expose'] = Variable<bool>(rawAutoExpose);
    }
    if (!nullToAbsent || rawAutoAdjustType != null) {
      map['raw_auto_adjust_type'] = Variable<int>(rawAutoAdjustType);
    }
    if (!nullToAbsent || isDeleted != null) {
      map['is_deleted'] = Variable<bool>(isDeleted);
    }
    return map;
  }

  WorkspaceFileEntityCompanion toCompanion(bool nullToAbsent) {
    return WorkspaceFileEntityCompanion(
      fileId: Value(fileId),
      edited: Value(edited),
      stars: Value(stars),
      exported: Value(exported),
      orgPath: Value(orgPath),
      exportTime: Value(exportTime),
      format: Value(format),
      broken: Value(broken),
      lastEditTime: Value(lastEditTime),
      createTime: Value(createTime),
      size: Value(size),
      width: Value(width),
      height: Value(height),
      orientation: Value(orientation),
      iccType: Value(iccType),
      isRaw: Value(isRaw),
      rawPath: Value(rawPath),
      converted: Value(converted),
      workspaceId: Value(workspaceId),
      fileName: fileName == null && nullToAbsent
          ? const Value.absent()
          : Value(fileName),
      iconized: iconized == null && nullToAbsent
          ? const Value.absent()
          : Value(iconized),
      midIconized: midIconized == null && nullToAbsent
          ? const Value.absent()
          : Value(midIconized),
      captureTime: captureTime == null && nullToAbsent
          ? const Value.absent()
          : Value(captureTime),
      isOverSize: isOverSize == null && nullToAbsent
          ? const Value.absent()
          : Value(isOverSize),
      faceCount: faceCount == null && nullToAbsent
          ? const Value.absent()
          : Value(faceCount),
      binFormat: binFormat == null && nullToAbsent
          ? const Value.absent()
          : Value(binFormat),
      rawAutoExpose: rawAutoExpose == null && nullToAbsent
          ? const Value.absent()
          : Value(rawAutoExpose),
      rawAutoAdjustType: rawAutoAdjustType == null && nullToAbsent
          ? const Value.absent()
          : Value(rawAutoAdjustType),
      isDeleted: isDeleted == null && nullToAbsent
          ? const Value.absent()
          : Value(isDeleted),
    );
  }

  factory WorkspaceFileEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return WorkspaceFileEntityData(
      fileId: serializer.fromJson<String>(json['fileId']),
      edited: serializer.fromJson<bool>(json['edited']),
      stars: serializer.fromJson<int>(json['stars']),
      exported: serializer.fromJson<bool>(json['exported']),
      orgPath: serializer.fromJson<String>(json['orgPath']),
      exportTime: serializer.fromJson<int>(json['exportTime']),
      format: serializer.fromJson<String>(json['format']),
      broken: serializer.fromJson<bool>(json['broken']),
      lastEditTime: serializer.fromJson<int>(json['lastEditTime']),
      createTime: serializer.fromJson<int>(json['createTime']),
      size: serializer.fromJson<int>(json['size']),
      width: serializer.fromJson<int>(json['width']),
      height: serializer.fromJson<int>(json['height']),
      orientation: serializer.fromJson<int>(json['orientation']),
      iccType: serializer.fromJson<int>(json['iccType']),
      isRaw: serializer.fromJson<bool>(json['isRaw']),
      rawPath: serializer.fromJson<String>(json['rawPath']),
      converted: serializer.fromJson<bool>(json['converted']),
      workspaceId: serializer.fromJson<String>(json['workspaceId']),
      fileName: serializer.fromJson<String?>(json['fileName']),
      iconized: serializer.fromJson<bool?>(json['iconized']),
      midIconized: serializer.fromJson<bool?>(json['midIconized']),
      captureTime: serializer.fromJson<int?>(json['captureTime']),
      isOverSize: serializer.fromJson<bool?>(json['isOverSize']),
      faceCount: serializer.fromJson<int?>(json['faceCount']),
      binFormat: serializer.fromJson<String?>(json['binFormat']),
      rawAutoExpose: serializer.fromJson<bool?>(json['rawAutoExpose']),
      rawAutoAdjustType: serializer.fromJson<int?>(json['rawAutoAdjustType']),
      isDeleted: serializer.fromJson<bool?>(json['isDeleted']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'fileId': serializer.toJson<String>(fileId),
      'edited': serializer.toJson<bool>(edited),
      'stars': serializer.toJson<int>(stars),
      'exported': serializer.toJson<bool>(exported),
      'orgPath': serializer.toJson<String>(orgPath),
      'exportTime': serializer.toJson<int>(exportTime),
      'format': serializer.toJson<String>(format),
      'broken': serializer.toJson<bool>(broken),
      'lastEditTime': serializer.toJson<int>(lastEditTime),
      'createTime': serializer.toJson<int>(createTime),
      'size': serializer.toJson<int>(size),
      'width': serializer.toJson<int>(width),
      'height': serializer.toJson<int>(height),
      'orientation': serializer.toJson<int>(orientation),
      'iccType': serializer.toJson<int>(iccType),
      'isRaw': serializer.toJson<bool>(isRaw),
      'rawPath': serializer.toJson<String>(rawPath),
      'converted': serializer.toJson<bool>(converted),
      'workspaceId': serializer.toJson<String>(workspaceId),
      'fileName': serializer.toJson<String?>(fileName),
      'iconized': serializer.toJson<bool?>(iconized),
      'midIconized': serializer.toJson<bool?>(midIconized),
      'captureTime': serializer.toJson<int?>(captureTime),
      'isOverSize': serializer.toJson<bool?>(isOverSize),
      'faceCount': serializer.toJson<int?>(faceCount),
      'binFormat': serializer.toJson<String?>(binFormat),
      'rawAutoExpose': serializer.toJson<bool?>(rawAutoExpose),
      'rawAutoAdjustType': serializer.toJson<int?>(rawAutoAdjustType),
      'isDeleted': serializer.toJson<bool?>(isDeleted),
    };
  }

  WorkspaceFileEntityData copyWith(
          {String? fileId,
          bool? edited,
          int? stars,
          bool? exported,
          String? orgPath,
          int? exportTime,
          String? format,
          bool? broken,
          int? lastEditTime,
          int? createTime,
          int? size,
          int? width,
          int? height,
          int? orientation,
          int? iccType,
          bool? isRaw,
          String? rawPath,
          bool? converted,
          String? workspaceId,
          Value<String?> fileName = const Value.absent(),
          Value<bool?> iconized = const Value.absent(),
          Value<bool?> midIconized = const Value.absent(),
          Value<int?> captureTime = const Value.absent(),
          Value<bool?> isOverSize = const Value.absent(),
          Value<int?> faceCount = const Value.absent(),
          Value<String?> binFormat = const Value.absent(),
          Value<bool?> rawAutoExpose = const Value.absent(),
          Value<int?> rawAutoAdjustType = const Value.absent(),
          Value<bool?> isDeleted = const Value.absent()}) =>
      WorkspaceFileEntityData(
        fileId: fileId ?? this.fileId,
        edited: edited ?? this.edited,
        stars: stars ?? this.stars,
        exported: exported ?? this.exported,
        orgPath: orgPath ?? this.orgPath,
        exportTime: exportTime ?? this.exportTime,
        format: format ?? this.format,
        broken: broken ?? this.broken,
        lastEditTime: lastEditTime ?? this.lastEditTime,
        createTime: createTime ?? this.createTime,
        size: size ?? this.size,
        width: width ?? this.width,
        height: height ?? this.height,
        orientation: orientation ?? this.orientation,
        iccType: iccType ?? this.iccType,
        isRaw: isRaw ?? this.isRaw,
        rawPath: rawPath ?? this.rawPath,
        converted: converted ?? this.converted,
        workspaceId: workspaceId ?? this.workspaceId,
        fileName: fileName.present ? fileName.value : this.fileName,
        iconized: iconized.present ? iconized.value : this.iconized,
        midIconized: midIconized.present ? midIconized.value : this.midIconized,
        captureTime: captureTime.present ? captureTime.value : this.captureTime,
        isOverSize: isOverSize.present ? isOverSize.value : this.isOverSize,
        faceCount: faceCount.present ? faceCount.value : this.faceCount,
        binFormat: binFormat.present ? binFormat.value : this.binFormat,
        rawAutoExpose:
            rawAutoExpose.present ? rawAutoExpose.value : this.rawAutoExpose,
        rawAutoAdjustType: rawAutoAdjustType.present
            ? rawAutoAdjustType.value
            : this.rawAutoAdjustType,
        isDeleted: isDeleted.present ? isDeleted.value : this.isDeleted,
      );
  WorkspaceFileEntityData copyWithCompanion(WorkspaceFileEntityCompanion data) {
    return WorkspaceFileEntityData(
      fileId: data.fileId.present ? data.fileId.value : this.fileId,
      edited: data.edited.present ? data.edited.value : this.edited,
      stars: data.stars.present ? data.stars.value : this.stars,
      exported: data.exported.present ? data.exported.value : this.exported,
      orgPath: data.orgPath.present ? data.orgPath.value : this.orgPath,
      exportTime:
          data.exportTime.present ? data.exportTime.value : this.exportTime,
      format: data.format.present ? data.format.value : this.format,
      broken: data.broken.present ? data.broken.value : this.broken,
      lastEditTime: data.lastEditTime.present
          ? data.lastEditTime.value
          : this.lastEditTime,
      createTime:
          data.createTime.present ? data.createTime.value : this.createTime,
      size: data.size.present ? data.size.value : this.size,
      width: data.width.present ? data.width.value : this.width,
      height: data.height.present ? data.height.value : this.height,
      orientation:
          data.orientation.present ? data.orientation.value : this.orientation,
      iccType: data.iccType.present ? data.iccType.value : this.iccType,
      isRaw: data.isRaw.present ? data.isRaw.value : this.isRaw,
      rawPath: data.rawPath.present ? data.rawPath.value : this.rawPath,
      converted: data.converted.present ? data.converted.value : this.converted,
      workspaceId:
          data.workspaceId.present ? data.workspaceId.value : this.workspaceId,
      fileName: data.fileName.present ? data.fileName.value : this.fileName,
      iconized: data.iconized.present ? data.iconized.value : this.iconized,
      midIconized:
          data.midIconized.present ? data.midIconized.value : this.midIconized,
      captureTime:
          data.captureTime.present ? data.captureTime.value : this.captureTime,
      isOverSize:
          data.isOverSize.present ? data.isOverSize.value : this.isOverSize,
      faceCount: data.faceCount.present ? data.faceCount.value : this.faceCount,
      binFormat: data.binFormat.present ? data.binFormat.value : this.binFormat,
      rawAutoExpose: data.rawAutoExpose.present
          ? data.rawAutoExpose.value
          : this.rawAutoExpose,
      rawAutoAdjustType: data.rawAutoAdjustType.present
          ? data.rawAutoAdjustType.value
          : this.rawAutoAdjustType,
      isDeleted: data.isDeleted.present ? data.isDeleted.value : this.isDeleted,
    );
  }

  @override
  String toString() {
    return (StringBuffer('WorkspaceFileEntityData(')
          ..write('fileId: $fileId, ')
          ..write('edited: $edited, ')
          ..write('stars: $stars, ')
          ..write('exported: $exported, ')
          ..write('orgPath: $orgPath, ')
          ..write('exportTime: $exportTime, ')
          ..write('format: $format, ')
          ..write('broken: $broken, ')
          ..write('lastEditTime: $lastEditTime, ')
          ..write('createTime: $createTime, ')
          ..write('size: $size, ')
          ..write('width: $width, ')
          ..write('height: $height, ')
          ..write('orientation: $orientation, ')
          ..write('iccType: $iccType, ')
          ..write('isRaw: $isRaw, ')
          ..write('rawPath: $rawPath, ')
          ..write('converted: $converted, ')
          ..write('workspaceId: $workspaceId, ')
          ..write('fileName: $fileName, ')
          ..write('iconized: $iconized, ')
          ..write('midIconized: $midIconized, ')
          ..write('captureTime: $captureTime, ')
          ..write('isOverSize: $isOverSize, ')
          ..write('faceCount: $faceCount, ')
          ..write('binFormat: $binFormat, ')
          ..write('rawAutoExpose: $rawAutoExpose, ')
          ..write('rawAutoAdjustType: $rawAutoAdjustType, ')
          ..write('isDeleted: $isDeleted')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hashAll([
        fileId,
        edited,
        stars,
        exported,
        orgPath,
        exportTime,
        format,
        broken,
        lastEditTime,
        createTime,
        size,
        width,
        height,
        orientation,
        iccType,
        isRaw,
        rawPath,
        converted,
        workspaceId,
        fileName,
        iconized,
        midIconized,
        captureTime,
        isOverSize,
        faceCount,
        binFormat,
        rawAutoExpose,
        rawAutoAdjustType,
        isDeleted
      ]);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is WorkspaceFileEntityData &&
          other.fileId == this.fileId &&
          other.edited == this.edited &&
          other.stars == this.stars &&
          other.exported == this.exported &&
          other.orgPath == this.orgPath &&
          other.exportTime == this.exportTime &&
          other.format == this.format &&
          other.broken == this.broken &&
          other.lastEditTime == this.lastEditTime &&
          other.createTime == this.createTime &&
          other.size == this.size &&
          other.width == this.width &&
          other.height == this.height &&
          other.orientation == this.orientation &&
          other.iccType == this.iccType &&
          other.isRaw == this.isRaw &&
          other.rawPath == this.rawPath &&
          other.converted == this.converted &&
          other.workspaceId == this.workspaceId &&
          other.fileName == this.fileName &&
          other.iconized == this.iconized &&
          other.midIconized == this.midIconized &&
          other.captureTime == this.captureTime &&
          other.isOverSize == this.isOverSize &&
          other.faceCount == this.faceCount &&
          other.binFormat == this.binFormat &&
          other.rawAutoExpose == this.rawAutoExpose &&
          other.rawAutoAdjustType == this.rawAutoAdjustType &&
          other.isDeleted == this.isDeleted);
}

class WorkspaceFileEntityCompanion
    extends UpdateCompanion<WorkspaceFileEntityData> {
  final Value<String> fileId;
  final Value<bool> edited;
  final Value<int> stars;
  final Value<bool> exported;
  final Value<String> orgPath;
  final Value<int> exportTime;
  final Value<String> format;
  final Value<bool> broken;
  final Value<int> lastEditTime;
  final Value<int> createTime;
  final Value<int> size;
  final Value<int> width;
  final Value<int> height;
  final Value<int> orientation;
  final Value<int> iccType;
  final Value<bool> isRaw;
  final Value<String> rawPath;
  final Value<bool> converted;
  final Value<String> workspaceId;
  final Value<String?> fileName;
  final Value<bool?> iconized;
  final Value<bool?> midIconized;
  final Value<int?> captureTime;
  final Value<bool?> isOverSize;
  final Value<int?> faceCount;
  final Value<String?> binFormat;
  final Value<bool?> rawAutoExpose;
  final Value<int?> rawAutoAdjustType;
  final Value<bool?> isDeleted;
  final Value<int> rowid;
  const WorkspaceFileEntityCompanion({
    this.fileId = const Value.absent(),
    this.edited = const Value.absent(),
    this.stars = const Value.absent(),
    this.exported = const Value.absent(),
    this.orgPath = const Value.absent(),
    this.exportTime = const Value.absent(),
    this.format = const Value.absent(),
    this.broken = const Value.absent(),
    this.lastEditTime = const Value.absent(),
    this.createTime = const Value.absent(),
    this.size = const Value.absent(),
    this.width = const Value.absent(),
    this.height = const Value.absent(),
    this.orientation = const Value.absent(),
    this.iccType = const Value.absent(),
    this.isRaw = const Value.absent(),
    this.rawPath = const Value.absent(),
    this.converted = const Value.absent(),
    this.workspaceId = const Value.absent(),
    this.fileName = const Value.absent(),
    this.iconized = const Value.absent(),
    this.midIconized = const Value.absent(),
    this.captureTime = const Value.absent(),
    this.isOverSize = const Value.absent(),
    this.faceCount = const Value.absent(),
    this.binFormat = const Value.absent(),
    this.rawAutoExpose = const Value.absent(),
    this.rawAutoAdjustType = const Value.absent(),
    this.isDeleted = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  WorkspaceFileEntityCompanion.insert({
    required String fileId,
    required bool edited,
    required int stars,
    required bool exported,
    required String orgPath,
    required int exportTime,
    required String format,
    required bool broken,
    required int lastEditTime,
    required int createTime,
    required int size,
    required int width,
    required int height,
    required int orientation,
    required int iccType,
    required bool isRaw,
    required String rawPath,
    required bool converted,
    required String workspaceId,
    this.fileName = const Value.absent(),
    this.iconized = const Value.absent(),
    this.midIconized = const Value.absent(),
    this.captureTime = const Value.absent(),
    this.isOverSize = const Value.absent(),
    this.faceCount = const Value.absent(),
    this.binFormat = const Value.absent(),
    this.rawAutoExpose = const Value.absent(),
    this.rawAutoAdjustType = const Value.absent(),
    this.isDeleted = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : fileId = Value(fileId),
        edited = Value(edited),
        stars = Value(stars),
        exported = Value(exported),
        orgPath = Value(orgPath),
        exportTime = Value(exportTime),
        format = Value(format),
        broken = Value(broken),
        lastEditTime = Value(lastEditTime),
        createTime = Value(createTime),
        size = Value(size),
        width = Value(width),
        height = Value(height),
        orientation = Value(orientation),
        iccType = Value(iccType),
        isRaw = Value(isRaw),
        rawPath = Value(rawPath),
        converted = Value(converted),
        workspaceId = Value(workspaceId);
  static Insertable<WorkspaceFileEntityData> custom({
    Expression<String>? fileId,
    Expression<bool>? edited,
    Expression<int>? stars,
    Expression<bool>? exported,
    Expression<String>? orgPath,
    Expression<int>? exportTime,
    Expression<String>? format,
    Expression<bool>? broken,
    Expression<int>? lastEditTime,
    Expression<int>? createTime,
    Expression<int>? size,
    Expression<int>? width,
    Expression<int>? height,
    Expression<int>? orientation,
    Expression<int>? iccType,
    Expression<bool>? isRaw,
    Expression<String>? rawPath,
    Expression<bool>? converted,
    Expression<String>? workspaceId,
    Expression<String>? fileName,
    Expression<bool>? iconized,
    Expression<bool>? midIconized,
    Expression<int>? captureTime,
    Expression<bool>? isOverSize,
    Expression<int>? faceCount,
    Expression<String>? binFormat,
    Expression<bool>? rawAutoExpose,
    Expression<int>? rawAutoAdjustType,
    Expression<bool>? isDeleted,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (fileId != null) 'file_id': fileId,
      if (edited != null) 'edited': edited,
      if (stars != null) 'stars': stars,
      if (exported != null) 'exported': exported,
      if (orgPath != null) 'org_path': orgPath,
      if (exportTime != null) 'export_time': exportTime,
      if (format != null) 'format': format,
      if (broken != null) 'broken': broken,
      if (lastEditTime != null) 'last_edit_time': lastEditTime,
      if (createTime != null) 'create_time': createTime,
      if (size != null) 'size': size,
      if (width != null) 'width': width,
      if (height != null) 'height': height,
      if (orientation != null) 'orientation': orientation,
      if (iccType != null) 'icc_type': iccType,
      if (isRaw != null) 'is_raw': isRaw,
      if (rawPath != null) 'raw_path': rawPath,
      if (converted != null) 'converted': converted,
      if (workspaceId != null) 'workspace_id': workspaceId,
      if (fileName != null) 'file_name': fileName,
      if (iconized != null) 'iconized': iconized,
      if (midIconized != null) 'mid_iconized': midIconized,
      if (captureTime != null) 'capture_time': captureTime,
      if (isOverSize != null) 'is_over_size': isOverSize,
      if (faceCount != null) 'face_count': faceCount,
      if (binFormat != null) 'bin_format': binFormat,
      if (rawAutoExpose != null) 'raw_auto_expose': rawAutoExpose,
      if (rawAutoAdjustType != null) 'raw_auto_adjust_type': rawAutoAdjustType,
      if (isDeleted != null) 'is_deleted': isDeleted,
      if (rowid != null) 'rowid': rowid,
    });
  }

  WorkspaceFileEntityCompanion copyWith(
      {Value<String>? fileId,
      Value<bool>? edited,
      Value<int>? stars,
      Value<bool>? exported,
      Value<String>? orgPath,
      Value<int>? exportTime,
      Value<String>? format,
      Value<bool>? broken,
      Value<int>? lastEditTime,
      Value<int>? createTime,
      Value<int>? size,
      Value<int>? width,
      Value<int>? height,
      Value<int>? orientation,
      Value<int>? iccType,
      Value<bool>? isRaw,
      Value<String>? rawPath,
      Value<bool>? converted,
      Value<String>? workspaceId,
      Value<String?>? fileName,
      Value<bool?>? iconized,
      Value<bool?>? midIconized,
      Value<int?>? captureTime,
      Value<bool?>? isOverSize,
      Value<int?>? faceCount,
      Value<String?>? binFormat,
      Value<bool?>? rawAutoExpose,
      Value<int?>? rawAutoAdjustType,
      Value<bool?>? isDeleted,
      Value<int>? rowid}) {
    return WorkspaceFileEntityCompanion(
      fileId: fileId ?? this.fileId,
      edited: edited ?? this.edited,
      stars: stars ?? this.stars,
      exported: exported ?? this.exported,
      orgPath: orgPath ?? this.orgPath,
      exportTime: exportTime ?? this.exportTime,
      format: format ?? this.format,
      broken: broken ?? this.broken,
      lastEditTime: lastEditTime ?? this.lastEditTime,
      createTime: createTime ?? this.createTime,
      size: size ?? this.size,
      width: width ?? this.width,
      height: height ?? this.height,
      orientation: orientation ?? this.orientation,
      iccType: iccType ?? this.iccType,
      isRaw: isRaw ?? this.isRaw,
      rawPath: rawPath ?? this.rawPath,
      converted: converted ?? this.converted,
      workspaceId: workspaceId ?? this.workspaceId,
      fileName: fileName ?? this.fileName,
      iconized: iconized ?? this.iconized,
      midIconized: midIconized ?? this.midIconized,
      captureTime: captureTime ?? this.captureTime,
      isOverSize: isOverSize ?? this.isOverSize,
      faceCount: faceCount ?? this.faceCount,
      binFormat: binFormat ?? this.binFormat,
      rawAutoExpose: rawAutoExpose ?? this.rawAutoExpose,
      rawAutoAdjustType: rawAutoAdjustType ?? this.rawAutoAdjustType,
      isDeleted: isDeleted ?? this.isDeleted,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (fileId.present) {
      map['file_id'] = Variable<String>(fileId.value);
    }
    if (edited.present) {
      map['edited'] = Variable<bool>(edited.value);
    }
    if (stars.present) {
      map['stars'] = Variable<int>(stars.value);
    }
    if (exported.present) {
      map['exported'] = Variable<bool>(exported.value);
    }
    if (orgPath.present) {
      map['org_path'] = Variable<String>(orgPath.value);
    }
    if (exportTime.present) {
      map['export_time'] = Variable<int>(exportTime.value);
    }
    if (format.present) {
      map['format'] = Variable<String>(format.value);
    }
    if (broken.present) {
      map['broken'] = Variable<bool>(broken.value);
    }
    if (lastEditTime.present) {
      map['last_edit_time'] = Variable<int>(lastEditTime.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<int>(createTime.value);
    }
    if (size.present) {
      map['size'] = Variable<int>(size.value);
    }
    if (width.present) {
      map['width'] = Variable<int>(width.value);
    }
    if (height.present) {
      map['height'] = Variable<int>(height.value);
    }
    if (orientation.present) {
      map['orientation'] = Variable<int>(orientation.value);
    }
    if (iccType.present) {
      map['icc_type'] = Variable<int>(iccType.value);
    }
    if (isRaw.present) {
      map['is_raw'] = Variable<bool>(isRaw.value);
    }
    if (rawPath.present) {
      map['raw_path'] = Variable<String>(rawPath.value);
    }
    if (converted.present) {
      map['converted'] = Variable<bool>(converted.value);
    }
    if (workspaceId.present) {
      map['workspace_id'] = Variable<String>(workspaceId.value);
    }
    if (fileName.present) {
      map['file_name'] = Variable<String>(fileName.value);
    }
    if (iconized.present) {
      map['iconized'] = Variable<bool>(iconized.value);
    }
    if (midIconized.present) {
      map['mid_iconized'] = Variable<bool>(midIconized.value);
    }
    if (captureTime.present) {
      map['capture_time'] = Variable<int>(captureTime.value);
    }
    if (isOverSize.present) {
      map['is_over_size'] = Variable<bool>(isOverSize.value);
    }
    if (faceCount.present) {
      map['face_count'] = Variable<int>(faceCount.value);
    }
    if (binFormat.present) {
      map['bin_format'] = Variable<String>(binFormat.value);
    }
    if (rawAutoExpose.present) {
      map['raw_auto_expose'] = Variable<bool>(rawAutoExpose.value);
    }
    if (rawAutoAdjustType.present) {
      map['raw_auto_adjust_type'] = Variable<int>(rawAutoAdjustType.value);
    }
    if (isDeleted.present) {
      map['is_deleted'] = Variable<bool>(isDeleted.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('WorkspaceFileEntityCompanion(')
          ..write('fileId: $fileId, ')
          ..write('edited: $edited, ')
          ..write('stars: $stars, ')
          ..write('exported: $exported, ')
          ..write('orgPath: $orgPath, ')
          ..write('exportTime: $exportTime, ')
          ..write('format: $format, ')
          ..write('broken: $broken, ')
          ..write('lastEditTime: $lastEditTime, ')
          ..write('createTime: $createTime, ')
          ..write('size: $size, ')
          ..write('width: $width, ')
          ..write('height: $height, ')
          ..write('orientation: $orientation, ')
          ..write('iccType: $iccType, ')
          ..write('isRaw: $isRaw, ')
          ..write('rawPath: $rawPath, ')
          ..write('converted: $converted, ')
          ..write('workspaceId: $workspaceId, ')
          ..write('fileName: $fileName, ')
          ..write('iconized: $iconized, ')
          ..write('midIconized: $midIconized, ')
          ..write('captureTime: $captureTime, ')
          ..write('isOverSize: $isOverSize, ')
          ..write('faceCount: $faceCount, ')
          ..write('binFormat: $binFormat, ')
          ..write('rawAutoExpose: $rawAutoExpose, ')
          ..write('rawAutoAdjustType: $rawAutoAdjustType, ')
          ..write('isDeleted: $isDeleted, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ExportTokenEntityTable extends ExportTokenEntity
    with TableInfo<$ExportTokenEntityTable, ExportTokenEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ExportTokenEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _imagePHashMeta =
      const VerificationMeta('imagePHash');
  @override
  late final GeneratedColumn<String> imagePHash = GeneratedColumn<String>(
      'image_p_hash', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _userIdMeta = const VerificationMeta('userId');
  @override
  late final GeneratedColumn<String> userId = GeneratedColumn<String>(
      'user_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _tokenIdMeta =
      const VerificationMeta('tokenId');
  @override
  late final GeneratedColumn<String> tokenId = GeneratedColumn<String>(
      'token_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _keyMeta = const VerificationMeta('key');
  @override
  late final GeneratedColumn<String> key = GeneratedColumn<String>(
      'key', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _expireAtMeta =
      const VerificationMeta('expireAt');
  @override
  late final GeneratedColumn<int> expireAt = GeneratedColumn<int>(
      'expire_at', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _imageNameMeta =
      const VerificationMeta('imageName');
  @override
  late final GeneratedColumn<String> imageName = GeneratedColumn<String>(
      'image_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  @override
  late final GeneratedColumn<int> createTime = GeneratedColumn<int>(
      'create_time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _exportTypeMeta =
      const VerificationMeta('exportType');
  @override
  late final GeneratedColumn<String> exportType = GeneratedColumn<String>(
      'export_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        imagePHash,
        userId,
        tokenId,
        key,
        expireAt,
        imageName,
        createTime,
        exportType
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'export_token_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<ExportTokenEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('image_p_hash')) {
      context.handle(
          _imagePHashMeta,
          imagePHash.isAcceptableOrUnknown(
              data['image_p_hash']!, _imagePHashMeta));
    } else if (isInserting) {
      context.missing(_imagePHashMeta);
    }
    if (data.containsKey('user_id')) {
      context.handle(_userIdMeta,
          userId.isAcceptableOrUnknown(data['user_id']!, _userIdMeta));
    } else if (isInserting) {
      context.missing(_userIdMeta);
    }
    if (data.containsKey('token_id')) {
      context.handle(_tokenIdMeta,
          tokenId.isAcceptableOrUnknown(data['token_id']!, _tokenIdMeta));
    } else if (isInserting) {
      context.missing(_tokenIdMeta);
    }
    if (data.containsKey('key')) {
      context.handle(
          _keyMeta, key.isAcceptableOrUnknown(data['key']!, _keyMeta));
    } else if (isInserting) {
      context.missing(_keyMeta);
    }
    if (data.containsKey('expire_at')) {
      context.handle(_expireAtMeta,
          expireAt.isAcceptableOrUnknown(data['expire_at']!, _expireAtMeta));
    } else if (isInserting) {
      context.missing(_expireAtMeta);
    }
    if (data.containsKey('image_name')) {
      context.handle(_imageNameMeta,
          imageName.isAcceptableOrUnknown(data['image_name']!, _imageNameMeta));
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    } else if (isInserting) {
      context.missing(_createTimeMeta);
    }
    if (data.containsKey('export_type')) {
      context.handle(
          _exportTypeMeta,
          exportType.isAcceptableOrUnknown(
              data['export_type']!, _exportTypeMeta));
    } else if (isInserting) {
      context.missing(_exportTypeMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {userId, imagePHash, exportType};
  @override
  ExportTokenEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ExportTokenEntityData(
      imagePHash: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}image_p_hash'])!,
      userId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}user_id'])!,
      tokenId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}token_id'])!,
      key: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}key'])!,
      expireAt: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}expire_at'])!,
      imageName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}image_name']),
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}create_time'])!,
      exportType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}export_type'])!,
    );
  }

  @override
  $ExportTokenEntityTable createAlias(String alias) {
    return $ExportTokenEntityTable(attachedDatabase, alias);
  }
}

class ExportTokenEntityData extends DataClass
    implements Insertable<ExportTokenEntityData> {
  final String imagePHash;
  final String userId;
  final String tokenId;
  final String key;
  final int expireAt;
  final String? imageName;
  final int createTime;
  final String exportType;
  const ExportTokenEntityData(
      {required this.imagePHash,
      required this.userId,
      required this.tokenId,
      required this.key,
      required this.expireAt,
      this.imageName,
      required this.createTime,
      required this.exportType});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['image_p_hash'] = Variable<String>(imagePHash);
    map['user_id'] = Variable<String>(userId);
    map['token_id'] = Variable<String>(tokenId);
    map['key'] = Variable<String>(key);
    map['expire_at'] = Variable<int>(expireAt);
    if (!nullToAbsent || imageName != null) {
      map['image_name'] = Variable<String>(imageName);
    }
    map['create_time'] = Variable<int>(createTime);
    map['export_type'] = Variable<String>(exportType);
    return map;
  }

  ExportTokenEntityCompanion toCompanion(bool nullToAbsent) {
    return ExportTokenEntityCompanion(
      imagePHash: Value(imagePHash),
      userId: Value(userId),
      tokenId: Value(tokenId),
      key: Value(key),
      expireAt: Value(expireAt),
      imageName: imageName == null && nullToAbsent
          ? const Value.absent()
          : Value(imageName),
      createTime: Value(createTime),
      exportType: Value(exportType),
    );
  }

  factory ExportTokenEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ExportTokenEntityData(
      imagePHash: serializer.fromJson<String>(json['imagePHash']),
      userId: serializer.fromJson<String>(json['userId']),
      tokenId: serializer.fromJson<String>(json['tokenId']),
      key: serializer.fromJson<String>(json['key']),
      expireAt: serializer.fromJson<int>(json['expireAt']),
      imageName: serializer.fromJson<String?>(json['imageName']),
      createTime: serializer.fromJson<int>(json['createTime']),
      exportType: serializer.fromJson<String>(json['exportType']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'imagePHash': serializer.toJson<String>(imagePHash),
      'userId': serializer.toJson<String>(userId),
      'tokenId': serializer.toJson<String>(tokenId),
      'key': serializer.toJson<String>(key),
      'expireAt': serializer.toJson<int>(expireAt),
      'imageName': serializer.toJson<String?>(imageName),
      'createTime': serializer.toJson<int>(createTime),
      'exportType': serializer.toJson<String>(exportType),
    };
  }

  ExportTokenEntityData copyWith(
          {String? imagePHash,
          String? userId,
          String? tokenId,
          String? key,
          int? expireAt,
          Value<String?> imageName = const Value.absent(),
          int? createTime,
          String? exportType}) =>
      ExportTokenEntityData(
        imagePHash: imagePHash ?? this.imagePHash,
        userId: userId ?? this.userId,
        tokenId: tokenId ?? this.tokenId,
        key: key ?? this.key,
        expireAt: expireAt ?? this.expireAt,
        imageName: imageName.present ? imageName.value : this.imageName,
        createTime: createTime ?? this.createTime,
        exportType: exportType ?? this.exportType,
      );
  ExportTokenEntityData copyWithCompanion(ExportTokenEntityCompanion data) {
    return ExportTokenEntityData(
      imagePHash:
          data.imagePHash.present ? data.imagePHash.value : this.imagePHash,
      userId: data.userId.present ? data.userId.value : this.userId,
      tokenId: data.tokenId.present ? data.tokenId.value : this.tokenId,
      key: data.key.present ? data.key.value : this.key,
      expireAt: data.expireAt.present ? data.expireAt.value : this.expireAt,
      imageName: data.imageName.present ? data.imageName.value : this.imageName,
      createTime:
          data.createTime.present ? data.createTime.value : this.createTime,
      exportType:
          data.exportType.present ? data.exportType.value : this.exportType,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ExportTokenEntityData(')
          ..write('imagePHash: $imagePHash, ')
          ..write('userId: $userId, ')
          ..write('tokenId: $tokenId, ')
          ..write('key: $key, ')
          ..write('expireAt: $expireAt, ')
          ..write('imageName: $imageName, ')
          ..write('createTime: $createTime, ')
          ..write('exportType: $exportType')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(imagePHash, userId, tokenId, key, expireAt,
      imageName, createTime, exportType);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ExportTokenEntityData &&
          other.imagePHash == this.imagePHash &&
          other.userId == this.userId &&
          other.tokenId == this.tokenId &&
          other.key == this.key &&
          other.expireAt == this.expireAt &&
          other.imageName == this.imageName &&
          other.createTime == this.createTime &&
          other.exportType == this.exportType);
}

class ExportTokenEntityCompanion
    extends UpdateCompanion<ExportTokenEntityData> {
  final Value<String> imagePHash;
  final Value<String> userId;
  final Value<String> tokenId;
  final Value<String> key;
  final Value<int> expireAt;
  final Value<String?> imageName;
  final Value<int> createTime;
  final Value<String> exportType;
  final Value<int> rowid;
  const ExportTokenEntityCompanion({
    this.imagePHash = const Value.absent(),
    this.userId = const Value.absent(),
    this.tokenId = const Value.absent(),
    this.key = const Value.absent(),
    this.expireAt = const Value.absent(),
    this.imageName = const Value.absent(),
    this.createTime = const Value.absent(),
    this.exportType = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ExportTokenEntityCompanion.insert({
    required String imagePHash,
    required String userId,
    required String tokenId,
    required String key,
    required int expireAt,
    this.imageName = const Value.absent(),
    required int createTime,
    required String exportType,
    this.rowid = const Value.absent(),
  })  : imagePHash = Value(imagePHash),
        userId = Value(userId),
        tokenId = Value(tokenId),
        key = Value(key),
        expireAt = Value(expireAt),
        createTime = Value(createTime),
        exportType = Value(exportType);
  static Insertable<ExportTokenEntityData> custom({
    Expression<String>? imagePHash,
    Expression<String>? userId,
    Expression<String>? tokenId,
    Expression<String>? key,
    Expression<int>? expireAt,
    Expression<String>? imageName,
    Expression<int>? createTime,
    Expression<String>? exportType,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (imagePHash != null) 'image_p_hash': imagePHash,
      if (userId != null) 'user_id': userId,
      if (tokenId != null) 'token_id': tokenId,
      if (key != null) 'key': key,
      if (expireAt != null) 'expire_at': expireAt,
      if (imageName != null) 'image_name': imageName,
      if (createTime != null) 'create_time': createTime,
      if (exportType != null) 'export_type': exportType,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ExportTokenEntityCompanion copyWith(
      {Value<String>? imagePHash,
      Value<String>? userId,
      Value<String>? tokenId,
      Value<String>? key,
      Value<int>? expireAt,
      Value<String?>? imageName,
      Value<int>? createTime,
      Value<String>? exportType,
      Value<int>? rowid}) {
    return ExportTokenEntityCompanion(
      imagePHash: imagePHash ?? this.imagePHash,
      userId: userId ?? this.userId,
      tokenId: tokenId ?? this.tokenId,
      key: key ?? this.key,
      expireAt: expireAt ?? this.expireAt,
      imageName: imageName ?? this.imageName,
      createTime: createTime ?? this.createTime,
      exportType: exportType ?? this.exportType,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (imagePHash.present) {
      map['image_p_hash'] = Variable<String>(imagePHash.value);
    }
    if (userId.present) {
      map['user_id'] = Variable<String>(userId.value);
    }
    if (tokenId.present) {
      map['token_id'] = Variable<String>(tokenId.value);
    }
    if (key.present) {
      map['key'] = Variable<String>(key.value);
    }
    if (expireAt.present) {
      map['expire_at'] = Variable<int>(expireAt.value);
    }
    if (imageName.present) {
      map['image_name'] = Variable<String>(imageName.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<int>(createTime.value);
    }
    if (exportType.present) {
      map['export_type'] = Variable<String>(exportType.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ExportTokenEntityCompanion(')
          ..write('imagePHash: $imagePHash, ')
          ..write('userId: $userId, ')
          ..write('tokenId: $tokenId, ')
          ..write('key: $key, ')
          ..write('expireAt: $expireAt, ')
          ..write('imageName: $imageName, ')
          ..write('createTime: $createTime, ')
          ..write('exportType: $exportType, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $CreatorInfoEntityTable extends CreatorInfoEntity
    with TableInfo<$CreatorInfoEntityTable, CreatorInfoEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CreatorInfoEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _uidMeta = const VerificationMeta('uid');
  @override
  late final GeneratedColumn<String> uid = GeneratedColumn<String>(
      'uid', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _creatorMobileMeta =
      const VerificationMeta('creatorMobile');
  @override
  late final GeneratedColumn<String> creatorMobile = GeneratedColumn<String>(
      'creator_mobile', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant(''));
  @override
  List<GeneratedColumn> get $columns => [uid, creatorMobile];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'creator_info_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<CreatorInfoEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('uid')) {
      context.handle(
          _uidMeta, uid.isAcceptableOrUnknown(data['uid']!, _uidMeta));
    } else if (isInserting) {
      context.missing(_uidMeta);
    }
    if (data.containsKey('creator_mobile')) {
      context.handle(
          _creatorMobileMeta,
          creatorMobile.isAcceptableOrUnknown(
              data['creator_mobile']!, _creatorMobileMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {uid};
  @override
  CreatorInfoEntityData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return CreatorInfoEntityData(
      uid: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}uid'])!,
      creatorMobile: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}creator_mobile'])!,
    );
  }

  @override
  $CreatorInfoEntityTable createAlias(String alias) {
    return $CreatorInfoEntityTable(attachedDatabase, alias);
  }
}

class CreatorInfoEntityData extends DataClass
    implements Insertable<CreatorInfoEntityData> {
  final String uid;
  final String creatorMobile;
  const CreatorInfoEntityData({required this.uid, required this.creatorMobile});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['uid'] = Variable<String>(uid);
    map['creator_mobile'] = Variable<String>(creatorMobile);
    return map;
  }

  CreatorInfoEntityCompanion toCompanion(bool nullToAbsent) {
    return CreatorInfoEntityCompanion(
      uid: Value(uid),
      creatorMobile: Value(creatorMobile),
    );
  }

  factory CreatorInfoEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return CreatorInfoEntityData(
      uid: serializer.fromJson<String>(json['uid']),
      creatorMobile: serializer.fromJson<String>(json['creatorMobile']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'uid': serializer.toJson<String>(uid),
      'creatorMobile': serializer.toJson<String>(creatorMobile),
    };
  }

  CreatorInfoEntityData copyWith({String? uid, String? creatorMobile}) =>
      CreatorInfoEntityData(
        uid: uid ?? this.uid,
        creatorMobile: creatorMobile ?? this.creatorMobile,
      );
  CreatorInfoEntityData copyWithCompanion(CreatorInfoEntityCompanion data) {
    return CreatorInfoEntityData(
      uid: data.uid.present ? data.uid.value : this.uid,
      creatorMobile: data.creatorMobile.present
          ? data.creatorMobile.value
          : this.creatorMobile,
    );
  }

  @override
  String toString() {
    return (StringBuffer('CreatorInfoEntityData(')
          ..write('uid: $uid, ')
          ..write('creatorMobile: $creatorMobile')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(uid, creatorMobile);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is CreatorInfoEntityData &&
          other.uid == this.uid &&
          other.creatorMobile == this.creatorMobile);
}

class CreatorInfoEntityCompanion
    extends UpdateCompanion<CreatorInfoEntityData> {
  final Value<String> uid;
  final Value<String> creatorMobile;
  final Value<int> rowid;
  const CreatorInfoEntityCompanion({
    this.uid = const Value.absent(),
    this.creatorMobile = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  CreatorInfoEntityCompanion.insert({
    required String uid,
    this.creatorMobile = const Value.absent(),
    this.rowid = const Value.absent(),
  }) : uid = Value(uid);
  static Insertable<CreatorInfoEntityData> custom({
    Expression<String>? uid,
    Expression<String>? creatorMobile,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (uid != null) 'uid': uid,
      if (creatorMobile != null) 'creator_mobile': creatorMobile,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CreatorInfoEntityCompanion copyWith(
      {Value<String>? uid, Value<String>? creatorMobile, Value<int>? rowid}) {
    return CreatorInfoEntityCompanion(
      uid: uid ?? this.uid,
      creatorMobile: creatorMobile ?? this.creatorMobile,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (uid.present) {
      map['uid'] = Variable<String>(uid.value);
    }
    if (creatorMobile.present) {
      map['creator_mobile'] = Variable<String>(creatorMobile.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CreatorInfoEntityCompanion(')
          ..write('uid: $uid, ')
          ..write('creatorMobile: $creatorMobile, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $FileOperationHistoryEntityTable extends FileOperationHistoryEntity
    with
        TableInfo<$FileOperationHistoryEntityTable,
            FileOperationHistoryEntityData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $FileOperationHistoryEntityTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _fileIdMeta = const VerificationMeta('fileId');
  @override
  late final GeneratedColumn<String> fileId = GeneratedColumn<String>(
      'file_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _createTimeMeta =
      const VerificationMeta('createTime');
  @override
  late final GeneratedColumn<int> createTime = GeneratedColumn<int>(
      'create_time', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _extraDataMeta =
      const VerificationMeta('extraData');
  @override
  late final GeneratedColumn<String> extraData = GeneratedColumn<String>(
      'extra_data', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [id, fileId, createTime, extraData];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'file_operation_history_entity';
  @override
  VerificationContext validateIntegrity(
      Insertable<FileOperationHistoryEntityData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('file_id')) {
      context.handle(_fileIdMeta,
          fileId.isAcceptableOrUnknown(data['file_id']!, _fileIdMeta));
    } else if (isInserting) {
      context.missing(_fileIdMeta);
    }
    if (data.containsKey('create_time')) {
      context.handle(
          _createTimeMeta,
          createTime.isAcceptableOrUnknown(
              data['create_time']!, _createTimeMeta));
    } else if (isInserting) {
      context.missing(_createTimeMeta);
    }
    if (data.containsKey('extra_data')) {
      context.handle(_extraDataMeta,
          extraData.isAcceptableOrUnknown(data['extra_data']!, _extraDataMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  FileOperationHistoryEntityData map(Map<String, dynamic> data,
      {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return FileOperationHistoryEntityData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      fileId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_id'])!,
      createTime: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}create_time'])!,
      extraData: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}extra_data']),
    );
  }

  @override
  $FileOperationHistoryEntityTable createAlias(String alias) {
    return $FileOperationHistoryEntityTable(attachedDatabase, alias);
  }
}

class FileOperationHistoryEntityData extends DataClass
    implements Insertable<FileOperationHistoryEntityData> {
  final int id;
  final String fileId;
  final int createTime;
  final String? extraData;
  const FileOperationHistoryEntityData(
      {required this.id,
      required this.fileId,
      required this.createTime,
      this.extraData});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['file_id'] = Variable<String>(fileId);
    map['create_time'] = Variable<int>(createTime);
    if (!nullToAbsent || extraData != null) {
      map['extra_data'] = Variable<String>(extraData);
    }
    return map;
  }

  FileOperationHistoryEntityCompanion toCompanion(bool nullToAbsent) {
    return FileOperationHistoryEntityCompanion(
      id: Value(id),
      fileId: Value(fileId),
      createTime: Value(createTime),
      extraData: extraData == null && nullToAbsent
          ? const Value.absent()
          : Value(extraData),
    );
  }

  factory FileOperationHistoryEntityData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return FileOperationHistoryEntityData(
      id: serializer.fromJson<int>(json['id']),
      fileId: serializer.fromJson<String>(json['fileId']),
      createTime: serializer.fromJson<int>(json['createTime']),
      extraData: serializer.fromJson<String?>(json['extraData']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'fileId': serializer.toJson<String>(fileId),
      'createTime': serializer.toJson<int>(createTime),
      'extraData': serializer.toJson<String?>(extraData),
    };
  }

  FileOperationHistoryEntityData copyWith(
          {int? id,
          String? fileId,
          int? createTime,
          Value<String?> extraData = const Value.absent()}) =>
      FileOperationHistoryEntityData(
        id: id ?? this.id,
        fileId: fileId ?? this.fileId,
        createTime: createTime ?? this.createTime,
        extraData: extraData.present ? extraData.value : this.extraData,
      );
  FileOperationHistoryEntityData copyWithCompanion(
      FileOperationHistoryEntityCompanion data) {
    return FileOperationHistoryEntityData(
      id: data.id.present ? data.id.value : this.id,
      fileId: data.fileId.present ? data.fileId.value : this.fileId,
      createTime:
          data.createTime.present ? data.createTime.value : this.createTime,
      extraData: data.extraData.present ? data.extraData.value : this.extraData,
    );
  }

  @override
  String toString() {
    return (StringBuffer('FileOperationHistoryEntityData(')
          ..write('id: $id, ')
          ..write('fileId: $fileId, ')
          ..write('createTime: $createTime, ')
          ..write('extraData: $extraData')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, fileId, createTime, extraData);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is FileOperationHistoryEntityData &&
          other.id == this.id &&
          other.fileId == this.fileId &&
          other.createTime == this.createTime &&
          other.extraData == this.extraData);
}

class FileOperationHistoryEntityCompanion
    extends UpdateCompanion<FileOperationHistoryEntityData> {
  final Value<int> id;
  final Value<String> fileId;
  final Value<int> createTime;
  final Value<String?> extraData;
  const FileOperationHistoryEntityCompanion({
    this.id = const Value.absent(),
    this.fileId = const Value.absent(),
    this.createTime = const Value.absent(),
    this.extraData = const Value.absent(),
  });
  FileOperationHistoryEntityCompanion.insert({
    this.id = const Value.absent(),
    required String fileId,
    required int createTime,
    this.extraData = const Value.absent(),
  })  : fileId = Value(fileId),
        createTime = Value(createTime);
  static Insertable<FileOperationHistoryEntityData> custom({
    Expression<int>? id,
    Expression<String>? fileId,
    Expression<int>? createTime,
    Expression<String>? extraData,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (fileId != null) 'file_id': fileId,
      if (createTime != null) 'create_time': createTime,
      if (extraData != null) 'extra_data': extraData,
    });
  }

  FileOperationHistoryEntityCompanion copyWith(
      {Value<int>? id,
      Value<String>? fileId,
      Value<int>? createTime,
      Value<String?>? extraData}) {
    return FileOperationHistoryEntityCompanion(
      id: id ?? this.id,
      fileId: fileId ?? this.fileId,
      createTime: createTime ?? this.createTime,
      extraData: extraData ?? this.extraData,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (fileId.present) {
      map['file_id'] = Variable<String>(fileId.value);
    }
    if (createTime.present) {
      map['create_time'] = Variable<int>(createTime.value);
    }
    if (extraData.present) {
      map['extra_data'] = Variable<String>(extraData.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('FileOperationHistoryEntityCompanion(')
          ..write('id: $id, ')
          ..write('fileId: $fileId, ')
          ..write('createTime: $createTime, ')
          ..write('extraData: $extraData')
          ..write(')'))
        .toString();
  }
}

abstract class _$DataBase extends GeneratedDatabase {
  _$DataBase(QueryExecutor e) : super(e);
  $DataBaseManager get managers => $DataBaseManager(this);
  late final $ProjectEntityTable projectEntity = $ProjectEntityTable(this);
  late final $UserEntityTable userEntity = $UserEntityTable(this);
  late final $WorkspaceEntityTable workspaceEntity =
      $WorkspaceEntityTable(this);
  late final $WorkspaceFileEntityTable workspaceFileEntity =
      $WorkspaceFileEntityTable(this);
  late final $ExportTokenEntityTable exportTokenEntity =
      $ExportTokenEntityTable(this);
  late final $CreatorInfoEntityTable creatorInfoEntity =
      $CreatorInfoEntityTable(this);
  late final $FileOperationHistoryEntityTable fileOperationHistoryEntity =
      $FileOperationHistoryEntityTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        projectEntity,
        userEntity,
        workspaceEntity,
        workspaceFileEntity,
        exportTokenEntity,
        creatorInfoEntity,
        fileOperationHistoryEntity
      ];
}

typedef $$ProjectEntityTableCreateCompanionBuilder = ProjectEntityCompanion
    Function({
  required String name,
  required String projectId,
  required String version,
  required String author,
  Value<String?> coverImages,
  Value<String?> description,
  Value<DateTime?> createdDate,
  Value<DateTime?> updateDate,
  Value<int> projectType,
  Value<String> outputFolder,
  Value<int> exportFileType,
  Value<int> quality,
  Value<bool> isReplace,
  Value<bool> transferSRGB,
  Value<int?> workspaceVersion,
  Value<int> rowid,
});
typedef $$ProjectEntityTableUpdateCompanionBuilder = ProjectEntityCompanion
    Function({
  Value<String> name,
  Value<String> projectId,
  Value<String> version,
  Value<String> author,
  Value<String?> coverImages,
  Value<String?> description,
  Value<DateTime?> createdDate,
  Value<DateTime?> updateDate,
  Value<int> projectType,
  Value<String> outputFolder,
  Value<int> exportFileType,
  Value<int> quality,
  Value<bool> isReplace,
  Value<bool> transferSRGB,
  Value<int?> workspaceVersion,
  Value<int> rowid,
});

class $$ProjectEntityTableTableManager extends RootTableManager<
    _$DataBase,
    $ProjectEntityTable,
    ProjectEntityData,
    $$ProjectEntityTableFilterComposer,
    $$ProjectEntityTableOrderingComposer,
    $$ProjectEntityTableCreateCompanionBuilder,
    $$ProjectEntityTableUpdateCompanionBuilder> {
  $$ProjectEntityTableTableManager(_$DataBase db, $ProjectEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$ProjectEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$ProjectEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> name = const Value.absent(),
            Value<String> projectId = const Value.absent(),
            Value<String> version = const Value.absent(),
            Value<String> author = const Value.absent(),
            Value<String?> coverImages = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<DateTime?> createdDate = const Value.absent(),
            Value<DateTime?> updateDate = const Value.absent(),
            Value<int> projectType = const Value.absent(),
            Value<String> outputFolder = const Value.absent(),
            Value<int> exportFileType = const Value.absent(),
            Value<int> quality = const Value.absent(),
            Value<bool> isReplace = const Value.absent(),
            Value<bool> transferSRGB = const Value.absent(),
            Value<int?> workspaceVersion = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProjectEntityCompanion(
            name: name,
            projectId: projectId,
            version: version,
            author: author,
            coverImages: coverImages,
            description: description,
            createdDate: createdDate,
            updateDate: updateDate,
            projectType: projectType,
            outputFolder: outputFolder,
            exportFileType: exportFileType,
            quality: quality,
            isReplace: isReplace,
            transferSRGB: transferSRGB,
            workspaceVersion: workspaceVersion,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String name,
            required String projectId,
            required String version,
            required String author,
            Value<String?> coverImages = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<DateTime?> createdDate = const Value.absent(),
            Value<DateTime?> updateDate = const Value.absent(),
            Value<int> projectType = const Value.absent(),
            Value<String> outputFolder = const Value.absent(),
            Value<int> exportFileType = const Value.absent(),
            Value<int> quality = const Value.absent(),
            Value<bool> isReplace = const Value.absent(),
            Value<bool> transferSRGB = const Value.absent(),
            Value<int?> workspaceVersion = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProjectEntityCompanion.insert(
            name: name,
            projectId: projectId,
            version: version,
            author: author,
            coverImages: coverImages,
            description: description,
            createdDate: createdDate,
            updateDate: updateDate,
            projectType: projectType,
            outputFolder: outputFolder,
            exportFileType: exportFileType,
            quality: quality,
            isReplace: isReplace,
            transferSRGB: transferSRGB,
            workspaceVersion: workspaceVersion,
            rowid: rowid,
          ),
        ));
}

class $$ProjectEntityTableFilterComposer
    extends FilterComposer<_$DataBase, $ProjectEntityTable> {
  $$ProjectEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get name => $state.composableBuilder(
      column: $state.table.name,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get projectId => $state.composableBuilder(
      column: $state.table.projectId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get version => $state.composableBuilder(
      column: $state.table.version,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get author => $state.composableBuilder(
      column: $state.table.author,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get coverImages => $state.composableBuilder(
      column: $state.table.coverImages,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get description => $state.composableBuilder(
      column: $state.table.description,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get createdDate => $state.composableBuilder(
      column: $state.table.createdDate,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<DateTime> get updateDate => $state.composableBuilder(
      column: $state.table.updateDate,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get projectType => $state.composableBuilder(
      column: $state.table.projectType,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get outputFolder => $state.composableBuilder(
      column: $state.table.outputFolder,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get exportFileType => $state.composableBuilder(
      column: $state.table.exportFileType,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get quality => $state.composableBuilder(
      column: $state.table.quality,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isReplace => $state.composableBuilder(
      column: $state.table.isReplace,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get transferSRGB => $state.composableBuilder(
      column: $state.table.transferSRGB,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get workspaceVersion => $state.composableBuilder(
      column: $state.table.workspaceVersion,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ComposableFilter workspaceEntityRefs(
      ComposableFilter Function($$WorkspaceEntityTableFilterComposer f) f) {
    final $$WorkspaceEntityTableFilterComposer composer =
        $state.composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.projectId,
            referencedTable: $state.db.workspaceEntity,
            getReferencedColumn: (t) => t.projectId,
            builder: (joinBuilder, parentComposers) =>
                $$WorkspaceEntityTableFilterComposer(ComposerState($state.db,
                    $state.db.workspaceEntity, joinBuilder, parentComposers)));
    return f(composer);
  }
}

class $$ProjectEntityTableOrderingComposer
    extends OrderingComposer<_$DataBase, $ProjectEntityTable> {
  $$ProjectEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get name => $state.composableBuilder(
      column: $state.table.name,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get projectId => $state.composableBuilder(
      column: $state.table.projectId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get version => $state.composableBuilder(
      column: $state.table.version,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get author => $state.composableBuilder(
      column: $state.table.author,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get coverImages => $state.composableBuilder(
      column: $state.table.coverImages,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get description => $state.composableBuilder(
      column: $state.table.description,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get createdDate => $state.composableBuilder(
      column: $state.table.createdDate,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<DateTime> get updateDate => $state.composableBuilder(
      column: $state.table.updateDate,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get projectType => $state.composableBuilder(
      column: $state.table.projectType,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get outputFolder => $state.composableBuilder(
      column: $state.table.outputFolder,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get exportFileType => $state.composableBuilder(
      column: $state.table.exportFileType,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get quality => $state.composableBuilder(
      column: $state.table.quality,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isReplace => $state.composableBuilder(
      column: $state.table.isReplace,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get transferSRGB => $state.composableBuilder(
      column: $state.table.transferSRGB,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get workspaceVersion => $state.composableBuilder(
      column: $state.table.workspaceVersion,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$UserEntityTableCreateCompanionBuilder = UserEntityCompanion Function({
  required String id,
  required String username,
  required String uid,
  required String phoneNumber,
  required String token,
  required int tokenExpiredDate,
  required String tokenEnd,
  Value<int> firstLogin,
  Value<String> lastLoginTime,
  Value<String> regDateTime,
  Value<int> cc,
  Value<String> role,
  Value<String> used,
  Value<int> enable,
  Value<String> lastLoginStoreId,
  Value<int> rowid,
});
typedef $$UserEntityTableUpdateCompanionBuilder = UserEntityCompanion Function({
  Value<String> id,
  Value<String> username,
  Value<String> uid,
  Value<String> phoneNumber,
  Value<String> token,
  Value<int> tokenExpiredDate,
  Value<String> tokenEnd,
  Value<int> firstLogin,
  Value<String> lastLoginTime,
  Value<String> regDateTime,
  Value<int> cc,
  Value<String> role,
  Value<String> used,
  Value<int> enable,
  Value<String> lastLoginStoreId,
  Value<int> rowid,
});

class $$UserEntityTableTableManager extends RootTableManager<
    _$DataBase,
    $UserEntityTable,
    UserEntityData,
    $$UserEntityTableFilterComposer,
    $$UserEntityTableOrderingComposer,
    $$UserEntityTableCreateCompanionBuilder,
    $$UserEntityTableUpdateCompanionBuilder> {
  $$UserEntityTableTableManager(_$DataBase db, $UserEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$UserEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$UserEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> username = const Value.absent(),
            Value<String> uid = const Value.absent(),
            Value<String> phoneNumber = const Value.absent(),
            Value<String> token = const Value.absent(),
            Value<int> tokenExpiredDate = const Value.absent(),
            Value<String> tokenEnd = const Value.absent(),
            Value<int> firstLogin = const Value.absent(),
            Value<String> lastLoginTime = const Value.absent(),
            Value<String> regDateTime = const Value.absent(),
            Value<int> cc = const Value.absent(),
            Value<String> role = const Value.absent(),
            Value<String> used = const Value.absent(),
            Value<int> enable = const Value.absent(),
            Value<String> lastLoginStoreId = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UserEntityCompanion(
            id: id,
            username: username,
            uid: uid,
            phoneNumber: phoneNumber,
            token: token,
            tokenExpiredDate: tokenExpiredDate,
            tokenEnd: tokenEnd,
            firstLogin: firstLogin,
            lastLoginTime: lastLoginTime,
            regDateTime: regDateTime,
            cc: cc,
            role: role,
            used: used,
            enable: enable,
            lastLoginStoreId: lastLoginStoreId,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String username,
            required String uid,
            required String phoneNumber,
            required String token,
            required int tokenExpiredDate,
            required String tokenEnd,
            Value<int> firstLogin = const Value.absent(),
            Value<String> lastLoginTime = const Value.absent(),
            Value<String> regDateTime = const Value.absent(),
            Value<int> cc = const Value.absent(),
            Value<String> role = const Value.absent(),
            Value<String> used = const Value.absent(),
            Value<int> enable = const Value.absent(),
            Value<String> lastLoginStoreId = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UserEntityCompanion.insert(
            id: id,
            username: username,
            uid: uid,
            phoneNumber: phoneNumber,
            token: token,
            tokenExpiredDate: tokenExpiredDate,
            tokenEnd: tokenEnd,
            firstLogin: firstLogin,
            lastLoginTime: lastLoginTime,
            regDateTime: regDateTime,
            cc: cc,
            role: role,
            used: used,
            enable: enable,
            lastLoginStoreId: lastLoginStoreId,
            rowid: rowid,
          ),
        ));
}

class $$UserEntityTableFilterComposer
    extends FilterComposer<_$DataBase, $UserEntityTable> {
  $$UserEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get username => $state.composableBuilder(
      column: $state.table.username,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get uid => $state.composableBuilder(
      column: $state.table.uid,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get phoneNumber => $state.composableBuilder(
      column: $state.table.phoneNumber,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get token => $state.composableBuilder(
      column: $state.table.token,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get tokenExpiredDate => $state.composableBuilder(
      column: $state.table.tokenExpiredDate,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get tokenEnd => $state.composableBuilder(
      column: $state.table.tokenEnd,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get firstLogin => $state.composableBuilder(
      column: $state.table.firstLogin,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get lastLoginTime => $state.composableBuilder(
      column: $state.table.lastLoginTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get regDateTime => $state.composableBuilder(
      column: $state.table.regDateTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get cc => $state.composableBuilder(
      column: $state.table.cc,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get role => $state.composableBuilder(
      column: $state.table.role,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get used => $state.composableBuilder(
      column: $state.table.used,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get enable => $state.composableBuilder(
      column: $state.table.enable,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get lastLoginStoreId => $state.composableBuilder(
      column: $state.table.lastLoginStoreId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$UserEntityTableOrderingComposer
    extends OrderingComposer<_$DataBase, $UserEntityTable> {
  $$UserEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get username => $state.composableBuilder(
      column: $state.table.username,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get uid => $state.composableBuilder(
      column: $state.table.uid,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get phoneNumber => $state.composableBuilder(
      column: $state.table.phoneNumber,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get token => $state.composableBuilder(
      column: $state.table.token,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get tokenExpiredDate => $state.composableBuilder(
      column: $state.table.tokenExpiredDate,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get tokenEnd => $state.composableBuilder(
      column: $state.table.tokenEnd,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get firstLogin => $state.composableBuilder(
      column: $state.table.firstLogin,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get lastLoginTime => $state.composableBuilder(
      column: $state.table.lastLoginTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get regDateTime => $state.composableBuilder(
      column: $state.table.regDateTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get cc => $state.composableBuilder(
      column: $state.table.cc,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get role => $state.composableBuilder(
      column: $state.table.role,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get used => $state.composableBuilder(
      column: $state.table.used,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get enable => $state.composableBuilder(
      column: $state.table.enable,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get lastLoginStoreId => $state.composableBuilder(
      column: $state.table.lastLoginStoreId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$WorkspaceEntityTableCreateCompanionBuilder = WorkspaceEntityCompanion
    Function({
  required String workspaceId,
  required String workspaceName,
  required String currentFileId,
  required int createTime,
  required int lastEditTime,
  required String projectId,
  Value<String?> filterValue,
  Value<String?> sortValue,
  Value<int> rowid,
});
typedef $$WorkspaceEntityTableUpdateCompanionBuilder = WorkspaceEntityCompanion
    Function({
  Value<String> workspaceId,
  Value<String> workspaceName,
  Value<String> currentFileId,
  Value<int> createTime,
  Value<int> lastEditTime,
  Value<String> projectId,
  Value<String?> filterValue,
  Value<String?> sortValue,
  Value<int> rowid,
});

class $$WorkspaceEntityTableTableManager extends RootTableManager<
    _$DataBase,
    $WorkspaceEntityTable,
    WorkspaceEntityData,
    $$WorkspaceEntityTableFilterComposer,
    $$WorkspaceEntityTableOrderingComposer,
    $$WorkspaceEntityTableCreateCompanionBuilder,
    $$WorkspaceEntityTableUpdateCompanionBuilder> {
  $$WorkspaceEntityTableTableManager(_$DataBase db, $WorkspaceEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$WorkspaceEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer:
              $$WorkspaceEntityTableOrderingComposer(ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> workspaceId = const Value.absent(),
            Value<String> workspaceName = const Value.absent(),
            Value<String> currentFileId = const Value.absent(),
            Value<int> createTime = const Value.absent(),
            Value<int> lastEditTime = const Value.absent(),
            Value<String> projectId = const Value.absent(),
            Value<String?> filterValue = const Value.absent(),
            Value<String?> sortValue = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              WorkspaceEntityCompanion(
            workspaceId: workspaceId,
            workspaceName: workspaceName,
            currentFileId: currentFileId,
            createTime: createTime,
            lastEditTime: lastEditTime,
            projectId: projectId,
            filterValue: filterValue,
            sortValue: sortValue,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String workspaceId,
            required String workspaceName,
            required String currentFileId,
            required int createTime,
            required int lastEditTime,
            required String projectId,
            Value<String?> filterValue = const Value.absent(),
            Value<String?> sortValue = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              WorkspaceEntityCompanion.insert(
            workspaceId: workspaceId,
            workspaceName: workspaceName,
            currentFileId: currentFileId,
            createTime: createTime,
            lastEditTime: lastEditTime,
            projectId: projectId,
            filterValue: filterValue,
            sortValue: sortValue,
            rowid: rowid,
          ),
        ));
}

class $$WorkspaceEntityTableFilterComposer
    extends FilterComposer<_$DataBase, $WorkspaceEntityTable> {
  $$WorkspaceEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get workspaceId => $state.composableBuilder(
      column: $state.table.workspaceId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get workspaceName => $state.composableBuilder(
      column: $state.table.workspaceName,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get currentFileId => $state.composableBuilder(
      column: $state.table.currentFileId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get createTime => $state.composableBuilder(
      column: $state.table.createTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get lastEditTime => $state.composableBuilder(
      column: $state.table.lastEditTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get filterValue => $state.composableBuilder(
      column: $state.table.filterValue,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get sortValue => $state.composableBuilder(
      column: $state.table.sortValue,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  $$ProjectEntityTableFilterComposer get projectId {
    final $$ProjectEntityTableFilterComposer composer = $state.composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.projectId,
        referencedTable: $state.db.projectEntity,
        getReferencedColumn: (t) => t.projectId,
        builder: (joinBuilder, parentComposers) =>
            $$ProjectEntityTableFilterComposer(ComposerState($state.db,
                $state.db.projectEntity, joinBuilder, parentComposers)));
    return composer;
  }

  ComposableFilter workspaceFileEntityRefs(
      ComposableFilter Function($$WorkspaceFileEntityTableFilterComposer f) f) {
    final $$WorkspaceFileEntityTableFilterComposer composer =
        $state.composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.workspaceId,
            referencedTable: $state.db.workspaceFileEntity,
            getReferencedColumn: (t) => t.workspaceId,
            builder: (joinBuilder, parentComposers) =>
                $$WorkspaceFileEntityTableFilterComposer(ComposerState(
                    $state.db,
                    $state.db.workspaceFileEntity,
                    joinBuilder,
                    parentComposers)));
    return f(composer);
  }
}

class $$WorkspaceEntityTableOrderingComposer
    extends OrderingComposer<_$DataBase, $WorkspaceEntityTable> {
  $$WorkspaceEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get workspaceId => $state.composableBuilder(
      column: $state.table.workspaceId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get workspaceName => $state.composableBuilder(
      column: $state.table.workspaceName,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get currentFileId => $state.composableBuilder(
      column: $state.table.currentFileId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get createTime => $state.composableBuilder(
      column: $state.table.createTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get lastEditTime => $state.composableBuilder(
      column: $state.table.lastEditTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get filterValue => $state.composableBuilder(
      column: $state.table.filterValue,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get sortValue => $state.composableBuilder(
      column: $state.table.sortValue,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  $$ProjectEntityTableOrderingComposer get projectId {
    final $$ProjectEntityTableOrderingComposer composer =
        $state.composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.projectId,
            referencedTable: $state.db.projectEntity,
            getReferencedColumn: (t) => t.projectId,
            builder: (joinBuilder, parentComposers) =>
                $$ProjectEntityTableOrderingComposer(ComposerState($state.db,
                    $state.db.projectEntity, joinBuilder, parentComposers)));
    return composer;
  }
}

typedef $$WorkspaceFileEntityTableCreateCompanionBuilder
    = WorkspaceFileEntityCompanion Function({
  required String fileId,
  required bool edited,
  required int stars,
  required bool exported,
  required String orgPath,
  required int exportTime,
  required String format,
  required bool broken,
  required int lastEditTime,
  required int createTime,
  required int size,
  required int width,
  required int height,
  required int orientation,
  required int iccType,
  required bool isRaw,
  required String rawPath,
  required bool converted,
  required String workspaceId,
  Value<String?> fileName,
  Value<bool?> iconized,
  Value<bool?> midIconized,
  Value<int?> captureTime,
  Value<bool?> isOverSize,
  Value<int?> faceCount,
  Value<String?> binFormat,
  Value<bool?> rawAutoExpose,
  Value<int?> rawAutoAdjustType,
  Value<bool?> isDeleted,
  Value<int> rowid,
});
typedef $$WorkspaceFileEntityTableUpdateCompanionBuilder
    = WorkspaceFileEntityCompanion Function({
  Value<String> fileId,
  Value<bool> edited,
  Value<int> stars,
  Value<bool> exported,
  Value<String> orgPath,
  Value<int> exportTime,
  Value<String> format,
  Value<bool> broken,
  Value<int> lastEditTime,
  Value<int> createTime,
  Value<int> size,
  Value<int> width,
  Value<int> height,
  Value<int> orientation,
  Value<int> iccType,
  Value<bool> isRaw,
  Value<String> rawPath,
  Value<bool> converted,
  Value<String> workspaceId,
  Value<String?> fileName,
  Value<bool?> iconized,
  Value<bool?> midIconized,
  Value<int?> captureTime,
  Value<bool?> isOverSize,
  Value<int?> faceCount,
  Value<String?> binFormat,
  Value<bool?> rawAutoExpose,
  Value<int?> rawAutoAdjustType,
  Value<bool?> isDeleted,
  Value<int> rowid,
});

class $$WorkspaceFileEntityTableTableManager extends RootTableManager<
    _$DataBase,
    $WorkspaceFileEntityTable,
    WorkspaceFileEntityData,
    $$WorkspaceFileEntityTableFilterComposer,
    $$WorkspaceFileEntityTableOrderingComposer,
    $$WorkspaceFileEntityTableCreateCompanionBuilder,
    $$WorkspaceFileEntityTableUpdateCompanionBuilder> {
  $$WorkspaceFileEntityTableTableManager(
      _$DataBase db, $WorkspaceFileEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$WorkspaceFileEntityTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$WorkspaceFileEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> fileId = const Value.absent(),
            Value<bool> edited = const Value.absent(),
            Value<int> stars = const Value.absent(),
            Value<bool> exported = const Value.absent(),
            Value<String> orgPath = const Value.absent(),
            Value<int> exportTime = const Value.absent(),
            Value<String> format = const Value.absent(),
            Value<bool> broken = const Value.absent(),
            Value<int> lastEditTime = const Value.absent(),
            Value<int> createTime = const Value.absent(),
            Value<int> size = const Value.absent(),
            Value<int> width = const Value.absent(),
            Value<int> height = const Value.absent(),
            Value<int> orientation = const Value.absent(),
            Value<int> iccType = const Value.absent(),
            Value<bool> isRaw = const Value.absent(),
            Value<String> rawPath = const Value.absent(),
            Value<bool> converted = const Value.absent(),
            Value<String> workspaceId = const Value.absent(),
            Value<String?> fileName = const Value.absent(),
            Value<bool?> iconized = const Value.absent(),
            Value<bool?> midIconized = const Value.absent(),
            Value<int?> captureTime = const Value.absent(),
            Value<bool?> isOverSize = const Value.absent(),
            Value<int?> faceCount = const Value.absent(),
            Value<String?> binFormat = const Value.absent(),
            Value<bool?> rawAutoExpose = const Value.absent(),
            Value<int?> rawAutoAdjustType = const Value.absent(),
            Value<bool?> isDeleted = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              WorkspaceFileEntityCompanion(
            fileId: fileId,
            edited: edited,
            stars: stars,
            exported: exported,
            orgPath: orgPath,
            exportTime: exportTime,
            format: format,
            broken: broken,
            lastEditTime: lastEditTime,
            createTime: createTime,
            size: size,
            width: width,
            height: height,
            orientation: orientation,
            iccType: iccType,
            isRaw: isRaw,
            rawPath: rawPath,
            converted: converted,
            workspaceId: workspaceId,
            fileName: fileName,
            iconized: iconized,
            midIconized: midIconized,
            captureTime: captureTime,
            isOverSize: isOverSize,
            faceCount: faceCount,
            binFormat: binFormat,
            rawAutoExpose: rawAutoExpose,
            rawAutoAdjustType: rawAutoAdjustType,
            isDeleted: isDeleted,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String fileId,
            required bool edited,
            required int stars,
            required bool exported,
            required String orgPath,
            required int exportTime,
            required String format,
            required bool broken,
            required int lastEditTime,
            required int createTime,
            required int size,
            required int width,
            required int height,
            required int orientation,
            required int iccType,
            required bool isRaw,
            required String rawPath,
            required bool converted,
            required String workspaceId,
            Value<String?> fileName = const Value.absent(),
            Value<bool?> iconized = const Value.absent(),
            Value<bool?> midIconized = const Value.absent(),
            Value<int?> captureTime = const Value.absent(),
            Value<bool?> isOverSize = const Value.absent(),
            Value<int?> faceCount = const Value.absent(),
            Value<String?> binFormat = const Value.absent(),
            Value<bool?> rawAutoExpose = const Value.absent(),
            Value<int?> rawAutoAdjustType = const Value.absent(),
            Value<bool?> isDeleted = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              WorkspaceFileEntityCompanion.insert(
            fileId: fileId,
            edited: edited,
            stars: stars,
            exported: exported,
            orgPath: orgPath,
            exportTime: exportTime,
            format: format,
            broken: broken,
            lastEditTime: lastEditTime,
            createTime: createTime,
            size: size,
            width: width,
            height: height,
            orientation: orientation,
            iccType: iccType,
            isRaw: isRaw,
            rawPath: rawPath,
            converted: converted,
            workspaceId: workspaceId,
            fileName: fileName,
            iconized: iconized,
            midIconized: midIconized,
            captureTime: captureTime,
            isOverSize: isOverSize,
            faceCount: faceCount,
            binFormat: binFormat,
            rawAutoExpose: rawAutoExpose,
            rawAutoAdjustType: rawAutoAdjustType,
            isDeleted: isDeleted,
            rowid: rowid,
          ),
        ));
}

class $$WorkspaceFileEntityTableFilterComposer
    extends FilterComposer<_$DataBase, $WorkspaceFileEntityTable> {
  $$WorkspaceFileEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get fileId => $state.composableBuilder(
      column: $state.table.fileId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get edited => $state.composableBuilder(
      column: $state.table.edited,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get stars => $state.composableBuilder(
      column: $state.table.stars,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get exported => $state.composableBuilder(
      column: $state.table.exported,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get orgPath => $state.composableBuilder(
      column: $state.table.orgPath,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get exportTime => $state.composableBuilder(
      column: $state.table.exportTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get format => $state.composableBuilder(
      column: $state.table.format,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get broken => $state.composableBuilder(
      column: $state.table.broken,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get lastEditTime => $state.composableBuilder(
      column: $state.table.lastEditTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get createTime => $state.composableBuilder(
      column: $state.table.createTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get size => $state.composableBuilder(
      column: $state.table.size,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get width => $state.composableBuilder(
      column: $state.table.width,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get height => $state.composableBuilder(
      column: $state.table.height,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get orientation => $state.composableBuilder(
      column: $state.table.orientation,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get iccType => $state.composableBuilder(
      column: $state.table.iccType,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isRaw => $state.composableBuilder(
      column: $state.table.isRaw,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get rawPath => $state.composableBuilder(
      column: $state.table.rawPath,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get converted => $state.composableBuilder(
      column: $state.table.converted,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get fileName => $state.composableBuilder(
      column: $state.table.fileName,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get iconized => $state.composableBuilder(
      column: $state.table.iconized,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get midIconized => $state.composableBuilder(
      column: $state.table.midIconized,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get captureTime => $state.composableBuilder(
      column: $state.table.captureTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isOverSize => $state.composableBuilder(
      column: $state.table.isOverSize,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get faceCount => $state.composableBuilder(
      column: $state.table.faceCount,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get binFormat => $state.composableBuilder(
      column: $state.table.binFormat,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get rawAutoExpose => $state.composableBuilder(
      column: $state.table.rawAutoExpose,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get rawAutoAdjustType => $state.composableBuilder(
      column: $state.table.rawAutoAdjustType,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<bool> get isDeleted => $state.composableBuilder(
      column: $state.table.isDeleted,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  $$WorkspaceEntityTableFilterComposer get workspaceId {
    final $$WorkspaceEntityTableFilterComposer composer =
        $state.composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.workspaceId,
            referencedTable: $state.db.workspaceEntity,
            getReferencedColumn: (t) => t.workspaceId,
            builder: (joinBuilder, parentComposers) =>
                $$WorkspaceEntityTableFilterComposer(ComposerState($state.db,
                    $state.db.workspaceEntity, joinBuilder, parentComposers)));
    return composer;
  }
}

class $$WorkspaceFileEntityTableOrderingComposer
    extends OrderingComposer<_$DataBase, $WorkspaceFileEntityTable> {
  $$WorkspaceFileEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get fileId => $state.composableBuilder(
      column: $state.table.fileId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get edited => $state.composableBuilder(
      column: $state.table.edited,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get stars => $state.composableBuilder(
      column: $state.table.stars,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get exported => $state.composableBuilder(
      column: $state.table.exported,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get orgPath => $state.composableBuilder(
      column: $state.table.orgPath,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get exportTime => $state.composableBuilder(
      column: $state.table.exportTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get format => $state.composableBuilder(
      column: $state.table.format,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get broken => $state.composableBuilder(
      column: $state.table.broken,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get lastEditTime => $state.composableBuilder(
      column: $state.table.lastEditTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get createTime => $state.composableBuilder(
      column: $state.table.createTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get size => $state.composableBuilder(
      column: $state.table.size,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get width => $state.composableBuilder(
      column: $state.table.width,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get height => $state.composableBuilder(
      column: $state.table.height,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get orientation => $state.composableBuilder(
      column: $state.table.orientation,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get iccType => $state.composableBuilder(
      column: $state.table.iccType,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isRaw => $state.composableBuilder(
      column: $state.table.isRaw,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get rawPath => $state.composableBuilder(
      column: $state.table.rawPath,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get converted => $state.composableBuilder(
      column: $state.table.converted,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get fileName => $state.composableBuilder(
      column: $state.table.fileName,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get iconized => $state.composableBuilder(
      column: $state.table.iconized,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get midIconized => $state.composableBuilder(
      column: $state.table.midIconized,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get captureTime => $state.composableBuilder(
      column: $state.table.captureTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isOverSize => $state.composableBuilder(
      column: $state.table.isOverSize,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get faceCount => $state.composableBuilder(
      column: $state.table.faceCount,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get binFormat => $state.composableBuilder(
      column: $state.table.binFormat,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get rawAutoExpose => $state.composableBuilder(
      column: $state.table.rawAutoExpose,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get rawAutoAdjustType => $state.composableBuilder(
      column: $state.table.rawAutoAdjustType,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<bool> get isDeleted => $state.composableBuilder(
      column: $state.table.isDeleted,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  $$WorkspaceEntityTableOrderingComposer get workspaceId {
    final $$WorkspaceEntityTableOrderingComposer composer =
        $state.composerBuilder(
            composer: this,
            getCurrentColumn: (t) => t.workspaceId,
            referencedTable: $state.db.workspaceEntity,
            getReferencedColumn: (t) => t.workspaceId,
            builder: (joinBuilder, parentComposers) =>
                $$WorkspaceEntityTableOrderingComposer(ComposerState($state.db,
                    $state.db.workspaceEntity, joinBuilder, parentComposers)));
    return composer;
  }
}

typedef $$ExportTokenEntityTableCreateCompanionBuilder
    = ExportTokenEntityCompanion Function({
  required String imagePHash,
  required String userId,
  required String tokenId,
  required String key,
  required int expireAt,
  Value<String?> imageName,
  required int createTime,
  required String exportType,
  Value<int> rowid,
});
typedef $$ExportTokenEntityTableUpdateCompanionBuilder
    = ExportTokenEntityCompanion Function({
  Value<String> imagePHash,
  Value<String> userId,
  Value<String> tokenId,
  Value<String> key,
  Value<int> expireAt,
  Value<String?> imageName,
  Value<int> createTime,
  Value<String> exportType,
  Value<int> rowid,
});

class $$ExportTokenEntityTableTableManager extends RootTableManager<
    _$DataBase,
    $ExportTokenEntityTable,
    ExportTokenEntityData,
    $$ExportTokenEntityTableFilterComposer,
    $$ExportTokenEntityTableOrderingComposer,
    $$ExportTokenEntityTableCreateCompanionBuilder,
    $$ExportTokenEntityTableUpdateCompanionBuilder> {
  $$ExportTokenEntityTableTableManager(
      _$DataBase db, $ExportTokenEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$ExportTokenEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer: $$ExportTokenEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> imagePHash = const Value.absent(),
            Value<String> userId = const Value.absent(),
            Value<String> tokenId = const Value.absent(),
            Value<String> key = const Value.absent(),
            Value<int> expireAt = const Value.absent(),
            Value<String?> imageName = const Value.absent(),
            Value<int> createTime = const Value.absent(),
            Value<String> exportType = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ExportTokenEntityCompanion(
            imagePHash: imagePHash,
            userId: userId,
            tokenId: tokenId,
            key: key,
            expireAt: expireAt,
            imageName: imageName,
            createTime: createTime,
            exportType: exportType,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String imagePHash,
            required String userId,
            required String tokenId,
            required String key,
            required int expireAt,
            Value<String?> imageName = const Value.absent(),
            required int createTime,
            required String exportType,
            Value<int> rowid = const Value.absent(),
          }) =>
              ExportTokenEntityCompanion.insert(
            imagePHash: imagePHash,
            userId: userId,
            tokenId: tokenId,
            key: key,
            expireAt: expireAt,
            imageName: imageName,
            createTime: createTime,
            exportType: exportType,
            rowid: rowid,
          ),
        ));
}

class $$ExportTokenEntityTableFilterComposer
    extends FilterComposer<_$DataBase, $ExportTokenEntityTable> {
  $$ExportTokenEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get imagePHash => $state.composableBuilder(
      column: $state.table.imagePHash,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get userId => $state.composableBuilder(
      column: $state.table.userId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get tokenId => $state.composableBuilder(
      column: $state.table.tokenId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get key => $state.composableBuilder(
      column: $state.table.key,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get expireAt => $state.composableBuilder(
      column: $state.table.expireAt,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get imageName => $state.composableBuilder(
      column: $state.table.imageName,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get createTime => $state.composableBuilder(
      column: $state.table.createTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get exportType => $state.composableBuilder(
      column: $state.table.exportType,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$ExportTokenEntityTableOrderingComposer
    extends OrderingComposer<_$DataBase, $ExportTokenEntityTable> {
  $$ExportTokenEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get imagePHash => $state.composableBuilder(
      column: $state.table.imagePHash,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get userId => $state.composableBuilder(
      column: $state.table.userId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get tokenId => $state.composableBuilder(
      column: $state.table.tokenId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get key => $state.composableBuilder(
      column: $state.table.key,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get expireAt => $state.composableBuilder(
      column: $state.table.expireAt,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get imageName => $state.composableBuilder(
      column: $state.table.imageName,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get createTime => $state.composableBuilder(
      column: $state.table.createTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get exportType => $state.composableBuilder(
      column: $state.table.exportType,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$CreatorInfoEntityTableCreateCompanionBuilder
    = CreatorInfoEntityCompanion Function({
  required String uid,
  Value<String> creatorMobile,
  Value<int> rowid,
});
typedef $$CreatorInfoEntityTableUpdateCompanionBuilder
    = CreatorInfoEntityCompanion Function({
  Value<String> uid,
  Value<String> creatorMobile,
  Value<int> rowid,
});

class $$CreatorInfoEntityTableTableManager extends RootTableManager<
    _$DataBase,
    $CreatorInfoEntityTable,
    CreatorInfoEntityData,
    $$CreatorInfoEntityTableFilterComposer,
    $$CreatorInfoEntityTableOrderingComposer,
    $$CreatorInfoEntityTableCreateCompanionBuilder,
    $$CreatorInfoEntityTableUpdateCompanionBuilder> {
  $$CreatorInfoEntityTableTableManager(
      _$DataBase db, $CreatorInfoEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer:
              $$CreatorInfoEntityTableFilterComposer(ComposerState(db, table)),
          orderingComposer: $$CreatorInfoEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<String> uid = const Value.absent(),
            Value<String> creatorMobile = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CreatorInfoEntityCompanion(
            uid: uid,
            creatorMobile: creatorMobile,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String uid,
            Value<String> creatorMobile = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              CreatorInfoEntityCompanion.insert(
            uid: uid,
            creatorMobile: creatorMobile,
            rowid: rowid,
          ),
        ));
}

class $$CreatorInfoEntityTableFilterComposer
    extends FilterComposer<_$DataBase, $CreatorInfoEntityTable> {
  $$CreatorInfoEntityTableFilterComposer(super.$state);
  ColumnFilters<String> get uid => $state.composableBuilder(
      column: $state.table.uid,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get creatorMobile => $state.composableBuilder(
      column: $state.table.creatorMobile,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$CreatorInfoEntityTableOrderingComposer
    extends OrderingComposer<_$DataBase, $CreatorInfoEntityTable> {
  $$CreatorInfoEntityTableOrderingComposer(super.$state);
  ColumnOrderings<String> get uid => $state.composableBuilder(
      column: $state.table.uid,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get creatorMobile => $state.composableBuilder(
      column: $state.table.creatorMobile,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

typedef $$FileOperationHistoryEntityTableCreateCompanionBuilder
    = FileOperationHistoryEntityCompanion Function({
  Value<int> id,
  required String fileId,
  required int createTime,
  Value<String?> extraData,
});
typedef $$FileOperationHistoryEntityTableUpdateCompanionBuilder
    = FileOperationHistoryEntityCompanion Function({
  Value<int> id,
  Value<String> fileId,
  Value<int> createTime,
  Value<String?> extraData,
});

class $$FileOperationHistoryEntityTableTableManager extends RootTableManager<
    _$DataBase,
    $FileOperationHistoryEntityTable,
    FileOperationHistoryEntityData,
    $$FileOperationHistoryEntityTableFilterComposer,
    $$FileOperationHistoryEntityTableOrderingComposer,
    $$FileOperationHistoryEntityTableCreateCompanionBuilder,
    $$FileOperationHistoryEntityTableUpdateCompanionBuilder> {
  $$FileOperationHistoryEntityTableTableManager(
      _$DataBase db, $FileOperationHistoryEntityTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          filteringComposer: $$FileOperationHistoryEntityTableFilterComposer(
              ComposerState(db, table)),
          orderingComposer: $$FileOperationHistoryEntityTableOrderingComposer(
              ComposerState(db, table)),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> fileId = const Value.absent(),
            Value<int> createTime = const Value.absent(),
            Value<String?> extraData = const Value.absent(),
          }) =>
              FileOperationHistoryEntityCompanion(
            id: id,
            fileId: fileId,
            createTime: createTime,
            extraData: extraData,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String fileId,
            required int createTime,
            Value<String?> extraData = const Value.absent(),
          }) =>
              FileOperationHistoryEntityCompanion.insert(
            id: id,
            fileId: fileId,
            createTime: createTime,
            extraData: extraData,
          ),
        ));
}

class $$FileOperationHistoryEntityTableFilterComposer
    extends FilterComposer<_$DataBase, $FileOperationHistoryEntityTable> {
  $$FileOperationHistoryEntityTableFilterComposer(super.$state);
  ColumnFilters<int> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get fileId => $state.composableBuilder(
      column: $state.table.fileId,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<int> get createTime => $state.composableBuilder(
      column: $state.table.createTime,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));

  ColumnFilters<String> get extraData => $state.composableBuilder(
      column: $state.table.extraData,
      builder: (column, joinBuilders) =>
          ColumnFilters(column, joinBuilders: joinBuilders));
}

class $$FileOperationHistoryEntityTableOrderingComposer
    extends OrderingComposer<_$DataBase, $FileOperationHistoryEntityTable> {
  $$FileOperationHistoryEntityTableOrderingComposer(super.$state);
  ColumnOrderings<int> get id => $state.composableBuilder(
      column: $state.table.id,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get fileId => $state.composableBuilder(
      column: $state.table.fileId,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<int> get createTime => $state.composableBuilder(
      column: $state.table.createTime,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));

  ColumnOrderings<String> get extraData => $state.composableBuilder(
      column: $state.table.extraData,
      builder: (column, joinBuilders) =>
          ColumnOrderings(column, joinBuilders: joinBuilders));
}

class $DataBaseManager {
  final _$DataBase _db;
  $DataBaseManager(this._db);
  $$ProjectEntityTableTableManager get projectEntity =>
      $$ProjectEntityTableTableManager(_db, _db.projectEntity);
  $$UserEntityTableTableManager get userEntity =>
      $$UserEntityTableTableManager(_db, _db.userEntity);
  $$WorkspaceEntityTableTableManager get workspaceEntity =>
      $$WorkspaceEntityTableTableManager(_db, _db.workspaceEntity);
  $$WorkspaceFileEntityTableTableManager get workspaceFileEntity =>
      $$WorkspaceFileEntityTableTableManager(_db, _db.workspaceFileEntity);
  $$ExportTokenEntityTableTableManager get exportTokenEntity =>
      $$ExportTokenEntityTableTableManager(_db, _db.exportTokenEntity);
  $$CreatorInfoEntityTableTableManager get creatorInfoEntity =>
      $$CreatorInfoEntityTableTableManager(_db, _db.creatorInfoEntity);
  $$FileOperationHistoryEntityTableTableManager
      get fileOperationHistoryEntity =>
          $$FileOperationHistoryEntityTableTableManager(
              _db, _db.fileOperationHistoryEntity);
}
