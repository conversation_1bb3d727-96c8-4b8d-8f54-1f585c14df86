import 'dart:async';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:turing_art/utils/pg_log.dart';

import 'generic_message.dart';
import 'task_processor_registry.dart';

/// 工作器初始化函数类型
/// 用于在Isolate中初始化特定的处理器
typedef WorkerInitializer = void Function();

/// 通用工作器通信通道
class WorkerChannel {
  final String workerId;
  final Isolate _isolate;
  final SendPort _sendToWorker;
  final ReceivePort _receiveFromWorker;
  final StreamController<dynamic> _messageController;

  bool _isIdle = true;

  WorkerChannel._({
    required this.workerId,
    required Isolate isolate,
    required SendPort sendToWorker,
    required ReceivePort receiveFromWorker,
  })  : _isolate = isolate,
        _sendToWorker = sendToWorker,
        _receiveFromWorker = receiveFromWorker,
        _messageController = StreamController<dynamic>.broadcast() {
    // 监听工作器发来的消息
    _receiveFromWorker.listen((message) {
      PGLog.d('📨 收到工作器 $workerId 消息: ${message.runtimeType}');
      if (message is GenericTaskResultMessage) {
        setIdle();
      }
      _messageController.add(message);
    });
  }

  /// 创建工作器通道
  /// [workerId] 工作器ID
  /// [initializer] 在Isolate中初始化处理器的函数
  static Future<WorkerChannel> create(
    String workerId, {
    WorkerInitializer? initializer,
  }) async {
    PGLog.d('🔧 创建工作器通道: $workerId');

    // 📡 第一步：创建初始化端口（用于"握手"）
    final initPort = ReceivePort();

    // 🚀 第二步：启动工作器Isolate，传递初始化参数
    final isolate = await Isolate.spawn(
      WorkerIsolate.main,
      WorkerInitData(
        mainSendPort: initPort.sendPort,
        initializer: initializer,
      ),
      debugName: workerId,
    );

    // 📨 第三步：等待工作器发送它的sendPort给我们
    final sendToWorker = await initPort.first as SendPort;
    initPort.close();

    // 📞 第四步：创建真正的通信端口（用于后续所有通信）
    final receiveFromWorker = ReceivePort();

    // 🏗️ 第五步：创建通道对象
    final channel = WorkerChannel._(
      workerId: workerId,
      isolate: isolate,
      sendToWorker: sendToWorker,
      receiveFromWorker: receiveFromWorker,
    );

    // 📤 第六步：告诉工作器我们的接收端口（完成双向通信）
    sendToWorker.send(receiveFromWorker.sendPort);

    PGLog.d('✅ 工作器通道 $workerId 创建完成');
    return channel;
  }

  /// 消息流
  Stream<dynamic> get messageStream => _messageController.stream;

  /// 是否空闲
  bool get isIdle => _isIdle;

  // 设置忙碌状态
  void setBusy() {
    _isIdle = false;
    PGLog.d('⚡ 工作器 $workerId 设为忙碌状态');
  }

  /// 设置空闲状态
  void setIdle() {
    _isIdle = true;
    PGLog.d('😴 工作器 $workerId 设为空闲状态');
  }

  /// 发送任务给工作器
  void sendTask(GenericTaskMessage<dynamic, dynamic> task) {
    PGLog.d('📤 发送任务给工作器 $workerId: ${task.taskId}');
    setBusy();
    _sendToWorker.send(task);
  }

  /// 关闭通道
  void dispose() {
    PGLog.d('🔒 关闭工作器通道: $workerId');
    _receiveFromWorker.close();
    _isolate.kill();
    _messageController.close();
  }
}

/// 工作器初始化数据
class WorkerInitData {
  final SendPort mainSendPort;
  final WorkerInitializer? initializer;

  WorkerInitData({
    required this.mainSendPort,
    this.initializer,
  });
}

/// 独立的工作器Isolate逻辑
class WorkerIsolate {
  static void main(WorkerInitData initData) {
    PGLog.d('🚀 工作器Isolate启动');

    // 🏭 第一步：初始化处理器（如果提供了初始化函数）
    if (initData.initializer != null) {
      PGLog.d('🔧 初始化工作器处理器...');
      initData.initializer!();
      PGLog.d(
          '✅ 工作器处理器初始化完成，注册了 ${TaskProcessorRegistry.registeredCount} 个处理器');
    }

    // 📞 第二步：创建工作器的接收端口
    final receivePort = ReceivePort();
    String workerId = 'worker_${DateTime.now().millisecondsSinceEpoch}';

    // 📤 第三步：发送我们的sendPort给主线程（第一次通信）
    initData.mainSendPort.send(receivePort.sendPort);

    SendPort? sendToMain;

    // 👂 第四步：监听主线程发来的消息
    receivePort.listen((message) {
      if (message is SendPort) {
        // 📨 第五步：接收主线程的sendPort（第二次通信）
        sendToMain = message;
        PGLog.d('🔗 工作器 $workerId 连接完成，等待任务分配');
      } else if (message is GenericTaskMessage) {
        // 🎯 处理主线程发来的任务
        PGLog.d('🎯 工作器 $workerId 开始处理任务: ${message.taskId}');
        _processTask(message, sendToMain!, workerId).then((_) {
          PGLog.d('✅ 工作器 $workerId 任务处理完成');
        }).catchError((e) {
          PGLog.d('❌ 工作器 $workerId 任务处理失败: $e');
        });
      }
    });
  }

  /// 处理单个任务
  static Future<void> _processTask(GenericTaskMessage<dynamic, dynamic> task,
      SendPort sendToMain, String workerId) async {
    final startTime = DateTime.now();
    // 从注册表获取对应的处理器
    final processor = TaskProcessorRegistry.getProcessor(task.processorKey);

    if (processor == null) {
      throw Exception('不支持的处理器类型: ${task.processorKey}');
    }

    // 使用处理器执行任务
    await processor.process(task, sendToMain, workerId);
    final processingTime = DateTime.now().difference(startTime);
    PGLog.d('🕒 工作器 $workerId 任务处理完成，耗时: $processingTime');
  }
}
