import 'package:turing_art/datalayer/domain/enums/payment_channel.dart';

import '../model/ops_custom_table.dart';

/// OpsCustomTable 数据仓库接口
abstract class OpsCustomTableRepository {
  /// 获取自定义表格数据
  Future<OpsCustomTable?> getCustomTable({required String code});

  /// 获取可用的支付渠道
  Future<List<PaymentChannel>> getAvailableChannels();

  /// 获取不需要展示企业微信礼包的店铺ID列表
  Future<List<String>> getNoWechatGiftStoreIds();

  /// 获取是否显示微信礼包入口
  Future<bool> getShowWechatGiftForAllStore();

  /// 获取AIGC用户手机号列表
  Future<List<String>> getAigcUserPhoneNumbers();

  /// 获取sampleKey字段
  Future<String?> getSampleKey();
}
