// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ops_custom_table.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OpsCustomTable _$OpsCustomTableFromJson(Map<String, dynamic> json) =>
    OpsCustomTable(
      betaConfig: json['betaConfig'] == null
          ? null
          : ExpiredTime.fromJson(json['betaConfig'] as Map<String, dynamic>),
      payChannel: (json['payChannel'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      noWechatGiftStoreIds: (json['noWechatGiftStoreIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      showWechatGiftForAllStore: json['showWechatGiftForAllStore'] as bool?,
      aigcUserPhoneNumbers: (json['aigcUserPhoneNumbers'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      sampleKey: json['sampleKey'] as String?,
    );

Map<String, dynamic> _$OpsCustomTableToJson(OpsCustomTable instance) =>
    <String, dynamic>{
      'betaConfig': instance.betaConfig,
      'payChannel': instance.payChannel,
      'noWechatGiftStoreIds': instance.noWechatGiftStoreIds,
      'showWechatGiftForAllStore': instance.showWechatGiftForAllStore,
      'aigcUserPhoneNumbers': instance.aigcUserPhoneNumbers,
      'sampleKey': instance.sampleKey,
    };
