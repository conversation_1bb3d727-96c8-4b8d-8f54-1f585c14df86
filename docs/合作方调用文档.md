# 图灵精修合作方调用文档

## 版本及更新日期
- **版本**：v0.2
- **更新日期**：2025.07.05
- **适用软件版本**：图灵精修 v1.8+

## 版本更新历史

### v0.2 (2025.07.05)
- **重要更新**：新增 `smart` 智能指令及相关功能
- **功能增强**：完善智能导入功能说明和使用指南
- **破坏性修改**：描述文件xxx_selected_files.json重命名为xxx_description.json

### v0.1 (2025.06.27)
- **初始版本**：完成基础的合作方调用接口文档
- **核心功能**：支持项目导入等基本功能
- **通信方式**：支持窗口消息和命令行参数两种通信方式

## 文档简介

本文档为第三方合作伙伴提供图灵精修应用的调用接口说明，包含应用启动检测、消息传递、参数配置等完整的集成方案。

**主要功能：**
- 检测并启动图灵精修应用
- 支持窗口消息和命令行参数两种通信方式
- 支持项目导入、智能导入等核心功能
- 智能导入功能：自动识别目录中的TAPJ文件，实现智能化导入
- 自动生成选版确认文件，便于选版方确认处理结果

**适用场景：**
- 第三方软件唤起图灵精修功能
- 选版/选片软件小样后确认选版信息
- 批量图片处理流程
- 自动化工作流集成

## 应用唤起及消息发送

### 标准调用流程

以下是检测和启动图灵精修应用的标准流程：

```mermaid
flowchart TD
    A["开始调用图灵精修"] --> B["检查 'turing_art' 窗口是否存在"]
    B --> C{"窗口存在?"}
    C -->|是| D["发送窗口消息"]
    C -->|否| E["尝试命令行启动"]
    E --> F["检查固定路径是否存在"]
    F --> G{"固定路径存在?"}
    G -->|是| H["直接调用并传递命令行参数"]
    G -->|否| I["通过注册表检查是否安装"]
    I --> J{"注册表存在?"}
    J -->|是| K["读取注册表安装地址"]
    K --> L["使用安装地址调用并传递参数"]
    J -->|否| M["报告异常：未安装图灵精修"]
    D --> N["调用成功"]
    H --> N
    L --> N
    M --> O["调用失败"]
```

### 实现细节说明

1. **窗口检测**：查找窗口标题为 "turing_art" 的进程窗口，使用 `FindWindowW(NULL, L"turing_art")`
2. **固定路径检查**：检查以下常见安装路径：
   - `C:\Program Files\图灵精修\`
   - `C:\图灵精修\`
   - `D:\Program Files\图灵精修\`
   - `D:\图灵精修\`
   - ...

3. **注册表检查**：依次检查以下注册表路径：
   - `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths`
   - `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`
   - `HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall`

## 消息/参数 传递

### 窗口消息

当图灵精修应用已经运行时，可以通过Windows消息机制进行通信。

#### 消息格式
使用 `WM_COPYDATA` 消息发送JSON格式的数据：

```json
{
  "type": "import_project",
  "data": {
    "tapj": "<tapj文件路径>",
    "newProject": false
  }
}
```

#### 支持的消息类型

| 消息类型 | 说明 | 必需参数 | 可选参数 |
|----------|------|----------|----------|
| `import_project` | 导入项目工程 | `tapj` - 项目文件路径 | `newProject` - 是否创建新工程 |
| `smart` | 智能导入图像 | `imagePaths` - 图像文件路径数组 | `projectName` - 项目名称, `autoNavigate` - 是否自动导航, `newProject` - 是否创建新工程 |

#### 窗口消息示例

##### 导入TAPJ项目
```json
{
  "type": "import_project",
  "data": {
    "tapj": "C:\\Users\\<USER>\\Downloads\\新建项目_2.tapj"
  }
}
```

##### 导入TAPJ项目并创建新工程
```json
{
  "type": "import_project",
  "data": {
    "tapj": "C:\\Users\\<USER>\\Downloads\\新建项目_2.tapj",
    "newProject": true
  }
}
```

或者使用正斜杠：
```json
{
  "type": "import_project",
  "data": {
    "tapj": "C:/Users/<USER>/Downloads/新建项目_2.tapj",
    "newProject": true
  }
}
```

##### 智能导入图像
```json
{
  "type": "smart",
  "data": {
    "imagePaths": [
      "C:\\Users\\<USER>\\Downloads\\images\\image1.jpg",
      "C:\\Users\\<USER>\\Downloads\\images\\image2.jpg"
    ],
    "projectName": "智能导入项目",
    "autoNavigate": true,
    "newProject": true
  }
}
```

### 命令行参数

当图灵精修应用未运行时，通过命令行参数启动应用并传递参数。

#### 基本语法
```bash
turing_art.exe [选项] [参数]
```

#### 支持的命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--tapj` | 导入TAPJ项目文件 | `--tapj=C:\project.tapj` |
| `--import` | 表明为导入功能 | `--import` |
| `--smart` | 智能导入图像（自动识别目录中的TAPJ） | `--smart` |
| `--images` | 指定图像文件路径（多个文件用\|分隔） | `--images=C:\img1.jpg\|C:\img2.jpg` |
| `--newProject` | 始终创建新工程（覆盖现有工程） | `--newProject` |

#### 命令行使用示例

##### 导入TAPJ项目
```bash
turing_art.exe --import --tapj=C:\Users\<USER>\Documents\Projects\我的项目.tapj
```

##### 导入TAPJ项目并创建新工程
```bash
turing_art.exe --import --tapj=C:\Users\<USER>\Documents\Projects\我的项目.tapj --newProject
```

##### 智能导入图像
```bash
turing_art.exe --smart --images=C:\Users\<USER>\Photos\photo1.jpg|C:\Users\<USER>\Photos\photo2.jpg
```

##### 智能导入图像并创建新工程
```bash
turing_art.exe --smart --images=C:\Users\<USER>\Photos\photo1.jpg|C:\Users\<USER>\Photos\photo2.jpg --newProject
```

#### 路径处理注意事项

**命令行参数路径：**
- 可以直接使用单个反斜杠 `\`
- 路径中包含空格时建议使用双引号包围
- 支持中文路径和文件名

**JSON消息路径：**
- 必须使用双反斜杠 `\\` 进行转义
- 或者使用正斜杠 `/` 代替反斜杠
- 路径字符串必须符合JSON格式要求

**示例：**
```bash
# 命令行参数格式
turing_art.exe --import --tapj=C:\Users\<USER>\Downloads\新建项目_2.tapj
# 创建新工程
turing_art.exe --import --tapj=C:\Users\<USER>\Downloads\新建项目_2.tapj --newProject
```

```json
// JSON消息格式 - 普通导入
{
  "type": "import_project",
  "data": {
    "tapj": "C:\\Users\\<USER>\\Downloads\\新建项目_2.tapj"
  }
}

// JSON消息格式 - 创建新工程
{
  "type": "import_project",  
  "data": {
    "tapj": "C:\\Users\\<USER>\\Downloads\\新建项目_2.tapj",
    "newProject": true
  }
}
```

## 选版文件格式说明

### _description.json 文件

图灵精修在导出TAPJ项目时会自动生成一个选版确认文件，文件名格式为 `项目名_description.json`，用于选版方确认选版照片的处理结果。

#### 文件命名规则
- **命名格式**：`{TAPJ项目名}_description.json`
- **生成时机**：TAPJ项目导出完成后自动生成
- **文件位置**：与TAPJ文件相同目录

#### 文件结构说明

选版文件为JSON数组格式，包含项目中所有图片的处理信息：

```json
[
    {
        "originalPath": "C:\\Users\\<USER>\\Downloads\\0526\\0526\\AH0A1633.CR2",
        "exportPath": "C:\\Users\\<USER>\\Downloads\\0526\\0526\\图灵精修\\AH0A1633_2.jpg",
        "isSelected": false
    },
    {
        "originalPath": "C:\\Users\\<USER>\\Downloads\\0526\\0526\\AH0A4826.CR2",
        "exportPath": "C:\\Users\\<USER>\\Downloads\\0526\\0526\\图灵精修\\AH0A4826_2.jpg",
        "isSelected": false
    },
    {
        "originalPath": "C:\\Users\\<USER>\\Downloads\\0526\\0526\\AH0A4989.CR2",
        "exportPath": "C:\\Users\\<USER>\\Downloads\\0526\\0526\\图灵精修\\AH0A4989_2.jpg",
        "isSelected": false
    }
]
```

#### 字段说明

| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `originalPath` | string | 原始文件的完整路径 | `C:\\Users\\<USER>\\Downloads\\0526\\0526\\AH0A1633.CR2` |
| `exportPath` | string | 导出处理后文件的完整路径 | `C:\\Users\\<USER>\\Downloads\\0526\\0526\\图灵精修\\AH0A1633_2.jpg` |
| `isSelected` | boolean | 该图片是否被选版方确认选中 | `false`（默认未选中） |

#### 使用场景

1. **选版确认**：选版方可以通过修改 `isSelected` 字段来标记确认的照片
2. **批量处理**：第三方软件可以读取此文件获取处理结果列表
3. **工作流集成**：基于选版结果进行后续的自动化处理

## 集成示例

### Windows C++ 实现

#### 1. 头文件包含和库链接

```cpp
#include <windows.h>
#include <string>
#include <shlwapi.h>
#include <winreg.h>
#include <iostream>

#pragma comment(lib, "shlwapi.lib")
```

#### 2. 窗口查找和消息发送

```cpp
// 查找窗口
bool FindWindowByTitle(const std::wstring& title, HWND& out_hwnd) {
    out_hwnd = FindWindowW(NULL, title.c_str());
    return out_hwnd != NULL;
}

// 发送消息到窗口
bool SendMessageToWindow(HWND hwnd, const std::string& message) {
    if (!hwnd) return false;

    // 准备 COPYDATASTRUCT
    COPYDATASTRUCT cds;
    cds.dwData = 1;  // 自定义标识符
    cds.cbData = static_cast<DWORD>(message.length() + 1);  // 包含空终止符
    cds.lpData = (void*)message.c_str();

    // 发送 WM_COPYDATA 消息
    LRESULT result = SendMessageW(hwnd, WM_COPYDATA, 0, (LPARAM)&cds);
    return result == 1;
}
```

#### 4. 检查安装并获取执行路径

```cpp
// appName传递 '图灵精修'
bool IsApplicationInstalled(const std::wstring& appName, std::wstring& installPath) {
    // First check common installation paths
    if (CheckCommonInstallPaths(appName, installPath)) {
        return true;
    }

    // If not found in common paths, check registry
    // registry paths
    const wchar_t* registryPaths[] = {
        L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\App Paths",
        L"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
        L"SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
    };

    for (const auto& basePath : registryPaths) {
        HKEY hKey;
        if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, basePath, 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            wchar_t subKeyName[256];
            DWORD index = 0;
            DWORD nameSize = sizeof(subKeyName) / sizeof(wchar_t);

            while (RegEnumKeyExW(hKey, index, subKeyName, &nameSize, NULL, NULL, NULL, NULL) == ERROR_SUCCESS) {
                HKEY hSubKey;
                std::wstring fullPath = std::wstring(basePath) + L"\\" + subKeyName;
                
                if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, fullPath.c_str(), 0, KEY_READ, &hSubKey) == ERROR_SUCCESS) {
                    wchar_t displayName[256];
                    DWORD dataSize = sizeof(displayName);
                    DWORD type = REG_SZ;

                    if (RegQueryValueExW(hSubKey, L"DisplayName", NULL, &type, (LPBYTE)displayName, &dataSize) == ERROR_SUCCESS) {
                        // convert both strings to lowercase for comparison
                        std::wstring displayNameLower = displayName;
                        std::wstring appNameLower = appName;
                        std::transform(displayNameLower.begin(), displayNameLower.end(), displayNameLower.begin(), ::towlower);
                        std::transform(appNameLower.begin(), appNameLower.end(), appNameLower.begin(), ::towlower);
                        
                        // use contains instead of equality
                        if (displayNameLower.find(appNameLower) != std::wstring::npos) {
                            // find the application and get the install path
                            wchar_t path[256];
                            dataSize = sizeof(path);
                            if (RegQueryValueExW(hSubKey, L"InstallLocation", NULL, &type, (LPBYTE)path, &dataSize) == ERROR_SUCCESS) {
                                installPath = path;
                                RegCloseKey(hSubKey);
                                RegCloseKey(hKey);
                                return true;
                            }
                        }
                    }
                    RegCloseKey(hSubKey);
                }
                nameSize = sizeof(subKeyName) / sizeof(wchar_t);
                index++;
            }
            RegCloseKey(hKey);
        }
    }
    return false;
}
```

#### 3. 完整的调用示例

```cpp
int main() {
    // 1. 检查图灵精修是否正在运行
    HWND target_hwnd;
    if (FindWindowByTitle(L"turing_art", target_hwnd)) {
                // 应用正在运行，发送消息
        std::string message = R"({
             "type": "import_project",
             "data": {
                 "tapj": "C:/Users/<USER>/Downloads/新建项目_2.tapj",
                 "newProject": true
             }
         })";
        
        if (SendMessageToWindow(target_hwnd, message)) {
            std::cout << "消息发送成功" << std::endl;
        } else {
            std::cout << "消息发送失败" << std::endl;
        }
    } else {
        // 应用未运行，尝试启动
        std::wstring installPath;
                 if (IsApplicationInstalled(L"图灵精修", installPath)) {
             std::wstring exePath = installPath + L"\\turing_art.exe";
             std::wstring arguments = L"--import --tapj=\"C:\\Users\\<USER>\\Downloads\\新建项目_2.tapj\" --newProject";
             
             if (LaunchApplication(exePath, arguments)) {
                std::cout << "应用启动成功" << std::endl;
            } else {
                std::cout << "应用启动失败" << std::endl;
            }
        } else {
            std::cout << "未找到图灵精修安装" << std::endl;
        }
    }
    
    return 0;
}
```

## 智能导入功能说明

### 功能特点

`--smart` 命令是图灵精修提供的智能导入功能，主要特点：

1. **自动识别TAPJ项目**：在指定的图像文件所在目录中查找TAPJ文件
2. **智能导入模式**：
   - 如果找到TAPJ文件，按现有项目导入模式处理
   - 如果没有找到TAPJ文件，自动创建新项目
3. **批量文件处理**：支持同时导入多个图像文件
4. **选版支持**：对于现有项目，自动将指定的图像文件标记为选中状态

### 使用场景

- **选版软件集成**：第三方选版软件可以通过此功能将选中的图像快速导入图灵精修
- **工作流自动化**：在后期处理流程中自动导入特定图像
- **批量处理**：一次性导入多个图像文件进行处理

### 智能导入逻辑

1. **检查目录**：扫描指定图像文件所在的目录
2. **查找TAPJ**：在目录中查找是否存在 `.tapj` 文件
3. **导入模式选择**：
   - 找到TAPJ → 按现有项目导入，将指定图像标记为选中
   - 未找到TAPJ → 创建新项目，导入所有指定图像

### 参数说明

```bash
# 基本语法
turing_art.exe --smart --images=图像路径1|图像路径2|...

# 可选参数
--newProject     # 强制创建新项目（忽略现有TAPJ）
--name=项目名称  # 指定项目名称（仅在创建新项目时有效）
```

### 示例

```bash
# 智能导入单个图像
turing_art.exe --smart --images=D:\Photos\IMG_001.jpg

# 智能导入多个图像
turing_art.exe --smart --images=D:\Photos\IMG_001.jpg|D:\Photos\IMG_002.jpg|D:\Photos\IMG_003.jpg

# 强制创建新项目
turing_art.exe --smart --images=D:\Photos\IMG_001.jpg --newProject --name=我的新项目
```

## 错误处理和调试

### 常见错误及解决方案

1. **窗口未找到**
   - 确认图灵精修应用正在运行
   - 检查窗口标题是否为 "turing_art"
   - 确认应用版本是否支持窗口消息

2. **消息发送失败**
   - 确认JSON格式正确
   - 检查文件路径是否存在
   - 确认路径转义格式正确

3. **应用启动失败**
   - 检查安装路径是否正确
   - 确认可执行文件存在
   - 检查命令行参数格式

**技术支持：** 如需更多技术支持，请联系图灵精修技术团队。